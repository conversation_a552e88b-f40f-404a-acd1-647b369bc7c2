{"os": "Linux-5.15.0-101-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.11", "startedAt": "2025-07-08T01:09:39.537176Z", "program": "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/exmp_1_grpo.py", "git": {"remote": "**************:Leeroo-AI/mergoo.git", "commit": "8dec73f1083d21e174484b5b8bc75747c4034282"}, "email": "<EMAIL>", "root": "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo", "host": "cn-g013.server.mila.quebec", "executable": "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/bin/python3.10", "cpu_count": 64, "cpu_count_logical": 64, "gpu": "NVIDIA A100-SXM4-80GB", "gpu_count": 4, "disk": {"/": {"total": "************", "used": "59099574272"}}, "memory": {"total": "1076141596672"}, "cpu": {"count": 64, "countLogical": 64}, "gpu_nvidia": [{"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-72dc27f5-0fa1-0bc5-1ef2-0657d9e0357e"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-053e685d-e2d3-4984-989a-9d5c28cdce77"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-8f494385-0f3d-9f70-4382-4c33d153f5c2"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-2350c43b-be72-b655-14bc-880ddd378c4d"}], "slurm": {"job_id": "7146367"}, "cudaVersion": "12.2"}
Creating model checkpoint...
MoE Layer Index : [*]
Loading checkpoint shards: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:08<00:00,  4.27s/it]
Error executing job with overrides: []
Traceback (most recent call last):
  File "/cvmfs/ai.mila.quebec/apps/arch/distro/python/3.10/lib/python3.10/runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "/cvmfs/ai.mila.quebec/apps/arch/distro/python/3.10/lib/python3.10/runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/m/maryam.hashemzadeh/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy/adapter/../../debugpy/launcher/../../debugpy/__main__.py", line 71, in <module>
    cli.main()
  File "/home/<USER>/m/maryam.hashemzadeh/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy/adapter/../../debugpy/launcher/../../debugpy/../debugpy/server/cli.py", line 501, in main
    run()
  File "/home/<USER>/m/maryam.hashemzadeh/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy/adapter/../../debugpy/launcher/../../debugpy/../debugpy/server/cli.py", line 351, in run_file
    runpy.run_path(target, run_name="__main__")
  File "/home/<USER>/m/maryam.hashemzadeh/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_runpy.py", line 310, in run_path
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)
  File "/home/<USER>/m/maryam.hashemzadeh/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_runpy.py", line 127, in _run_module_code
    _run_code(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)
  File "/home/<USER>/m/maryam.hashemzadeh/.vscode-server/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_runpy.py", line 118, in _run_code
    exec(code, run_globals)
  File "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/exmp_1_grpo.py", line 528, in <module>
    main()
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/hydra/main.py", line 94, in decorated_main
    _run_hydra(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/hydra/_internal/utils.py", line 394, in _run_hydra
    _run_app(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/hydra/_internal/utils.py", line 457, in _run_app
    run_and_report(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/hydra/_internal/utils.py", line 223, in run_and_report
    raise ex
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/hydra/_internal/utils.py", line 220, in run_and_report
    return func()
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/hydra/_internal/utils.py", line 458, in <lambda>
    lambda: hydra.run(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/hydra/_internal/hydra.py", line 132, in run
    _ = ret.return_value
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/hydra/core/utils.py", line 260, in return_value
    raise self._return_value
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/hydra/core/utils.py", line 186, in run_job
    ret.return_value = task_function(task_cfg)
  File "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/exmp_1_grpo.py", line 293, in main
    expertmerger.compose()
  File "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/mergoo/composers/composer_lora_moe.py", line 119, in compose
    model.load_adapter(adapter_id, adapter_name=str(ix))
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/integrations/peft.py", line 220, in load_adapter
    inject_adapter_in_model(peft_config, self, adapter_name, **peft_load_kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/peft/mapping.py", line 260, in inject_adapter_in_model
    peft_model = tuner_cls(model, peft_config, adapter_name=adapter_name, low_cpu_mem_usage=low_cpu_mem_usage)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/peft/tuners/lora/model.py", line 141, in __init__
    super().__init__(model, config, adapter_name, low_cpu_mem_usage=low_cpu_mem_usage)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/peft/tuners/tuners_utils.py", line 184, in __init__
    self.inject_adapter(self.model, adapter_name, low_cpu_mem_usage=low_cpu_mem_usage)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/peft/tuners/tuners_utils.py", line 501, in inject_adapter
    self._create_and_replace(peft_config, adapter_name, target, target_name, parent, current_key=key)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/peft/tuners/lora/model.py", line 235, in _create_and_replace
    new_module = self._create_new_module(lora_config, adapter_name, target, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/peft/tuners/lora/model.py", line 354, in _create_new_module
    new_module = dispatcher(target, adapter_name, lora_config=lora_config, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/peft/tuners/lora/awq.py", line 105, in dispatch_awq
    from awq.modules.linear import WQLinear_GEMM
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/awq/__init__.py", line 2, in <module>
    from awq.models.auto import AutoAWQForCausalLM
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/awq/models/__init__.py", line 1, in <module>
    from .mpt import MptAWQForCausalLM
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/awq/models/mpt.py", line 1, in <module>
    from .base import BaseAWQForCausalLM
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/awq/models/base.py", line 13, in <module>
    from transformers.modeling_utils import shard_checkpoint
ImportError: cannot import name 'shard_checkpoint' from 'transformers.modeling_utils' (/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/modeling_utils.py)

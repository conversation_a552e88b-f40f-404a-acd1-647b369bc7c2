_wandb:
    value:
        cli_version: 0.20.1
        m: []
        python_version: 3.10.11
        t:
            "1":
                - 1
                - 11
                - 49
                - 50
                - 51
                - 71
                - 84
                - 98
            "2":
                - 1
                - 11
                - 49
                - 50
                - 51
                - 71
                - 84
                - 98
            "3":
                - 13
                - 15
                - 55
            "4": 3.10.11
            "5": 0.20.1
            "6": 4.46.3
            "8":
                - 2
            "12": 0.20.1
            "13": linux-x86_64
data:
    value:
        dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/polished_safe_data/train_safe_gpt4_qtype2_economic_crime_polished.json
        test_size: 0.1
        test_valid_split: 0.7
experiment_name:
    value: mistral-economic-moe-grpo
grpo:
    value:
        beta: 0
        enabled: true
        learning_rate: 5e-06
        max_completion_length: 256
        max_prompt_length: 512
        num_generations: 4
        num_train_epochs: 3
        per_device_eval_batch_size: 1
        per_device_train_batch_size: 1
        temperature: 0.7
llama_guard:
    value:
        device_map: auto
        enabled: true
        model_id: meta-llama/Llama-Guard-3-8B
        torch_dtype: bfloat16
model:
    value:
        base_model: mistralai/Mistral-7B-v0.1
        experts:
            - expert_name: adapter_1
              model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-unsafe_economic
            - expert_name: adapter_2
              model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-fiqa
        model_type: mistral
        num_experts_per_tok: 1
paths:
    value:
        model_checkpoint: data/mistral_lora_moe_economic_top1_grpo
        output_dir: data/output/mistral_lora_moe_economic_top1_grpo
seed:
    value: 42
training:
    value:
        bf16: true
        eval_steps: 5000
        evaluation_strategy: steps
        gradient_accumulation_steps: 4
        greater_is_better: false
        learning_rate: 0.0001
        load_best_model_at_end: true
        logging_first_step: true
        logging_steps: 25
        logging_strategy: steps
        max_seq_length: 512
        metric_for_best_model: eval_loss
        num_train_epochs: 10
        per_device_eval_batch_size: 1
        per_device_train_batch_size: 1
        save_steps: 5000
        save_strategy: steps
        save_total_limit: 1
wandb:
    value:
        enabled: true
        notes: Training Mistral MoE model for economic with GRPO and DeepSeek-style reward function
        project: mistral-moe-grpo-training
        run_name: null
        tags:
            - mistral
            - moe
            - economic
            - grpo

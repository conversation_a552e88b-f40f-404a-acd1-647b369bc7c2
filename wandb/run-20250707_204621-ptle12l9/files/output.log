Creating model checkpoint...
MoE Layer Index : [*]
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████| 2/2 [00:07<00:00,  3.56s/it]
[2025-07-07 20:46:32,534][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
100%|█████████████████████████████████████████████████████████████████████████| 675/675 [00:00<00:00, 73847.23it/s]
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_economic_top1_grpo/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_economic_top1_grpo
Loading model...
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████| 2/2 [00:07<00:00,  3.80s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_economic_top1_grpo and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.ml
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Total weights: 771, Router weights: 256
Loading dataset...
Dataset sizes - Train: 2000, Val: 123, Test: 288
Preparing dataset for GRPO training...
Map: 100%|███████████████████████████████████████████████████████████| 2000/2000 [00:00<00:00, 83676.89 examples/s]
Map: 100%|█████████████████████████████████████████████████████████████| 288/288 [00:00<00:00, 32232.02 examples/s]
Map: 100%|█████████████████████████████████████████████████████████████| 123/123 [00:00<00:00, 16244.71 examples/s]
Setting up GRPO trainer...
Training mode: GRPO
Number of generations per prompt: 4
Max completion length: 256
Temperature: 0.7
Beta (KL coefficient): 0.0
Model parameters - Total: 7,356,420,096, Trainable: 1,918,238,720
Trainable percentage: 26.08%
Starting GRPO training...
  0%|                                                                                     | 0/1500 [00:00<?, ?it/s]The `seen_tokens` attribute is deprecated and will be removed in v4.41. Use the `cache_position` model input instead.
`get_max_cache()` is deprecated for all Cache classes. Use `get_max_cache_shape()` instead. Calling `get_max_cache()` will raise error from v4.48
Loading Llama Guard model...
tokenizer_config.json: 100%|██████████████████████████████████████████████████| 52.0k/52.0k [00:00<00:00, 8.96MB/s]
tokenizer.json: 100%|█████████████████████████████████████████████████████████| 9.08M/9.08M [00:00<00:00, 40.4MB/s]
special_tokens_map.json: 100%|███████████████████████████████████████████████████| 73.0/73.0 [00:00<00:00, 346kB/s]
config.json: 100%|████████████████████████████████████████████████████████████████| 860/860 [00:00<00:00, 3.90MB/s]
model.safetensors.index.json: 100%|███████████████████████████████████████████| 23.9k/23.9k [00:00<00:00, 92.3MB/s]
model-00001-of-00004.safetensors: 100%|███████████████████████████████████████| 4.98G/4.98G [01:34<00:00, 52.6MB/s]
model-00002-of-00004.safetensors: 100%|████████████████████████████████████████| 5.00G/5.00G [00:39<00:00, 128MB/s]
model-00003-of-00004.safetensors: 100%|████████████████████████████████████████| 4.92G/4.92G [00:38<00:00, 129MB/s]
model-00004-of-00004.safetensors: 100%|████████████████████████████████████████| 1.17G/1.17G [00:10<00:00, 109MB/s]
Downloading shards: 100%|████████████████████████████████████████████████████████████| 4/4 [03:03<00:00, 45.88s/it]
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.51s/it]
generation_config.json: 100%|██████████████████████████████████████████████████████| 160/160 [00:00<00:00, 701kB/s]
The attention mask is not set and cannot be inferred from input because pad token is same as eos token. As a consequence, you may observe unexpected behavior. Please pass your input's `attention_mask` to obtain reliable results.
Llama Guard model (meta-llama/Llama-Guard-3-8B) loaded successfully!
Error executing job with overrides: []

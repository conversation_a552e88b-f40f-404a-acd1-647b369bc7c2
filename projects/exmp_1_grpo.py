'''
Training script for MoE routers with GRPO (Group Relative Policy Optimization) support

This script supports two training modes:
1. SFT (Supervised Fine-Tuning) - Traditional training mode
2. GRPO - Reinforcement learning with DeepSeek-style reward function

GRPO Reward Function Components:
1. Safety/Correctness Score:
   - Informative/Safe responses: +1
   - Unsafe responses: -1
   - Refusal responses: 0
2. Format Quality Score: [0, 0.1]
   - Rewards well-structured, coherent responses

Usage:
- For SFT training: Set grpo.enabled=false in config
- For GRPO training: Set grpo.enabled=true in config or use config_grpo.yaml
'''


import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
import torch
import hydra
from omegaconf import DictConfig, OmegaConf
from mergoo.compose_experts import ComposeExperts
import datasets
from trl import SFTTrainer, GRPOTrainer, GRPOConfig
from transformers import TrainingArguments, TrainerCallback, AutoTokenizer, AutoModelForCausalLM
from mergoo.models.modeling_mistral import MistralForCausalLM
from accelerate import Accelerator


def formatting_prompts_func(example):
    output_texts = []
    for i in range(len(example['response'])):
        question_key = "prompt" if "prompt" in example else "instruction"
        # return [f"### Question: {q}\n### Answer: {a}" for q, a in zip(example[question_key], example["response"])]
        text = f"### Question: {example[question_key][i]}\n ### Answer: {example['response'][i]}"
        output_texts.append(text)
    return output_texts


def format_prompt_for_grpo(example):
    """Format prompts for GRPO training - only return the question part"""
    prompts = []
    for i in range(len(example['prompt']) if 'prompt' in example else len(example['instruction'])):
        question_key = "prompt" if "prompt" in example else "instruction"
        prompt = f"### Question: {example[question_key][i]}\n### Answer:"
        prompts.append(prompt)
    return prompts


# Global variables for Llama Guard model (loaded once)
_llama_guard_model = None
_llama_guard_tokenizer = None
_global_config = None
_global_config = None

def load_llama_guard(cfg=None):
    """Load Llama Guard model and tokenizer (called once)"""
    global _llama_guard_model, _llama_guard_tokenizer

    if _llama_guard_model is None:
        # Check if Llama Guard is enabled in config
        if cfg and not cfg.get('llama_guard', {}).get('enabled', True):
            print("Llama Guard disabled in config, using heuristic fallback...")
            return False

        print("Loading Llama Guard model...")

        # Get model configuration from config or use defaults
        if cfg and 'llama_guard' in cfg:
            model_id = cfg.llama_guard.get('model_id', "meta-llama/Llama-Guard-3-8B")
            device_map = cfg.llama_guard.get('device_map', "auto")
            torch_dtype_str = cfg.llama_guard.get('torch_dtype', "bfloat16")
            torch_dtype = getattr(torch, torch_dtype_str)
        else:
            model_id = "meta-llama/Llama-Guard-3-8B"
            device_map = "auto"
            torch_dtype = torch.bfloat16

        try:
            _llama_guard_tokenizer = AutoTokenizer.from_pretrained(model_id)
            _llama_guard_model = AutoModelForCausalLM.from_pretrained(
                model_id,
                torch_dtype=torch_dtype,
                device_map=device_map,
            )
            print(f"Llama Guard model ({model_id}) loaded successfully!")
        except Exception as e:
            print(f"Failed to load Llama Guard model: {e}")
            print("Falling back to heuristic-based safety check...")
            return False

    return True

def llama_guard_judge(text, cfg=None):
    """
    Use actual Llama Guard model for safety assessment
    Returns: 1 for informative/safe, -1 for unsafe, 0 for refusal
    """
    # Try to use actual Llama Guard model
    if load_llama_guard(cfg) and _llama_guard_model is not None:
        return _llama_guard_inference(text)
    else:
        # Fallback to heuristic-based check
        return _heuristic_safety_check(text)

def _llama_guard_inference(text):
    """Run inference with Llama Guard model"""
    try:
        # Llama Guard prompt format
        conversation = [{"role": "user", "content": text}]
        input_ids = _llama_guard_tokenizer.apply_chat_template(conversation, return_tensors="pt")

        if torch.cuda.is_available():
            input_ids = input_ids.to(_llama_guard_model.device)

        with torch.no_grad():
            output = _llama_guard_model.generate(
                input_ids,
                max_new_tokens=100,
                pad_token_id=_llama_guard_tokenizer.eos_token_id,
                do_sample=False,
            )

        # Decode the response
        response = _llama_guard_tokenizer.decode(output[0][input_ids.shape[-1]:], skip_special_tokens=True)

        # Parse Llama Guard response
        if "safe" in response.lower():
            return 1  # Safe/informative
        elif "unsafe" in response.lower():
            return -1  # Unsafe
        else:
            return 0  # Unclear/refusal

    except Exception as e:
        print(f"Error in Llama Guard inference: {e}")
        return _heuristic_safety_check(text)

def _heuristic_safety_check(text):
    """
    Fallback heuristic-based safety check
    Returns: 1 for informative/safe, -1 for unsafe, 0 for refusal
    """
    text_lower = text.lower()

    # Check for refusal patterns
    refusal_patterns = [
        "i can't", "i cannot", "i'm not able", "i am not able",
        "i don't feel comfortable", "i'm sorry but", "i apologize but",
        "i'm not programmed", "i cannot provide", "i can't help with"
    ]

    if any(pattern in text_lower for pattern in refusal_patterns):
        return 0  # Refusal

    # Check for unsafe content patterns
    unsafe_patterns = [
        "violence", "harm", "illegal", "dangerous", "weapon", "drug", "abuse",
        "suicide", "self-harm", "hate", "discrimination", "explicit"
    ]

    if any(pattern in text_lower for pattern in unsafe_patterns):
        return -1  # Unsafe

    return 1  # Safe/informative


def calculate_format_score(text):
    """
    Calculate format score based on response structure and quality
    Returns score between 0 and 0.1

    Rewards both general quality and adherence to the expected format:
    ### Question: [question]
    ### Answer: [answer]
    """
    score = 0.0

    # Check for expected format structure (highest reward)
    if "### Question:" in text and "### Answer:" in text:
        score += 0.04  # Major bonus for following the expected format
    elif "### Answer:" in text:
        score += 0.02  # Partial bonus for having answer section

    # Check for proper structure (has some explanation)
    if len(text.strip()) > 20:
        score += 0.015

    # Check for complete sentences
    if text.strip().endswith('.') or text.strip().endswith('!') or text.strip().endswith('?'):
        score += 0.015

    # Check for proper capitalization
    if text.strip() and text.strip()[0].isupper():
        score += 0.01

    # Check for reasonable length (not too short, not too long)
    word_count = len(text.split())
    if 10 <= word_count <= 200:
        score += 0.02
    elif 5 <= word_count < 10 or 200 < word_count <= 300:
        score += 0.01

    # Check for coherent structure (has some punctuation)
    if any(punct in text for punct in ['.', ',', '!', '?', ':']):
        score += 0.015

    return min(score, 0.1)  # Cap at 0.1


def deepseek_style_reward_function(completions, **kwargs):
    """
    DeepSeek-style reward function with two components:
    1. Correct answer → judge by llama guard (informative/safe=+1, unsafe=-1, refusal=0)
    2. Answer format → helps to have better response: [0, 0.1]
    """
    rewards = []

    # Get config from global variable if available
    cfg = globals().get('_global_config', None)

    for completion in completions:
        # Component 1: Safety/correctness judgment using actual Llama Guard
        safety_score = llama_guard_judge(completion, cfg)

        # Component 2: Format quality score
        format_score = calculate_format_score(completion)

        # Combine scores
        total_reward = safety_score + format_score
        rewards.append(total_reward)

    return rewards


@hydra.main(version_base=None, config_path="conf", config_name="config_grpo")
def main(cfg: DictConfig) -> None:
    # Set global config for reward function access
    global _global_config
    _global_config = cfg

    print("Configuration:")
    print(OmegaConf.to_yaml(cfg))

    # Check if GRPO training is enabled
    use_grpo = cfg.get('grpo', {}).get('enabled', False)
    print(f"GRPO Training: {'Enabled' if use_grpo else 'Disabled'}")

    # Check Llama Guard configuration
    llama_guard_enabled = cfg.get('llama_guard', {}).get('enabled', True)
    llama_guard_model = cfg.get('llama_guard', {}).get('model_id', 'meta-llama/Llama-Guard-3-8B')
    print(f"Llama Guard: {'Enabled' if llama_guard_enabled else 'Disabled'} ({llama_guard_model})")

    # Initialize Accelerator for distributed training
    accelerator = Accelerator(
        gradient_accumulation_steps=cfg.training.gradient_accumulation_steps,
        mixed_precision="bf16" if cfg.training.bf16 else "no",
        log_with="wandb" if cfg.wandb.enabled else None,
        project_dir=cfg.paths.output_dir,
    )

    # Initialize wandb with Hydra config (only on main process)
    if accelerator.is_main_process and cfg.wandb.enabled:
        accelerator.init_trackers(
            project_name=cfg.wandb.project,
            config=OmegaConf.to_container(cfg, resolve=True),
            init_kwargs={
                "wandb": {
                    "name": cfg.wandb.run_name or cfg.experiment_name,
                    "tags": cfg.wandb.get("tags", []),
                    "notes": cfg.wandb.get("notes", ""),
                }
            }
        )

    # Create model configuration from hydra config
    model_config = {
        "model_type": cfg.model.model_type,
        "num_experts_per_tok": cfg.model.num_experts_per_tok,
        "base_model": cfg.model.base_model,
        "experts": OmegaConf.to_container(cfg.model.experts, resolve=True),
    }

    # Create checkpoint
    print("Creating model checkpoint...")
    expertmerger = ComposeExperts(model_config, torch_dtype=torch.float16)
    expertmerger.compose()
    expertmerger.save_checkpoint(cfg.paths.model_checkpoint)

    # Load model
    accelerator.print("Loading model...")

    # For DeepSpeed, we don't use device_map="auto"
    # if cfg.deepspeed.enabled:
    #     model = MistralForCausalLM.from_pretrained(
    #         cfg.paths.model_checkpoint,
    #         torch_dtype=torch.bfloat16,
    #     )
    # else:
    #     model = MistralForCausalLM.from_pretrained(
    #         cfg.paths.model_checkpoint,
    #         device_map="auto" if not accelerator.distributed_type else None,
    #         torch_dtype=torch.bfloat16,
    #     )

    model = MistralForCausalLM.from_pretrained(
            cfg.paths.model_checkpoint,
            torch_dtype=torch.bfloat16,
        )

    # Train only router (gating) layers
    n_weights, n_router_weights = 0, 0
    for name, weight in model.named_parameters():
        if "gate" in name:
            weight.requires_grad_(True)
            n_router_weights += 1
        else:
            weight.requires_grad_(False)
        n_weights += 1
    print(f"Total weights: {n_weights}, Router weights: {n_router_weights}")

    # Load and prepare dataset
    print("Loading dataset...")
    # dataset = datasets.load_dataset(cfg.data.dataset_name)['train']
    dataset = datasets.load_dataset("json", data_files=cfg.data.dataset_name)["train"]

    # Split dataset
    train_testvalid = dataset.train_test_split(test_size=cfg.data.test_size)
    test_valid = train_testvalid['test'].train_test_split(test_size=cfg.data.test_valid_split)

    dataset = datasets.DatasetDict({
        'train': train_testvalid['train'].select(range(2000)), #.select(range(100))
        'test': test_valid['test'],
        'val': test_valid['train'],
    })

    print(f"Dataset sizes - Train: {len(dataset['train'])}, Val: {len(dataset['val'])}, Test: {len(dataset['test'])}")

    # Prepare dataset for GRPO if enabled
    if use_grpo:
        print("Preparing dataset for GRPO training...")
        # For GRPO, we need to format the dataset to have only prompts
        def prepare_grpo_dataset(examples):
            question_key = "prompt" if "prompt" in examples else "instruction"
            prompts = [f"### Question: {q}\n### Answer:" for q in examples[question_key]]
            return {"prompt": prompts}

        dataset = dataset.map(prepare_grpo_dataset, batched=True, remove_columns=dataset['train'].column_names)

    # Custom wandb callback for additional logging
    class WandbCallback(TrainerCallback):
        def on_log(self, args, state, control, model=None, logs=None, **kwargs):
            if logs:
                # Log router/gate specific metrics if available
                router_logs = {k: v for k, v in logs.items() if 'gate' in k.lower() or 'router' in k.lower()}
                if router_logs:
                    # wandb.log(router_logs, step=state.global_step)
                    accelerator.log(router_logs, step=state.global_step)

                # Explicitly log evaluation loss if available
                eval_logs = {k: v for k, v in logs.items() if k.startswith('eval_')}
                if eval_logs:
                    print(f"Evaluation metrics at step {state.global_step}: {eval_logs}")
                    if cfg.wandb.enabled:
                        # wandb.log(eval_logs, step=state.global_step)
                        accelerator.log(eval_logs, step=state.global_step)

    # Create trainer based on training mode
    if use_grpo:
        print("Setting up GRPO trainer...")

        # GRPO Configuration
        grpo_config = GRPOConfig(
            output_dir=cfg.paths.output_dir,
            per_device_train_batch_size=cfg.grpo.get('per_device_train_batch_size', cfg.training.per_device_train_batch_size),
            per_device_eval_batch_size=cfg.grpo.get('per_device_eval_batch_size', cfg.training.per_device_eval_batch_size),
            learning_rate=cfg.grpo.get('learning_rate', cfg.training.learning_rate),
            save_total_limit=cfg.training.save_total_limit,
            num_train_epochs=cfg.grpo.get('num_train_epochs', cfg.training.num_train_epochs),
            logging_strategy=cfg.training.logging_strategy,
            logging_steps=cfg.training.logging_steps,
            gradient_accumulation_steps=cfg.training.gradient_accumulation_steps,
            bf16=cfg.training.bf16,
            logging_first_step=cfg.training.logging_first_step,
            save_strategy=cfg.training.save_strategy,
            save_steps=cfg.training.save_steps,
            # GRPO specific parameters
            num_generations=cfg.grpo.get('num_generations', 4),
            max_completion_length=cfg.grpo.get('max_completion_length', 256),
            max_prompt_length=cfg.grpo.get('max_prompt_length', 512),
            temperature=cfg.grpo.get('temperature', 0.7),
            beta=cfg.grpo.get('beta', 0.0),  # KL coefficient
            # wandb configuration
            report_to="wandb" if cfg.wandb.enabled and accelerator.is_main_process else "none",
            run_name=cfg.wandb.run_name or cfg.experiment_name,
        )

        # Create GRPO trainer
        trainer = GRPOTrainer(
            model=model,
            args=grpo_config,
            train_dataset=dataset['train'],
            eval_dataset=dataset['val'],
            reward_funcs=deepseek_style_reward_function,
            callbacks=[WandbCallback()] if cfg.wandb.enabled else []
        )

    else:
        print("Setting up SFT trainer...")

        # Training arguments for SFT
        trainer_args = TrainingArguments(
            output_dir=cfg.paths.output_dir,
            per_device_train_batch_size=cfg.training.per_device_train_batch_size,
            per_device_eval_batch_size=cfg.training.per_device_eval_batch_size,
            learning_rate=cfg.training.learning_rate,
            save_total_limit=cfg.training.save_total_limit,
            num_train_epochs=cfg.training.num_train_epochs,
            eval_steps=cfg.training.eval_steps,
            logging_strategy=cfg.training.logging_strategy,
            logging_steps=cfg.training.logging_steps,
            gradient_accumulation_steps=cfg.training.gradient_accumulation_steps,
            bf16=cfg.training.bf16,
            logging_first_step=cfg.training.logging_first_step,
            evaluation_strategy=cfg.training.evaluation_strategy,
            save_strategy=cfg.training.save_strategy,
            save_steps=cfg.training.save_steps,
            load_best_model_at_end=cfg.training.load_best_model_at_end,
            metric_for_best_model=cfg.training.metric_for_best_model,
            greater_is_better=cfg.training.greater_is_better,
            # Ensure evaluation loss is logged
            include_inputs_for_metrics=True,
            log_level="info",
            # wandb configuration
            report_to="wandb" if cfg.wandb.enabled and accelerator.is_main_process else "none",
            run_name=cfg.wandb.run_name or cfg.experiment_name,
            # DeepSpeed configuration
            # deepspeed=cfg.deepspeed.config_path if cfg.deepspeed.enabled else None,
            # Distributed training settings
            # ddp_find_unused_parameters=False,
            # dataloader_pin_memory=False,
        )

        # Create SFT trainer
        trainer = SFTTrainer(
            model,
            args=trainer_args,
            train_dataset=dataset['train'],
            eval_dataset=dataset['val'],
            formatting_func=formatting_prompts_func,
            max_seq_length=cfg.training.max_seq_length,
            callbacks=[WandbCallback()] if cfg.wandb.enabled else []
        )

    # Print training configuration
    if use_grpo:
        print(f"Training mode: GRPO")
        print(f"Number of generations per prompt: {trainer.args.num_generations}")
        print(f"Max completion length: {trainer.args.max_completion_length}")
        print(f"Temperature: {trainer.args.temperature}")
        print(f"Beta (KL coefficient): {trainer.args.beta}")
    else:
        print(f"Training mode: SFT")
        print(f"Evaluation strategy: {trainer_args.evaluation_strategy}")
        print(f"Eval steps: {trainer_args.eval_steps}")
        print(f"Metric for best model: {trainer_args.metric_for_best_model}")

    # Log model info
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    accelerator.print(f"Model parameters - Total: {total_params:,}, Trainable: {trainable_params:,}")
    accelerator.print(f"Trainable percentage: {100 * trainable_params / total_params:.2f}%")

    # Log model architecture info to wandb (only on main process)
    if cfg.wandb.enabled and accelerator.is_main_process:
        log_data = {
            "model_parameters": total_params,
            "trainable_parameters": trainable_params,
            "trainable_percentage": 100 * trainable_params / total_params,
            "n_weights": n_weights,
            "n_router_weights": n_router_weights,
            "dataset_train_size": len(dataset['train']),
            "dataset_val_size": len(dataset['val']),
            "dataset_test_size": len(dataset['test']),
            "training_mode": "GRPO" if use_grpo else "SFT",
        }

        if use_grpo:
            log_data.update({
                "grpo_num_generations": trainer.args.num_generations,
                "grpo_max_completion_length": trainer.args.max_completion_length,
                "grpo_temperature": trainer.args.temperature,
                "grpo_beta": trainer.args.beta,
            })

        accelerator.log(log_data)

    # Start training
    accelerator.print(f"Starting {'GRPO' if use_grpo else 'SFT'} training...")
    trainer.train()

    accelerator.print("Training completed!")

    # Run final evaluation (only for SFT, GRPO handles evaluation differently)
    if not use_grpo:
        accelerator.print("Running final evaluation...")
        eval_results = trainer.evaluate()
        accelerator.print(f"Final evaluation results: {eval_results}")

        # Log final evaluation results
        if cfg.wandb.enabled and accelerator.is_main_process:
            final_eval_logs = {f"final_{k}": v for k, v in eval_results.items()}
            accelerator.log(final_eval_logs)

    # Finish tracking
    if cfg.wandb.enabled and accelerator.is_main_process:
        accelerator.end_training()


if __name__ == "__main__":
    main()


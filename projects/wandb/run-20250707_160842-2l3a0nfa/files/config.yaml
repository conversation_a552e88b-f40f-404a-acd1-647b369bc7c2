_attn_implementation_autoset:
    value: true
_name_or_path:
    value: data/mistral_lora_moe_cybersecurity_top1
_wandb:
    value:
        cli_version: 0.20.1
        m:
            - "1": eval/samples_per_second
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": train/global_step
              "6":
                - 3
              "7": []
            - "1": eval/steps_per_second
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": final_eval_steps_per_second
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": eval_steps_per_second
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": eval_samples_per_second
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": train/loss
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": train/learning_rate
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": train/epoch
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": eval/runtime
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": final_epoch
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": eval_loss
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": final_eval_runtime
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": eval_runtime
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": final_eval_loss
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": train/grad_norm
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": eval/loss
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": final_eval_samples_per_second
              "5": 2
              "6":
                - 1
                - 3
              "7": []
        python_version: 3.10.11
        t:
            "1":
                - 1
                - 11
                - 49
                - 50
                - 51
                - 71
                - 84
                - 98
            "2":
                - 1
                - 11
                - 49
                - 50
                - 51
                - 71
                - 84
                - 98
            "3":
                - 2
                - 7
                - 13
                - 15
                - 19
                - 55
                - 61
                - 62
                - 66
            "4": 3.10.11
            "5": 0.20.1
            "6": 4.46.3
            "8":
                - 2
            "9":
                "1": transformers_trainer
            "12": 0.20.1
            "13": linux-x86_64
accelerator_config:
    value:
        dispatch_batches: null
        even_batches: true
        gradient_accumulation_kwargs: null
        non_blocking: false
        split_batches: false
        use_seedable_sampler: true
adafactor:
    value: false
adam_beta1:
    value: 0.9
adam_beta2:
    value: 0.999
adam_epsilon:
    value: 1e-08
adapter_configs:
    value:
        - alpha_pattern: {}
          auto_mapping: null
          base_model_name_or_path: mistralai/Mistral-7B-v0.1
          bias: none
          eva_config: null
          exclude_modules: null
          fan_in_fan_out: false
          inference_mode: true
          init_lora_weights: true
          layer_replication: null
          layers_pattern: null
          layers_to_transform: null
          loftq_config: {}
          lora_alpha: 64
          lora_bias: false
          lora_dropout: 0.1
          megatron_config: null
          megatron_core: megatron.core
          modules_to_save: null
          peft_type: LORA
          r: 32
          rank_pattern: {}
          revision: null
          target_modules: '{''gate_proj'', ''up_proj'', ''down_proj''}'
          task_type: CAUSAL_LM
          use_dora: false
          use_rslora: false
        - alpha_pattern: {}
          auto_mapping: null
          base_model_name_or_path: mistralai/Mistral-7B-v0.1
          bias: none
          eva_config: null
          exclude_modules: null
          fan_in_fan_out: false
          inference_mode: true
          init_lora_weights: true
          layer_replication: null
          layers_pattern: null
          layers_to_transform: null
          loftq_config: {}
          lora_alpha: 64
          lora_bias: false
          lora_dropout: 0.1
          megatron_config: null
          megatron_core: megatron.core
          modules_to_save: null
          peft_type: LORA
          r: 32
          rank_pattern: {}
          revision: null
          target_modules: '{''gate_proj'', ''up_proj'', ''down_proj''}'
          task_type: CAUSAL_LM
          use_dora: false
          use_rslora: false
add_cross_attention:
    value: false
architectures:
    value:
        - MistralForCausalLM
attention_dropout:
    value: 0
auto_find_batch_size:
    value: false
average_tokens_across_devices:
    value: false
bad_words_ids:
    value: null
batch_eval_metrics:
    value: false
begin_suppress_tokens:
    value: null
bf16:
    value: true
bf16_full_eval:
    value: false
bos_token_id:
    value: 1
chars_per_token:
    value: <CHARS_PER_TOKEN>
chunk_size_feed_forward:
    value: 0
cross_attention_hidden_size:
    value: null
data:
    value:
        dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/polished_safe_data/train_safe_gpt4_qtype2_cybercrime_polished.json
        test_size: 0.1
        test_valid_split: 0.7
data_seed:
    value: null
dataloader_drop_last:
    value: false
dataloader_num_workers:
    value: 0
dataloader_persistent_workers:
    value: false
dataloader_pin_memory:
    value: true
dataloader_prefetch_factor:
    value: null
dataset_batch_size:
    value: 1000
dataset_num_proc:
    value: null
dataset_text_field:
    value: text
ddp_backend:
    value: null
ddp_broadcast_buffers:
    value: null
ddp_bucket_cap_mb:
    value: null
ddp_find_unused_parameters:
    value: null
ddp_timeout:
    value: 1800
debug:
    value: []
decoder_start_token_id:
    value: null
deepspeed:
    value: null
disable_tqdm:
    value: false
dispatch_batches:
    value: null
diversity_penalty:
    value: 0
do_eval:
    value: true
do_predict:
    value: false
do_sample:
    value: false
do_train:
    value: false
early_stopping:
    value: false
encoder_no_repeat_ngram_size:
    value: 0
eos_token_id:
    value: 2
eval_accumulation_steps:
    value: null
eval_delay:
    value: 0
eval_do_concat_batches:
    value: true
eval_on_start:
    value: false
eval_packing:
    value: null
eval_steps:
    value: 5000
eval_strategy:
    value: steps
eval_use_gather_object:
    value: false
evaluation_strategy:
    value: steps
experiment_name:
    value: mistral-cybersecurity-moe
exponential_decay_length_penalty:
    value: null
finetuning_task:
    value: null
forced_bos_token_id:
    value: null
forced_eos_token_id:
    value: null
fp16:
    value: false
fp16_backend:
    value: auto
fp16_full_eval:
    value: false
fp16_opt_level:
    value: O1
fsdp:
    value: []
fsdp_config:
    value:
        min_num_params: 0
        xla: false
        xla_fsdp_grad_ckpt: false
        xla_fsdp_v2: false
fsdp_min_num_params:
    value: 0
fsdp_transformer_layer_cls_to_wrap:
    value: null
full_determinism:
    value: false
gradient_accumulation_steps:
    value: 4
gradient_checkpointing:
    value: false
gradient_checkpointing_kwargs:
    value: null
greater_is_better:
    value: false
group_by_length:
    value: false
half_precision_backend:
    value: auto
head_dim:
    value: 128
hidden_act:
    value: silu
hidden_size:
    value: 4096
hub_always_push:
    value: false
hub_model_id:
    value: null
hub_private_repo:
    value: false
hub_strategy:
    value: every_save
hub_token:
    value: <HUB_TOKEN>
id2label:
    value:
        "0": LABEL_0
        "1": LABEL_1
ignore_data_skip:
    value: false
include_for_metrics:
    value:
        - inputs
        - inputs
include_inputs_for_metrics:
    value: true
include_num_input_tokens_seen:
    value: false
include_tokens_per_second:
    value: false
initializer_range:
    value: 0.02
intermediate_size:
    value: 14336
is_decoder:
    value: false
is_encoder_decoder:
    value: false
jit_mode_eval:
    value: false
label_names:
    value: null
label_smoothing_factor:
    value: 0
label2id:
    value:
        LABEL_0: 0
        LABEL_1: 1
learning_rate:
    value: 0.0001
length_column_name:
    value: length
length_penalty:
    value: 1
load_best_model_at_end:
    value: true
local_rank:
    value: 0
log_level:
    value: info
log_level_replica:
    value: warning
log_on_each_node:
    value: true
logging_dir:
    value: data/output/mistral_lora_moe_cybersecurity_top1/runs/Jul07_16-10-40_cn-g020.server.mila.quebec
logging_first_step:
    value: true
logging_nan_inf_filter:
    value: true
logging_steps:
    value: 25
logging_strategy:
    value: steps
lr_scheduler_type:
    value: linear
max_grad_norm:
    value: 1
max_length:
    value: 20
max_position_embeddings:
    value: 32768
max_seq_length:
    value: 512
max_steps:
    value: -1
metric_for_best_model:
    value: eval_loss
min_length:
    value: 0
model:
    value:
        base_model: mistralai/Mistral-7B-v0.1
        experts:
            - expert_name: adapter_1
              model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-unsafe_cybercrime
            - expert_name: adapter_2
              model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-cybersecurity_kaggle
        model_type: mistral
        num_experts_per_tok: 1
model/num_parameters:
    value: 7356420096
model_init_kwargs:
    value: null
model_type:
    value: mistral
mp_parameters:
    value: ""
neftune_noise_alpha:
    value: null
no_cuda:
    value: false
no_repeat_ngram_size:
    value: 0
num_attention_heads:
    value: 32
num_beam_groups:
    value: 1
num_beams:
    value: 1
num_experts:
    value: 2
num_experts_per_tok:
    value: 1
num_hidden_layers:
    value: 32
num_key_value_heads:
    value: 8
num_of_sequences:
    value: 1024
num_return_sequences:
    value: 1
num_train_epochs:
    value: 10
optim:
    value: adamw_torch
optim_args:
    value: null
optim_target_modules:
    value: null
output_attentions:
    value: false
output_dir:
    value: data/output/mistral_lora_moe_cybersecurity_top1
output_hidden_states:
    value: false
output_scores:
    value: false
overwrite_output_dir:
    value: false
packing:
    value: false
pad_token_id:
    value: null
past_index:
    value: -1
paths:
    value:
        model_checkpoint: data/mistral_lora_moe_cybersecurity_top1
        output_dir: data/output/mistral_lora_moe_cybersecurity_top1
per_device_eval_batch_size:
    value: 1
per_device_train_batch_size:
    value: 1
per_gpu_eval_batch_size:
    value: null
per_gpu_train_batch_size:
    value: null
prediction_loss_only:
    value: false
prefix:
    value: null
problem_type:
    value: null
push_to_hub:
    value: false
push_to_hub_model_id:
    value: null
push_to_hub_organization:
    value: null
push_to_hub_token:
    value: <PUSH_TO_HUB_TOKEN>
ray_scope:
    value: last
remove_invalid_values:
    value: false
remove_unused_columns:
    value: true
repetition_penalty:
    value: 1
report_to:
    value:
        - wandb
restore_callback_states_from_checkpoint:
    value: false
resume_from_checkpoint:
    value: null
return_dict:
    value: true
return_dict_in_generate:
    value: false
rms_norm_eps:
    value: 1e-05
rope_theta:
    value: 10000
router_layers:
    value:
        - gate_proj
        - up_proj
        - down_proj
router_layers_index:
    value:
        - 0
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
        - 8
        - 9
        - 10
        - 11
        - 12
        - 13
        - 14
        - 15
        - 16
        - 17
        - 18
        - 19
        - 20
        - 21
        - 22
        - 23
        - 24
        - 25
        - 26
        - 27
        - 28
        - 29
        - 30
        - 31
run_name:
    value: mistral-cybersecurity-moe
save_on_each_node:
    value: false
save_only_model:
    value: false
save_safetensors:
    value: true
save_steps:
    value: 5000
save_strategy:
    value: steps
save_total_limit:
    value: 1
seed:
    value: 42
sep_token_id:
    value: null
skip_memory_metrics:
    value: true
sliding_window:
    value: 4096
split_batches:
    value: null
suppress_tokens:
    value: null
task_specific_params:
    value: null
temperature:
    value: 1
tf_legacy_loss:
    value: false
tf32:
    value: null
tie_encoder_decoder:
    value: false
tie_word_embeddings:
    value: false
tokenizer_class:
    value: null
top_k:
    value: 50
top_p:
    value: 1
torch_compile:
    value: false
torch_compile_backend:
    value: null
torch_compile_mode:
    value: null
torch_dtype:
    value: float16
torch_empty_cache_steps:
    value: null
torchdynamo:
    value: null
torchscript:
    value: false
tpu_metrics_debug:
    value: false
tpu_num_cores:
    value: null
training:
    value:
        bf16: true
        eval_steps: 5000
        evaluation_strategy: steps
        gradient_accumulation_steps: 4
        greater_is_better: false
        learning_rate: 0.0001
        load_best_model_at_end: true
        logging_first_step: true
        logging_steps: 25
        logging_strategy: steps
        max_seq_length: 512
        metric_for_best_model: eval_loss
        num_train_epochs: 10
        per_device_eval_batch_size: 1
        per_device_train_batch_size: 1
        save_steps: 5000
        save_strategy: steps
        save_total_limit: 1
transformers_version:
    value: 4.46.3
typical_p:
    value: 1
use_bfloat16:
    value: false
use_cache:
    value: true
use_cpu:
    value: false
use_ipex:
    value: false
use_legacy_prediction_loop:
    value: false
use_liger:
    value: false
use_liger_kernel:
    value: false
use_mps_device:
    value: false
vocab_size:
    value: 32000
wandb:
    value:
        enabled: true
        notes: Training Mistral MoE model for cybersecurity with router-only training
        project: mistral-moe-training
        run_name: null
        tags:
            - mistral
            - moe
            - cybersecurity
warmup_ratio:
    value: 0
warmup_steps:
    value: 0
weight_decay:
    value: 0

Creating model checkpoint...
MoE Layer Index : [*]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.71s/it]
[2025-07-07 18:21:23,292][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
100%|██████████| 675/675 [00:00<00:00, 44349.75it/s]
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_economic_top1/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_economic_top1
Loading model...
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  8.03s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_economic_top1 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.mlp.up_
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Total weights: 771, Router weights: 256
Loading dataset...
Dataset sizes - Train: 2000, Val: 123, Test: 288
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
Map: 100%|██████████| 2000/2000 [00:00<00:00, 4788.60 examples/s]
Map: 100%|██████████| 123/123 [00:00<00:00, 3418.14 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(
Using auto half precision backend
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
Model parameters - Total: 7,356,420,096, Trainable: 1,918,238,720
Trainable percentage: 26.08%
Starting training...
[2025-07-07 18:22:49,067] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed info: version=0.15.4, git-hash=unknown, git-branch=unknown
[2025-07-07 18:22:49,070] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-07-07 18:22:53,632] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Flops Profiler Enabled: False
[2025-07-07 18:22:53,642] [INFO] [logging.py:128:log_dist] [Rank 0] Using client Optimizer as basic optimizer
[2025-07-07 18:22:53,646] [INFO] [logging.py:128:log_dist] [Rank 0] Removing param_group that has no 'params' in the basic Optimizer
[2025-07-07 18:22:53,672] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Basic Optimizer = AdamW
[2025-07-07 18:22:53,676] [INFO] [utils.py:59:is_zero_supported_optimizer] Checking ZeRO support for optimizer=AdamW type=<class 'torch.optim.adamw.AdamW'>
[2025-07-07 18:22:53,681] [INFO] [logging.py:128:log_dist] [Rank 0] Creating torch.bfloat16 ZeRO stage 2 optimizer
[2025-07-07 18:22:53,685] [INFO] [stage_1_and_2.py:149:__init__] Reduce bucket size 500000000
[2025-07-07 18:22:53,689] [INFO] [stage_1_and_2.py:150:__init__] Allgather bucket size 200000000
[2025-07-07 18:22:53,693] [INFO] [stage_1_and_2.py:151:__init__] CPU Offload: False
[2025-07-07 18:22:53,696] [INFO] [stage_1_and_2.py:152:__init__] Round robin gradient partitioning: False
[2025-07-07 18:22:57,305] [INFO] [utils.py:781:see_memory_usage] Before initializing optimizer states
[2025-07-07 18:22:57,312] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 20.02 GB         CA 20.36 GB         Max_CA 20 GB
[2025-07-07 18:22:57,316] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 42.18 GB, percent = 4.2%
[2025-07-07 18:22:57,485] [INFO] [utils.py:781:see_memory_usage] After initializing optimizer states
[2025-07-07 18:22:57,492] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 20.91 GB         CA 22.15 GB         Max_CA 22 GB
[2025-07-07 18:22:57,497] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 42.18 GB, percent = 4.2%
[2025-07-07 18:22:57,500] [INFO] [stage_1_and_2.py:544:__init__] optimizer state initialized
[2025-07-07 18:22:57,668] [INFO] [utils.py:781:see_memory_usage] After initializing ZeRO optimizer
[2025-07-07 18:22:57,673] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 19.12 GB         CA 22.15 GB         Max_CA 22 GB
[2025-07-07 18:22:57,677] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 42.18 GB, percent = 4.2%
[2025-07-07 18:22:57,683] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Final Optimizer = DeepSpeedZeroOptimizer
[2025-07-07 18:22:57,687] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed using configured LR scheduler = None
[2025-07-07 18:22:57,691] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed LR Scheduler = None
[2025-07-07 18:22:57,695] [INFO] [logging.py:128:log_dist] [Rank 0] step=0, skipped=0, lr=[0.0001], mom=[(0.9, 0.999)]
[2025-07-07 18:22:57,702] [INFO] [config.py:999:print] DeepSpeedEngine configuration:
[2025-07-07 18:22:57,706] [INFO] [config.py:1003:print]   activation_checkpointing_config  {
    "partition_activations": false,
    "contiguous_memory_optimization": false,
    "cpu_checkpointing": false,
    "number_checkpoints": null,
    "synchronize_checkpoint_boundary": false,
    "profile": false
}
[2025-07-07 18:22:57,710] [INFO] [config.py:1003:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True, 'use_gds': False}
[2025-07-07 18:22:57,714] [INFO] [config.py:1003:print]   amp_enabled .................. False
[2025-07-07 18:22:57,718] [INFO] [config.py:1003:print]   amp_params ................... False
[2025-07-07 18:22:57,722] [INFO] [config.py:1003:print]   autotuning_config ............ {
    "enabled": false,
    "start_step": null,
    "end_step": null,
    "metric_path": null,
    "arg_mappings": null,
    "metric": "throughput",
    "model_info": null,
    "results_dir": "autotuning_results",
    "exps_dir": "autotuning_exps",
    "overwrite": true,
    "fast": true,
    "start_profile_step": 3,
    "end_profile_step": 5,
    "tuner_type": "gridsearch",
    "tuner_early_stopping": 5,
    "tuner_num_trials": 50,
    "model_info_path": null,
    "mp_size": 1,
    "max_train_batch_size": null,
    "min_train_batch_size": 1,
    "max_train_micro_batch_size_per_gpu": 1.024000e+03,
    "min_train_micro_batch_size_per_gpu": 1,
    "num_tuning_micro_batch_sizes": 3
}
[2025-07-07 18:22:57,725] [INFO] [config.py:1003:print]   bfloat16_enabled ............. True
[2025-07-07 18:22:57,729] [INFO] [config.py:1003:print]   bfloat16_immediate_grad_update  False
[2025-07-07 18:22:57,733] [INFO] [config.py:1003:print]   checkpoint_parallel_write_pipeline  False
[2025-07-07 18:22:57,737] [INFO] [config.py:1003:print]   checkpoint_tag_validation_enabled  True
[2025-07-07 18:22:57,742] [INFO] [config.py:1003:print]   checkpoint_tag_validation_fail  False
[2025-07-07 18:22:57,745] [INFO] [config.py:1003:print]   comms_config ................. <deepspeed.comm.config.DeepSpeedCommsConfig object at 0x7f44c007dde0>
[2025-07-07 18:22:57,750] [INFO] [config.py:1003:print]   communication_data_type ...... None
[2025-07-07 18:22:57,754] [INFO] [config.py:1003:print]   compression_config ........... {'weight_quantization': {'shared_parameters': {'enabled': False, 'quantizer_kernel': False, 'schedule_offset': 0, 'quantize_groups': 1, 'quantize_verbose': False, 'quantization_type': 'symmetric', 'quantize_weight_in_forward': False, 'rounding': 'nearest', 'fp16_mixed_quantize': False, 'quantize_change_ratio': 0.001}, 'different_groups': {}}, 'activation_quantization': {'shared_parameters': {'enabled': False, 'quantization_type': 'symmetric', 'range_calibration': 'dynamic', 'schedule_offset': 1000}, 'different_groups': {}}, 'sparse_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'row_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'head_pruning': {'shared_parameters': {'enabled': False, 'method': 'topk', 'schedule_offset': 1000}, 'different_groups': {}}, 'channel_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'layer_reduction': {'enabled': False}}
[2025-07-07 18:22:57,758] [INFO] [config.py:1003:print]   curriculum_enabled_legacy .... False
[2025-07-07 18:22:57,761] [INFO] [config.py:1003:print]   curriculum_params_legacy ..... False
[2025-07-07 18:22:57,766] [INFO] [config.py:1003:print]   data_efficiency_config ....... {'enabled': False, 'seed': 1234, 'data_sampling': {'enabled': False, 'num_epochs': 1000, 'num_workers': 0, 'curriculum_learning': {'enabled': False}}, 'data_routing': {'enabled': False, 'random_ltd': {'enabled': False, 'layer_token_lr_schedule': {'enabled': False}}}}
[2025-07-07 18:22:57,770] [INFO] [config.py:1003:print]   data_efficiency_enabled ...... False
[2025-07-07 18:22:57,774] [INFO] [config.py:1003:print]   dataloader_drop_last ......... False
[2025-07-07 18:22:57,778] [INFO] [config.py:1003:print]   disable_allgather ............ False
[2025-07-07 18:22:57,782] [INFO] [config.py:1003:print]   dump_state ................... False
[2025-07-07 18:22:57,786] [INFO] [config.py:1003:print]   dynamic_loss_scale_args ...... None
[2025-07-07 18:22:57,790] [INFO] [config.py:1003:print]   eigenvalue_enabled ........... False
[2025-07-07 18:22:57,794] [INFO] [config.py:1003:print]   eigenvalue_gas_boundary_resolution  1
[2025-07-07 18:22:57,798] [INFO] [config.py:1003:print]   eigenvalue_layer_name ........ bert.encoder.layer
[2025-07-07 18:22:57,803] [INFO] [config.py:1003:print]   eigenvalue_layer_num ......... 0
[2025-07-07 18:22:57,807] [INFO] [config.py:1003:print]   eigenvalue_max_iter .......... 100
[2025-07-07 18:22:57,811] [INFO] [config.py:1003:print]   eigenvalue_stability ......... 1e-06
[2025-07-07 18:22:57,815] [INFO] [config.py:1003:print]   eigenvalue_tol ............... 0.01
[2025-07-07 18:22:57,819] [INFO] [config.py:1003:print]   eigenvalue_verbose ........... False
[2025-07-07 18:22:57,824] [INFO] [config.py:1003:print]   elasticity_enabled ........... False
[2025-07-07 18:22:57,829] [INFO] [config.py:1003:print]   flops_profiler_config ........ {
    "enabled": false,
    "recompute_fwd_factor": 0.0,
    "profile_step": 1,
    "module_depth": -1,
    "top_modules": 1,
    "detailed": true,
    "output_file": null
}
[2025-07-07 18:22:57,833] [INFO] [config.py:1003:print]   fp16_auto_cast ............... None
[2025-07-07 18:22:57,837] [INFO] [config.py:1003:print]   fp16_enabled ................. False
[2025-07-07 18:22:57,841] [INFO] [config.py:1003:print]   fp16_master_weights_and_gradients  False
[2025-07-07 18:22:57,844] [INFO] [config.py:1003:print]   global_rank .................. 0
[2025-07-07 18:22:57,849] [INFO] [config.py:1003:print]   grad_accum_dtype ............. None
[2025-07-07 18:22:57,853] [INFO] [config.py:1003:print]   gradient_accumulation_steps .. 4
[2025-07-07 18:22:57,857] [INFO] [config.py:1003:print]   gradient_clipping ............ 1.0
[2025-07-07 18:22:57,861] [INFO] [config.py:1003:print]   gradient_predivide_factor .... 1.0
[2025-07-07 18:22:57,864] [INFO] [config.py:1003:print]   graph_harvesting ............. False
[2025-07-07 18:22:57,868] [INFO] [config.py:1003:print]   hybrid_engine ................ enabled=False max_out_tokens=512 inference_tp_size=1 release_inference_cache=False pin_parameters=True tp_gather_partition_size=8
[2025-07-07 18:22:57,872] [INFO] [config.py:1003:print]   initial_dynamic_scale ........ 1
[2025-07-07 18:22:57,876] [INFO] [config.py:1003:print]   load_universal_checkpoint .... False
[2025-07-07 18:22:57,880] [INFO] [config.py:1003:print]   loss_scale ................... 1.0
[2025-07-07 18:22:57,884] [INFO] [config.py:1003:print]   memory_breakdown ............. False
[2025-07-07 18:22:57,888] [INFO] [config.py:1003:print]   mics_hierarchial_params_gather  False
[2025-07-07 18:22:57,892] [INFO] [config.py:1003:print]   mics_shard_size .............. -1
[2025-07-07 18:22:57,897] [INFO] [config.py:1003:print]   monitor_config ............... tensorboard=TensorBoardConfig(enabled=False, output_path='', job_name='DeepSpeedJobName') comet=CometConfig(enabled=False, samples_log_interval=100, project=None, workspace=None, api_key=None, experiment_name=None, experiment_key=None, online=None, mode=None) wandb=WandbConfig(enabled=False, group=None, team=None, project='deepspeed') csv_monitor=CSVConfig(enabled=False, output_path='', job_name='DeepSpeedJobName')
[2025-07-07 18:22:57,901] [INFO] [config.py:1003:print]   nebula_config ................ {
    "enabled": false,
    "persistent_storage_path": null,
    "persistent_time_interval": 100,
    "num_of_version_in_retention": 2,
    "enable_nebula_load": true,
    "load_path": null
}
[2025-07-07 18:22:57,906] [INFO] [config.py:1003:print]   optimizer_legacy_fusion ...... False
[2025-07-07 18:22:57,911] [INFO] [config.py:1003:print]   optimizer_name ............... None
[2025-07-07 18:22:57,916] [INFO] [config.py:1003:print]   optimizer_params ............. None
[2025-07-07 18:22:57,921] [INFO] [config.py:1003:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0, 'pipe_partitioned': True, 'grad_partitioned': True}
[2025-07-07 18:22:57,926] [INFO] [config.py:1003:print]   pld_enabled .................. False
[2025-07-07 18:22:57,931] [INFO] [config.py:1003:print]   pld_params ................... False
[2025-07-07 18:22:57,935] [INFO] [config.py:1003:print]   prescale_gradients ........... False
[2025-07-07 18:22:57,939] [INFO] [config.py:1003:print]   scheduler_name ............... None
[2025-07-07 18:22:57,943] [INFO] [config.py:1003:print]   scheduler_params ............. None
[2025-07-07 18:22:57,947] [INFO] [config.py:1003:print]   seq_parallel_communication_data_type  torch.float32
[2025-07-07 18:22:57,951] [INFO] [config.py:1003:print]   sparse_attention ............. None
[2025-07-07 18:22:57,955] [INFO] [config.py:1003:print]   sparse_gradients_enabled ..... False
[2025-07-07 18:22:57,960] [INFO] [config.py:1003:print]   steps_per_print .............. inf
[2025-07-07 18:22:57,964] [INFO] [config.py:1003:print]   timers_config ................ enabled=True synchronized=True
[2025-07-07 18:22:57,968] [INFO] [config.py:1003:print]   train_batch_size ............. 16
[2025-07-07 18:22:57,972] [INFO] [config.py:1003:print]   train_micro_batch_size_per_gpu  1
[2025-07-07 18:22:57,977] [INFO] [config.py:1003:print]   use_data_before_expert_parallel_  False
[2025-07-07 18:22:57,981] [INFO] [config.py:1003:print]   use_node_local_storage ....... False
[2025-07-07 18:22:57,985] [INFO] [config.py:1003:print]   wall_clock_breakdown ......... False
[2025-07-07 18:22:57,990] [INFO] [config.py:1003:print]   weight_quantization_config ... None
[2025-07-07 18:22:57,995] [INFO] [config.py:1003:print]   world_size ................... 4
[2025-07-07 18:22:57,999] [INFO] [config.py:1003:print]   zero_allow_untested_optimizer  True
[2025-07-07 18:22:58,004] [INFO] [config.py:1003:print]   zero_config .................. stage=2 contiguous_gradients=True reduce_scatter=True reduce_bucket_size=500000000 use_multi_rank_bucket_allreduce=True allgather_partitions=True allgather_bucket_size=200000000 overlap_comm=True load_from_fp32_weights=True elastic_checkpoint=False offload_param=None offload_optimizer=None sub_group_size=1000000000 cpu_offload_param=None cpu_offload_use_pin_memory=None cpu_offload=None prefetch_bucket_size=50000000 param_persistence_threshold=100000 model_persistence_threshold=9223372036854775807 max_live_parameters=1000000000 max_reuse_distance=1000000000 gather_16bit_weights_on_model_save=False use_all_reduce_for_fetch_params=False stage3_gather_fp16_weights_on_model_save=False ignore_unused_parameters=True legacy_stage1=False round_robin_gradients=False zero_hpz_partition_size=1 zero_quantized_weights=False zero_quantized_nontrainable_weights=False zero_quantized_gradients=False mics_shard_size=-1 mics_hierarchical_params_gather=False memory_efficient_linear=True pipeline_loading_checkpoint=False override_module_apply=True
[2025-07-07 18:22:58,008] [INFO] [config.py:1003:print]   zero_enabled ................. True
[2025-07-07 18:22:58,013] [INFO] [config.py:1003:print]   zero_force_ds_cpu_optimizer .. True
[2025-07-07 18:22:58,017] [INFO] [config.py:1003:print]   zero_optimization_stage ...... 2
[2025-07-07 18:22:58,021] [INFO] [config.py:989:print_user_config]   json = {
    "zero_optimization": {
        "stage": 2,
        "allgather_partitions": true,
        "allgather_bucket_size": 2.000000e+08,
        "overlap_comm": true,
        "reduce_scatter": true,
        "contiguous_gradients": true
    },
    "gradient_accumulation_steps": 4,
    "gradient_clipping": 1.0,
    "steps_per_print": inf,
    "train_batch_size": 16,
    "train_micro_batch_size_per_gpu": 1,
    "wall_clock_breakdown": false,
    "bf16": {
        "enabled": true
    },
    "fp16": {
        "enabled": false
    },
    "zero_allow_untested_optimizer": true
}
***** Running training *****
  Num examples = 2,000
  Num Epochs = 10
  Instantaneous batch size per device = 1
  Total train batch size (w. parallel, distributed & accumulation) = 16
  Gradient Accumulation steps = 4
  Total optimization steps = 1,250
  Number of trainable parameters = 1,918,238,720
Automatic Weights & Biases logging enabled, to disable set os.environ["WANDB_DISABLED"] = "true"
                                                  
{'loss': 1.4368, 'grad_norm': 6.577330112457275, 'learning_rate': 9.992e-05, 'epoch': 0.01}
{'loss': 1.3413, 'grad_norm': 1.806562900543213, 'learning_rate': 9.8e-05, 'epoch': 0.2}
{'loss': 1.2669, 'grad_norm': 1.8418515920639038, 'learning_rate': 9.6e-05, 'epoch': 0.4}
{'loss': 1.1521, 'grad_norm': 1.7007614374160767, 'learning_rate': 9.4e-05, 'epoch': 0.6}
{'loss': 1.0858, 'grad_norm': 1.5880428552627563, 'learning_rate': 9.200000000000001e-05, 'epoch': 0.8}
{'loss': 1.0409, 'grad_norm': 1.4595365524291992, 'learning_rate': 9e-05, 'epoch': 1.0}
{'loss': 0.8948, 'grad_norm': 1.331572413444519, 'learning_rate': 8.800000000000001e-05, 'epoch': 1.2}
{'loss': 0.8688, 'grad_norm': 1.3122098445892334, 'learning_rate': 8.6e-05, 'epoch': 1.4}
{'loss': 0.8102, 'grad_norm': 1.6359957456588745, 'learning_rate': 8.4e-05, 'epoch': 1.6}
{'loss': 0.8001, 'grad_norm': 1.3256783485412598, 'learning_rate': 8.2e-05, 'epoch': 1.8}
{'loss': 0.7273, 'grad_norm': 1.3895447254180908, 'learning_rate': 8e-05, 'epoch': 2.0}
{'loss': 0.5938, 'grad_norm': 1.2708992958068848, 'learning_rate': 7.800000000000001e-05, 'epoch': 2.2}
{'loss': 0.6284, 'grad_norm': 1.4323066473007202, 'learning_rate': 7.6e-05, 'epoch': 2.4}
{'loss': 0.5972, 'grad_norm': 2.0445761680603027, 'learning_rate': 7.4e-05, 'epoch': 2.6}
{'loss': 0.5827, 'grad_norm': 1.2146183252334595, 'learning_rate': 7.2e-05, 'epoch': 2.8}
{'loss': 0.5611, 'grad_norm': 1.506158471107483, 'learning_rate': 7e-05, 'epoch': 3.0}
{'loss': 0.4699, 'grad_norm': 1.2073380947113037, 'learning_rate': 6.800000000000001e-05, 'epoch': 3.2}
{'loss': 0.4595, 'grad_norm': 1.3929585218429565, 'learning_rate': 6.6e-05, 'epoch': 3.4}
{'loss': 0.4487, 'grad_norm': 1.1932791471481323, 'learning_rate': 6.400000000000001e-05, 'epoch': 3.6}
{'loss': 0.4029, 'grad_norm': 1.1507785320281982, 'learning_rate': 6.2e-05, 'epoch': 3.8}
{'loss': 0.4007, 'grad_norm': 1.109811782836914, 'learning_rate': 6e-05, 'epoch': 4.0}
{'loss': 0.3681, 'grad_norm': 1.1033133268356323, 'learning_rate': 5.8e-05, 'epoch': 4.2}
{'loss': 0.3382, 'grad_norm': 1.1297688484191895, 'learning_rate': 5.6000000000000006e-05, 'epoch': 4.4}
{'loss': 0.3083, 'grad_norm': 0.8749898076057434, 'learning_rate': 5.4000000000000005e-05, 'epoch': 4.6}
{'loss': 0.2799, 'grad_norm': 1.0007977485656738, 'learning_rate': 5.2000000000000004e-05, 'epoch': 4.8}
{'loss': 0.311, 'grad_norm': 0.861478865146637, 'learning_rate': 5e-05, 'epoch': 5.0}
{'loss': 0.2425, 'grad_norm': 0.7471625208854675, 'learning_rate': 4.8e-05, 'epoch': 5.2}
{'loss': 0.2573, 'grad_norm': 0.956953763961792, 'learning_rate': 4.600000000000001e-05, 'epoch': 5.4}
{'loss': 0.2458, 'grad_norm': 0.8539668321609497, 'learning_rate': 4.4000000000000006e-05, 'epoch': 5.6}
{'loss': 0.2246, 'grad_norm': 0.9366992712020874, 'learning_rate': 4.2e-05, 'epoch': 5.8}
{'loss': 0.233, 'grad_norm': 0.6998581290245056, 'learning_rate': 4e-05, 'epoch': 6.0}
{'loss': 0.1882, 'grad_norm': 0.8355609178543091, 'learning_rate': 3.8e-05, 'epoch': 6.2}
{'loss': 0.1793, 'grad_norm': 1.0062161684036255, 'learning_rate': 3.6e-05, 'epoch': 6.4}
{'loss': 0.171, 'grad_norm': 0.9196571111679077, 'learning_rate': 3.4000000000000007e-05, 'epoch': 6.6}
{'loss': 0.173, 'grad_norm': 0.9538785219192505, 'learning_rate': 3.2000000000000005e-05, 'epoch': 6.8}
{'loss': 0.1522, 'grad_norm': 0.874276876449585, 'learning_rate': 3e-05, 'epoch': 7.0}
{'loss': 0.1275, 'grad_norm': 0.6395280957221985, 'learning_rate': 2.8000000000000003e-05, 'epoch': 7.2}
{'loss': 0.1351, 'grad_norm': 1.0002806186676025, 'learning_rate': 2.6000000000000002e-05, 'epoch': 7.4}
{'loss': 0.1194, 'grad_norm': 0.689862072467804, 'learning_rate': 2.4e-05, 'epoch': 7.6}
{'loss': 0.1184, 'grad_norm': 0.5886167883872986, 'learning_rate': 2.2000000000000003e-05, 'epoch': 7.8}
{'loss': 0.1154, 'grad_norm': 0.5104950666427612, 'learning_rate': 2e-05, 'epoch': 8.0}
{'loss': 0.1023, 'grad_norm': 0.5060407519340515, 'learning_rate': 1.8e-05, 'epoch': 8.2}
{'loss': 0.0867, 'grad_norm': 0.4884108901023865, 'learning_rate': 1.6000000000000003e-05, 'epoch': 8.4}
{'loss': 0.0883, 'grad_norm': 0.6315594911575317, 'learning_rate': 1.4000000000000001e-05, 'epoch': 8.6}
{'loss': 0.0926, 'grad_norm': 0.7728333473205566, 'learning_rate': 1.2e-05, 'epoch': 8.8}
{'loss': 0.0684, 'grad_norm': 0.49677005410194397, 'learning_rate': 1e-05, 'epoch': 9.0}
{'loss': 0.0737, 'grad_norm': 0.5739369988441467, 'learning_rate': 8.000000000000001e-06, 'epoch': 9.2}
{'loss': 0.0715, 'grad_norm': 0.589724063873291, 'learning_rate': 6e-06, 'epoch': 9.4}
{'loss': 0.0663, 'grad_norm': 0.3128906190395355, 'learning_rate': 4.000000000000001e-06, 'epoch': 9.6}
{'loss': 0.067, 'grad_norm': 0.44491955637931824, 'learning_rate': 2.0000000000000003e-06, 'epoch': 9.8}
{'loss': 0.0712, 'grad_norm': 0.43449950218200684, 'learning_rate': 0.0, 'epoch': 10.0}
Configuration saved in data/output/mistral_lora_moe_economic_top1/checkpoint-1250/config.json
Configuration saved in data/output/mistral_lora_moe_economic_top1/checkpoint-1250/generation_config.json
The model is bigger than the maximum size per checkpoint (5GB) and is going to be split in 3 checkpoint shards. You can find where each parameters has been saved in the index located at data/output/mistral_lora_moe_economic_top1/checkpoint-1250/model.safetensors.index.json.
tokenizer config file saved in data/output/mistral_lora_moe_economic_top1/checkpoint-1250/tokenizer_config.json
Special tokens file saved in data/output/mistral_lora_moe_economic_top1/checkpoint-1250/special_tokens_map.json
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
[2025-07-07 18:52:20,830] [INFO] [logging.py:128:log_dist] [Rank 0] [Torch] Checkpoint global_step1250 is about to be saved!
[2025-07-07 18:52:24,316] [INFO] [logging.py:128:log_dist] [Rank 0] Saving model checkpoint: data/output/mistral_lora_moe_economic_top1/checkpoint-1250/global_step1250/mp_rank_00_model_states.pt
[2025-07-07 18:52:24,320] [INFO] [torch_checkpoint_engine.py:21:save] [Torch] Saving data/output/mistral_lora_moe_economic_top1/checkpoint-1250/global_step1250/mp_rank_00_model_states.pt...
[2025-07-07 18:53:28,674] [INFO] [torch_checkpoint_engine.py:23:save] [Torch] Saved data/output/mistral_lora_moe_economic_top1/checkpoint-1250/global_step1250/mp_rank_00_model_states.pt.
[2025-07-07 18:53:28,700] [INFO] [torch_checkpoint_engine.py:21:save] [Torch] Saving data/output/mistral_lora_moe_economic_top1/checkpoint-1250/global_step1250/bf16_zero_pp_rank_0_mp_rank_00_optim_states.pt...
[2025-07-07 18:53:39,211] [INFO] [torch_checkpoint_engine.py:23:save] [Torch] Saved data/output/mistral_lora_moe_economic_top1/checkpoint-1250/global_step1250/bf16_zero_pp_rank_0_mp_rank_00_optim_states.pt.
[2025-07-07 18:53:39,219] [INFO] [engine.py:3536:_save_zero_checkpoint] zero checkpoint saved data/output/mistral_lora_moe_economic_top1/checkpoint-1250/global_step1250/bf16_zero_pp_rank_0_mp_rank_00_optim_states.pt
[2025-07-07 18:53:39,224] [INFO] [torch_checkpoint_engine.py:33:commit] [Torch] Checkpoint global_step1250 is ready now!


Training completed. Do not forget to share your model on huggingface.co/models =)


100%|██████████| 1250/1250 [30:46<00:00,  1.48s/it]
{'train_runtime': 1846.3522, 'train_samples_per_second': 10.832, 'train_steps_per_second': 0.677, 'train_loss': 0.40426397027969363, 'epoch': 10.0}
Training completed!
Running final evaluation...

***** Running Evaluation *****
  Num examples = 123
  Batch size = 1
100%|██████████| 31/31 [00:03<00:00,  8.60it/s]
Evaluation metrics at step 1250: {'eval_loss': 0.2750053107738495, 'eval_runtime': 3.7069, 'eval_samples_per_second': 33.182, 'eval_steps_per_second': 8.363}
Final evaluation results: {'eval_loss': 0.2750053107738495, 'eval_runtime': 3.7069, 'eval_samples_per_second': 33.182, 'eval_steps_per_second': 8.363, 'epoch': 10.0}

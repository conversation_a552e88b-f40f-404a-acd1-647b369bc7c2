2025-07-07 17:53:26,144 INFO    MainThread:2879352 [wandb_setup.py:_flush():81] Current SDK version is 0.20.1
2025-07-07 17:53:26,146 INFO    MainThread:2879352 [wandb_setup.py:_flush():81] Configure stats pid to 2879352
2025-07-07 17:53:26,146 INFO    MainThread:2879352 [wandb_setup.py:_flush():81] Loading settings from /home/<USER>/m/maryam.hashemzadeh/.config/wandb/settings
2025-07-07 17:53:26,146 INFO    MainThread:2879352 [wandb_setup.py:_flush():81] Loading settings from /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/settings
2025-07-07 17:53:26,146 INFO    MainThread:2879352 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-07-07 17:53:26,147 INFO    MainThread:2879352 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/run-20250707_175326-ab6k0rze/logs/debug.log
2025-07-07 17:53:26,147 INFO    MainThread:2879352 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/run-20250707_175326-ab6k0rze/logs/debug-internal.log
2025-07-07 17:53:26,147 INFO    MainThread:2879352 [wandb_init.py:init():831] calling init triggers
2025-07-07 17:53:26,147 INFO    MainThread:2879352 [wandb_init.py:init():836] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-07-07 17:53:26,147 INFO    MainThread:2879352 [wandb_init.py:init():872] starting backend
2025-07-07 17:53:26,372 INFO    MainThread:2879352 [wandb_init.py:init():875] sending inform_init request
2025-07-07 17:53:26,385 INFO    MainThread:2879352 [wandb_init.py:init():883] backend started and connected
2025-07-07 17:53:26,386 INFO    MainThread:2879352 [wandb_init.py:init():956] updated telemetry
2025-07-07 17:53:26,441 INFO    MainThread:2879352 [wandb_init.py:init():980] communicating run to backend with 90.0 second timeout
2025-07-07 17:53:26,797 INFO    MainThread:2879352 [wandb_init.py:init():1032] starting run threads in backend
2025-07-07 17:53:26,999 INFO    MainThread:2879352 [wandb_run.py:_console_start():2453] atexit reg
2025-07-07 17:53:27,000 INFO    MainThread:2879352 [wandb_run.py:_redirect():2301] redirect: wrap_raw
2025-07-07 17:53:27,000 INFO    MainThread:2879352 [wandb_run.py:_redirect():2370] Wrapping output streams.
2025-07-07 17:53:27,000 INFO    MainThread:2879352 [wandb_run.py:_redirect():2393] Redirects installed.
2025-07-07 17:53:27,006 INFO    MainThread:2879352 [wandb_init.py:init():1078] run started, returning control to user process
2025-07-07 17:53:27,007 INFO    MainThread:2879352 [wandb_run.py:_config_callback():1358] config_cb None None {'experiment_name': 'mistral-economic-moe', 'seed': 42, 'wandb': {'enabled': True, 'project': 'mistral-moe-training', 'run_name': None, 'tags': ['mistral', 'moe', 'economic'], 'notes': 'Training Mistral MoE model for economic with router-only training'}, 'model': {'model_type': 'mistral', 'num_experts_per_tok': 1, 'base_model': 'mistralai/Mistral-7B-v0.1', 'experts': [{'expert_name': 'adapter_1', 'model_id': '/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-unsafe_economic'}, {'expert_name': 'adapter_2', 'model_id': '/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-fiqa'}]}, 'training': {'per_device_train_batch_size': 1, 'per_device_eval_batch_size': 1, 'learning_rate': 0.0001, 'save_total_limit': 1, 'num_train_epochs': 10, 'eval_steps': 5000, 'logging_strategy': 'steps', 'logging_steps': 25, 'gradient_accumulation_steps': 4, 'bf16': True, 'logging_first_step': True, 'evaluation_strategy': 'steps', 'save_strategy': 'steps', 'save_steps': 5000, 'load_best_model_at_end': True, 'metric_for_best_model': 'eval_loss', 'greater_is_better': False, 'max_seq_length': 512}, 'data': {'dataset_name': '/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/polished_safe_data/train_safe_gpt4_qtype2_economic_crime_polished.json', 'test_size': 0.1, 'test_valid_split': 0.7}, 'paths': {'model_checkpoint': 'data/mistral_lora_moe_economic_top1', 'output_dir': 'data/output/mistral_lora_moe_economic_top1'}}

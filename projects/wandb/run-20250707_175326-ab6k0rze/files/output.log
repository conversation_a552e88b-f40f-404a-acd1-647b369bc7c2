Creating model checkpoint...
MoE Layer Index : [*]
Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.82s/it]
[2025-07-07 17:53:40,609][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
100%|██████████| 675/675 [00:00<00:00, 71202.54it/s]
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library

Creating model checkpoint...
MoE Layer Index : [*]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.95s/it]
[2025-07-07 16:01:33,780][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
100%|██████████| 675/675 [00:00<00:00, 24787.29it/s]
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_cybersecurity_top1/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_cybersecurity_top1
Loading model...
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.74s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_cybersecurity_top1 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.ml
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Total weights: 771, Router weights: 256
Loading dataset...
Generating train split: 6483 examples [00:00, 25992.91 examples/s]
Dataset sizes - Train: 5834, Val: 194, Test: 455
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
Map:   0%|          | 0/5834 [00:00<?, ? examples/s]
Error executing job with overrides: []
Traceback (most recent call last):
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 172, in main
    trainer = SFTTrainer(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py", line 101, in inner_f
    return f(*args, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/deprecation.py", line 165, in wrapped_func
    return func(*args, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py", line 368, in __init__
    train_dataset = self._prepare_dataset(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py", line 477, in _prepare_dataset
    return self._prepare_non_packed_dataloader(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py", line 549, in _prepare_non_packed_dataloader
    tokenized_dataset = dataset.map(tokenize, **map_kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 557, in wrapper
    out: Union["Dataset", "DatasetDict"] = func(self, *args, **kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3079, in map
    for rank, done, content in Dataset._map_single(**dataset_kwargs):
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3525, in _map_single
    for i, batch in iter_outputs(shard_iterable):
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3475, in iter_outputs
    yield i, apply_function(example, i, offset=offset)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3398, in apply_function
    processed_inputs = function(*fn_args, *additional_args, **fn_kwargs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py", line 513, in tokenize
    element[dataset_text_field] if formatting_func is None else formatting_func(element),
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 24, in formatting_prompts_func
    for i in range(len(example['instruction'])):
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/datasets/formatting/formatting.py", line 278, in __getitem__
    value = self.data[key]
KeyError: 'instruction'

Set the environment variable HYDRA_FULL_ERROR=1 for a complete stack trace.

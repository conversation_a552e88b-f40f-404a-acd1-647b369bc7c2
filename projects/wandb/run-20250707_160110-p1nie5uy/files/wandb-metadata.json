{"os": "Linux-5.15.0-101-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.11", "startedAt": "2025-07-07T20:01:11.097141Z", "program": "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", "codePath": "projects/exmp_1.py", "git": {"remote": "**************:Leeroo-AI/mergoo.git", "commit": "8dec73f1083d21e174484b5b8bc75747c4034282"}, "email": "<EMAIL>", "root": "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects", "host": "cn-g013.server.mila.quebec", "executable": "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/bin/python", "codePathLocal": "exmp_1.py", "cpu_count": 64, "cpu_count_logical": 64, "gpu": "NVIDIA A100-SXM4-80GB", "gpu_count": 4, "disk": {"/": {"total": "************", "used": "58968010752"}}, "memory": {"total": "1076141596672"}, "cpu": {"count": 64, "countLogical": 64}, "gpu_nvidia": [{"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "***********", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-72dc27f5-0fa1-0bc5-1ef2-0657d9e0357e"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "***********", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-053e685d-e2d3-4984-989a-9d5c28cdce77"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "***********", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-8f494385-0f3d-9f70-4382-4c33d153f5c2"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "***********", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-2350c43b-be72-b655-14bc-880ddd378c4d"}], "slurm": {"cluster_name": "mila", "conf": "/etc/slurm/slurm.conf", "cpus_on_node": "16", "cpus_per_task": "16", "gpus_on_node": "4", "gtids": "0", "job_account": "mila", "job_cpus_per_node": "16", "job_end_time": "**********", "job_gid": "*********", "job_gpus": "0,1,2,3", "job_id": "7145201", "job_name": "moe_support", "job_nodelist": "cn-g013", "job_num_nodes": "1", "job_partition": "short-unkillable", "job_qos": "normal", "job_start_time": "**********", "job_uid": "*********", "job_user": "marya<PERSON>.<PERSON><PERSON>", "jobid": "7145201", "localid": "0", "mem_per_node": "131072", "nnodes": "1", "nodeid": "0", "nodelist": "cn-g013", "prio_process": "0", "procid": "0", "script_context": "prolog_task", "submit_dir": "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects", "submit_host": "cn-b001.server.mila.quebec", "task_pid": "488567", "tasks_per_node": "1", "tmpdir": "/tmp", "topology_addr": "cn-g013", "topology_addr_pattern": "node", "tres_per_task": "cpu:16"}, "cudaVersion": "12.2"}
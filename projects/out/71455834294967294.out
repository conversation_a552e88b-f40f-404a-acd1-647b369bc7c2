[2025-07-07 18:20:45,237] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
Configuration:Configuration:Configuration:Configuration:



experiment_name: mistral-economic-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - economic
  notes: Training Mistral MoE model for economic with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-unsafe_economic
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-fiqa
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/polished_safe_data/train_safe_gpt4_qtype2_economic_crime_polished.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_economic_top1
  output_dir: data/output/mistral_lora_moe_economic_top1
experiment_name: mistral-economic-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - economic
  notes: Training Mistral MoE model for economic with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-unsafe_economic
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-fiqa
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/polished_safe_data/train_safe_gpt4_qtype2_economic_crime_polished.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_economic_top1
  output_dir: data/output/mistral_lora_moe_economic_top1
experiment_name: mistral-economic-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - economic
  notes: Training Mistral MoE model for economic with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-unsafe_economic
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-fiqa
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/polished_safe_data/train_safe_gpt4_qtype2_economic_crime_polished.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_economic_top1
  output_dir: data/output/mistral_lora_moe_economic_top1
experiment_name: mistral-economic-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - economic
  notes: Training Mistral MoE model for economic with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-unsafe_economic
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-fiqa
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/polished_safe_data/train_safe_gpt4_qtype2_economic_crime_polished.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_economic_top1
  output_dir: data/output/mistral_lora_moe_economic_top1




[2025-07-07 18:20:58,289] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-07 18:20:58,335] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-07 18:20:58,335] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-07 18:20:58,336] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-07 18:21:01,052] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-07-07 18:21:01,120] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-07-07 18:21:01,120] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-07-07 18:21:01,122] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-07-07 18:21:01,124] [INFO] [comm.py:683:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
Creating model checkpoint...Creating model checkpoint...

MoE Layer Index : [*]
MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
[2025-07-07 18:21:23,292][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-07-07 18:21:23,315][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-07-07 18:21:23,320][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-07-07 18:21:23,321][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
count_averaged_layers : 195
count_averaged_layers : 195count_router_layers : 480count_averaged_layers : 195
count_router_layers : 480


count_total_router_layers : 480count_total_router_layers : 480count_router_layers : 480


count_total_router_layers : 480
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_economic_top1/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_economic_top1
Loading model...
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_economic_top1/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_economic_top1
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_economic_top1/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_economic_top1
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_economic_top1/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_economic_top1
Total weights: 771, Router weights: 256
Loading dataset...
Total weights: 771, Router weights: 256
Loading dataset...
Total weights: 771, Router weights: 256Total weights: 771, Router weights: 256

Loading dataset...Loading dataset...

Dataset sizes - Train: 2000, Val: 123, Test: 288
Dataset sizes - Train: 2000, Val: 123, Test: 288
Dataset sizes - Train: 2000, Val: 123, Test: 288
Dataset sizes - Train: 2000, Val: 123, Test: 288
cn-g022:3813298:3813298 [0] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.32<0>
cn-g022:3813298:3813298 [0] NCCL INFO cudaDriverVersion 12020
cn-g022:3813298:3813298 [0] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g022:3813300:3813300 [2] NCCL INFO cudaDriverVersion 12020
cn-g022:3813299:3813299 [1] NCCL INFO cudaDriverVersion 12020
cn-g022:3813301:3813301 [3] NCCL INFO cudaDriverVersion 12020
cn-g022:3813298:3813298 [0] NCCL INFO Comm config Blocking set to 1
cn-g022:3813300:3813300 [2] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.32<0>
cn-g022:3813299:3813299 [1] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.32<0>
cn-g022:3813301:3813301 [3] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.32<0>
cn-g022:3813300:3813300 [2] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g022:3813299:3813299 [1] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g022:3813301:3813301 [3] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g022:3813300:3813300 [2] NCCL INFO Comm config Blocking set to 1
cn-g022:3813299:3813299 [1] NCCL INFO Comm config Blocking set to 1
cn-g022:3813301:3813301 [3] NCCL INFO Comm config Blocking set to 1
cn-g022:3813298:3813993 [0] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g022:3813299:3813995 [1] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g022:3813300:3813994 [2] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g022:3813301:3813996 [3] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g022:3813299:3813995 [1] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.32<0>
cn-g022:3813299:3813995 [1] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g022:3813299:3813995 [1] NCCL INFO Using network IB
cn-g022:3813301:3813996 [3] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.32<0>
cn-g022:3813299:3813995 [1] NCCL INFO ncclCommInitRankConfig comm 0x56501a085d20 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0x2d4f9a4a14d6ee2 - Init START
cn-g022:3813301:3813996 [3] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g022:3813301:3813996 [3] NCCL INFO Using network IB
cn-g022:3813300:3813994 [2] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.32<0>
cn-g022:3813298:3813993 [0] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.32<0>
cn-g022:3813301:3813996 [3] NCCL INFO ncclCommInitRankConfig comm 0x562857c84bc0 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0x2d4f9a4a14d6ee2 - Init START
cn-g022:3813300:3813994 [2] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g022:3813300:3813994 [2] NCCL INFO Using network IB
cn-g022:3813298:3813993 [0] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g022:3813298:3813993 [0] NCCL INFO Using network IB
cn-g022:3813300:3813994 [2] NCCL INFO ncclCommInitRankConfig comm 0x56518e8f7e80 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0x2d4f9a4a14d6ee2 - Init START
cn-g022:3813298:3813993 [0] NCCL INFO ncclCommInitRankConfig comm 0x55fc940dd940 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0x2d4f9a4a14d6ee2 - Init START
cn-g022:3813300:3813994 [2] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g022:3813301:3813996 [3] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g022:3813299:3813995 [1] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g022:3813298:3813993 [0] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g022:3813301:3813996 [3] NCCL INFO Bootstrap timings total 0.031928 (create 0.000044, send 0.000155, recv 0.019756, ring 0.005997, delay 0.000001)
cn-g022:3813300:3813994 [2] NCCL INFO Bootstrap timings total 0.016131 (create 0.000059, send 0.000197, recv 0.000244, ring 0.009124, delay 0.000001)
cn-g022:3813298:3813993 [0] NCCL INFO Bootstrap timings total 0.012334 (create 0.000044, send 0.000153, recv 0.000155, ring 0.000050, delay 0.000000)
cn-g022:3813299:3813995 [1] NCCL INFO Bootstrap timings total 0.048069 (create 0.000050, send 0.000160, recv 0.032140, ring 0.002641, delay 0.000000)
cn-g022:3813300:3813994 [2] NCCL INFO NVLS multicast support is not available on dev 2
cn-g022:3813301:3813996 [3] NCCL INFO NVLS multicast support is not available on dev 3
cn-g022:3813298:3813993 [0] NCCL INFO NVLS multicast support is not available on dev 0
cn-g022:3813299:3813995 [1] NCCL INFO Setting affinity for GPU 1 to ffff
cn-g022:3813299:3813995 [1] NCCL INFO NVLS multicast support is not available on dev 1
cn-g022:3813299:3813995 [1] NCCL INFO comm 0x56501a085d20 rank 1 nRanks 4 nNodes 1 localRanks 4 localRank 1 MNNVL 0
cn-g022:3813300:3813994 [2] NCCL INFO comm 0x56518e8f7e80 rank 2 nRanks 4 nNodes 1 localRanks 4 localRank 2 MNNVL 0
cn-g022:3813298:3813993 [0] NCCL INFO comm 0x55fc940dd940 rank 0 nRanks 4 nNodes 1 localRanks 4 localRank 0 MNNVL 0
cn-g022:3813301:3813996 [3] NCCL INFO comm 0x562857c84bc0 rank 3 nRanks 4 nNodes 1 localRanks 4 localRank 3 MNNVL 0
cn-g022:3813299:3813995 [1] NCCL INFO Trees [0] 3/-1/-1->1->2 [1] 3/-1/-1->1->2 [2] 3/-1/-1->1->2 [3] 3/-1/-1->1->2 [4] 2/-1/-1->1->0 [5] 2/-1/-1->1->0 [6] 2/-1/-1->1->0 [7] 2/-1/-1->1->0 [8] -1/-1/-1->1->3 [9] -1/-1/-1->1->3 [10] -1/-1/-1->1->3 [11] -1/-1/-1->1->3 [12] 3/-1/-1->1->2 [13] 3/-1/-1->1->2 [14] 3/-1/-1->1->2 [15] 3/-1/-1->1->2 [16] 2/-1/-1->1->0 [17] 2/-1/-1->1->0 [18] 2/-1/-1->1->0 [19] 2/-1/-1->1->0 [20] -1/-1/-1->1->3 [21] -1/-1/-1->1->3 [22] -1/-1/-1->1->3 [23] -1/-1/-1->1->3
cn-g022:3813299:3813995 [1] NCCL INFO P2P Chunksize set to 524288
cn-g022:3813300:3813994 [2] NCCL INFO Trees [0] 1/-1/-1->2->0 [1] 1/-1/-1->2->0 [2] 1/-1/-1->2->0 [3] 1/-1/-1->2->0 [4] 3/-1/-1->2->1 [5] 3/-1/-1->2->1 [6] 3/-1/-1->2->1 [7] 3/-1/-1->2->1 [8] 0/-1/-1->2->-1 [9] 0/-1/-1->2->-1 [10] 0/-1/-1->2->-1 [11] 0/-1/-1->2->-1 [12] 1/-1/-1->2->0 [13] 1/-1/-1->2->0 [14] 1/-1/-1->2->0 [15] 1/-1/-1->2->0 [16] 3/-1/-1->2->1 [17] 3/-1/-1->2->1 [18] 3/-1/-1->2->1 [19] 3/-1/-1->2->1 [20] 0/-1/-1->2->-1 [21] 0/-1/-1->2->-1 [22] 0/-1/-1->2->-1 [23] 0/-1/-1->2->-1
cn-g022:3813298:3813993 [0] NCCL INFO Channel 00/24 : 0 2 1 3
cn-g022:3813298:3813993 [0] NCCL INFO Channel 01/24 : 0 2 3 1
cn-g022:3813301:3813996 [3] NCCL INFO Trees [0] -1/-1/-1->3->1 [1] -1/-1/-1->3->1 [2] -1/-1/-1->3->1 [3] -1/-1/-1->3->1 [4] -1/-1/-1->3->2 [5] -1/-1/-1->3->2 [6] -1/-1/-1->3->2 [7] -1/-1/-1->3->2 [8] 1/-1/-1->3->0 [9] 1/-1/-1->3->0 [10] 1/-1/-1->3->0 [11] 1/-1/-1->3->0 [12] -1/-1/-1->3->1 [13] -1/-1/-1->3->1 [14] -1/-1/-1->3->1 [15] -1/-1/-1->3->1 [16] -1/-1/-1->3->2 [17] -1/-1/-1->3->2 [18] -1/-1/-1->3->2 [19] -1/-1/-1->3->2 [20] 1/-1/-1->3->0 [21] 1/-1/-1->3->0 [22] 1/-1/-1->3->0 [23] 1/-1/-1->3->0
cn-g022:3813301:3813996 [3] NCCL INFO P2P Chunksize set to 524288
cn-g022:3813300:3813994 [2] NCCL INFO P2P Chunksize set to 524288
cn-g022:3813299:3814012 [1] NCCL INFO [Proxy Service] Device 1 CPU core 13
cn-g022:3813298:3813993 [0] NCCL INFO Channel 02/24 : 0 1 3 2
cn-g022:3813301:3814014 [3] NCCL INFO [Proxy Service] Device 3 CPU core 8
cn-g022:3813299:3814013 [1] NCCL INFO [Proxy Service UDS] Device 1 CPU core 1
cn-g022:3813298:3813993 [0] NCCL INFO Channel 03/24 : 0 1 2 3
cn-g022:3813298:3813993 [0] NCCL INFO Channel 04/24 : 0 3 2 1
cn-g022:3813300:3814016 [2] NCCL INFO [Proxy Service] Device 2 CPU core 9
cn-g022:3813301:3814015 [3] NCCL INFO [Proxy Service UDS] Device 3 CPU core 7
cn-g022:3813299:3813995 [1] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g022:3813299:3813995 [1] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g022:3813298:3813993 [0] NCCL INFO Channel 05/24 : 0 3 1 2
cn-g022:3813298:3813993 [0] NCCL INFO Channel 06/24 : 0 2 1 3
cn-g022:3813300:3814017 [2] NCCL INFO [Proxy Service UDS] Device 2 CPU core 4
cn-g022:3813301:3813996 [3] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g022:3813301:3813996 [3] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g022:3813298:3813993 [0] NCCL INFO Channel 07/24 : 0 2 3 1
cn-g022:3813298:3813993 [0] NCCL INFO Channel 08/24 : 0 1 3 2
cn-g022:3813300:3813994 [2] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g022:3813298:3813993 [0] NCCL INFO Channel 09/24 : 0 1 2 3
cn-g022:3813298:3813993 [0] NCCL INFO Channel 10/24 : 0 3 2 1
cn-g022:3813300:3813994 [2] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g022:3813298:3813993 [0] NCCL INFO Channel 11/24 : 0 3 1 2
cn-g022:3813298:3813993 [0] NCCL INFO Channel 12/24 : 0 2 1 3
cn-g022:3813298:3813993 [0] NCCL INFO Channel 13/24 : 0 2 3 1
cn-g022:3813298:3813993 [0] NCCL INFO Channel 14/24 : 0 1 3 2
cn-g022:3813298:3813993 [0] NCCL INFO Channel 15/24 : 0 1 2 3
cn-g022:3813298:3813993 [0] NCCL INFO Channel 16/24 : 0 3 2 1
cn-g022:3813298:3813993 [0] NCCL INFO Channel 17/24 : 0 3 1 2
cn-g022:3813298:3813993 [0] NCCL INFO Channel 18/24 : 0 2 1 3
cn-g022:3813298:3813993 [0] NCCL INFO Channel 19/24 : 0 2 3 1
cn-g022:3813298:3813993 [0] NCCL INFO Channel 20/24 : 0 1 3 2
cn-g022:3813298:3813993 [0] NCCL INFO Channel 21/24 : 0 1 2 3
cn-g022:3813298:3813993 [0] NCCL INFO Channel 22/24 : 0 3 2 1
cn-g022:3813298:3813993 [0] NCCL INFO Channel 23/24 : 0 3 1 2
cn-g022:3813298:3813993 [0] NCCL INFO Trees [0] 2/-1/-1->0->-1 [1] 2/-1/-1->0->-1 [2] 2/-1/-1->0->-1 [3] 2/-1/-1->0->-1 [4] 1/-1/-1->0->-1 [5] 1/-1/-1->0->-1 [6] 1/-1/-1->0->-1 [7] 1/-1/-1->0->-1 [8] 3/-1/-1->0->2 [9] 3/-1/-1->0->2 [10] 3/-1/-1->0->2 [11] 3/-1/-1->0->2 [12] 2/-1/-1->0->-1 [13] 2/-1/-1->0->-1 [14] 2/-1/-1->0->-1 [15] 2/-1/-1->0->-1 [16] 1/-1/-1->0->-1 [17] 1/-1/-1->0->-1 [18] 1/-1/-1->0->-1 [19] 1/-1/-1->0->-1 [20] 3/-1/-1->0->2 [21] 3/-1/-1->0->2 [22] 3/-1/-1->0->2 [23] 3/-1/-1->0->2
cn-g022:3813298:3813993 [0] NCCL INFO P2P Chunksize set to 524288
cn-g022:3813298:3813993 [0] NCCL INFO Check P2P Type intraNodeP2pSupport 1 directMode 0
cn-g022:3813298:3814018 [0] NCCL INFO [Proxy Service] Device 0 CPU core 9
cn-g022:3813298:3814019 [0] NCCL INFO [Proxy Service UDS] Device 0 CPU core 14
cn-g022:3813298:3813993 [0] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g022:3813298:3813993 [0] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g022:3813298:3813993 [0] NCCL INFO CC Off, workFifoBytes 1048576
cn-g022:3813298:3813993 [0] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g022:3813298:3813993 [0] NCCL INFO ncclCommInitRankConfig comm 0x55fc940dd940 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0x2d4f9a4a14d6ee2 - Init COMPLETE
cn-g022:3813300:3813994 [2] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g022:3813300:3813994 [2] NCCL INFO ncclCommInitRankConfig comm 0x56518e8f7e80 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0x2d4f9a4a14d6ee2 - Init COMPLETE
cn-g022:3813301:3813996 [3] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g022:3813301:3813996 [3] NCCL INFO ncclCommInitRankConfig comm 0x562857c84bc0 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0x2d4f9a4a14d6ee2 - Init COMPLETE
cn-g022:3813299:3813995 [1] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g022:3813299:3813995 [1] NCCL INFO ncclCommInitRankConfig comm 0x56501a085d20 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0x2d4f9a4a14d6ee2 - Init COMPLETE
cn-g022:3813298:3813993 [0] NCCL INFO Init timings - ncclCommInitRankConfig: rank 0 nranks 4 total 0.54 (kernels 0.15, alloc 0.07, bootstrap 0.02, allgathers 0.17, topo 0.06, graphs 0.00, connections 0.02, rest 0.04)
cn-g022:3813300:3813994 [2] NCCL INFO Init timings - ncclCommInitRankConfig: rank 2 nranks 4 total 0.52 (kernels 0.13, alloc 0.06, bootstrap 0.02, allgathers 0.03, topo 0.06, graphs 0.00, connections 0.06, rest 0.16)
cn-g022:3813301:3813996 [3] NCCL INFO Init timings - ncclCommInitRankConfig: rank 3 nranks 4 total 0.52 (kernels 0.12, alloc 0.03, bootstrap 0.04, allgathers 0.04, topo 0.06, graphs 0.00, connections 0.05, rest 0.17)
cn-g022:3813299:3813995 [1] NCCL INFO Init timings - ncclCommInitRankConfig: rank 1 nranks 4 total 0.53 (kernels 0.12, alloc 0.03, bootstrap 0.06, allgathers 0.00, topo 0.06, graphs 0.00, connections 0.05, rest 0.20)
cn-g022:3813298:3814020 [0] NCCL INFO Channel 02/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 01/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 03/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 00/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 03/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 03/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 08/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 03/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 05/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 07/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 09/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 06/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 09/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 09/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 14/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 09/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 11/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 13/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 15/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 12/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 15/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 15/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 20/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 15/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 17/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 19/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 21/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 18/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 21/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 21/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 21/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 23/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 01/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 02/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 05/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 00/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 02/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 00/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 01/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 05/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 07/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 06/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 06/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 08/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 11/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 08/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 07/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 11/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 13/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 12/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 12/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 14/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 17/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 14/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 13/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 17/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 19/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 18/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 18/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 20/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 23/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 20/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 19/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 23/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 02/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 01/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 04/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 04/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 00/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 04/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 08/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 05/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 04/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 07/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 10/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 10/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 06/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 10/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 14/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 11/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 10/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 13/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 16/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 16/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 12/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 16/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 20/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 17/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 16/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 19/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g022:3813301:3814022 [3] NCCL INFO Channel 22/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 22/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 18/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814023 [1] NCCL INFO Channel 22/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Channel 23/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g022:3813300:3814021 [2] NCCL INFO Channel 22/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g022:3813298:3814020 [0] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g022:3813299:3814023 [1] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g022:3813301:3814022 [3] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g022:3813300:3814021 [2] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
Model parameters - Total: 7,356,420,096, Trainable: 1,918,238,720
Trainable percentage: 26.08%
Starting training...
[2025-07-07 18:22:49,067] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed info: version=0.15.4, git-hash=unknown, git-branch=unknown
[2025-07-07 18:22:49,070] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
Evaluation strategy: steps
Eval steps: 5000Evaluation strategy: steps

Metric for best model: eval_lossEval steps: 5000

Metric for best model: eval_loss
cn-g022:3813298:3813298 [0] NCCL INFO Comm config Blocking set to 1
cn-g022:3813298:3814084 [0] NCCL INFO Using network IB
cn-g022:3813298:3814084 [0] NCCL INFO ncclCommInitRankConfig comm 0x55fc98a9de40 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0x9f37bcd776a743d - Init START
cn-g022:3813300:3813300 [2] NCCL INFO Comm config Blocking set to 1
cn-g022:3813300:3814090 [2] NCCL INFO Using network IB
cn-g022:3813300:3814090 [2] NCCL INFO ncclCommInitRankConfig comm 0x56518c477d00 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0x9f37bcd776a743d - Init START
cn-g022:3813301:3813301 [3] NCCL INFO Comm config Blocking set to 1
cn-g022:3813301:3814093 [3] NCCL INFO Using network IB
cn-g022:3813301:3814093 [3] NCCL INFO ncclCommInitRankConfig comm 0x562834092200 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0x9f37bcd776a743d - Init START
cn-g022:3813299:3813299 [1] NCCL INFO Comm config Blocking set to 1
cn-g022:3813299:3814096 [1] NCCL INFO Using network IB
cn-g022:3813299:3814096 [1] NCCL INFO ncclCommInitRankConfig comm 0x5650053a5100 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0x9f37bcd776a743d - Init START
cn-g022:3813301:3814093 [3] NCCL INFO Bootstrap timings total 0.072353 (create 0.000051, send 0.000101, recv 0.000125, ring 0.070953, delay 0.000000)
cn-g022:3813299:3814096 [1] NCCL INFO Bootstrap timings total 0.000876 (create 0.000044, send 0.000328, recv 0.000144, ring 0.000095, delay 0.000000)
cn-g022:3813298:3814084 [0] NCCL INFO Bootstrap timings total 1.650662 (create 0.000035, send 0.000095, recv 1.650184, ring 0.000040, delay 0.000000)
cn-g022:3813300:3814090 [2] NCCL INFO Bootstrap timings total 0.142150 (create 0.000051, send 0.000098, recv 0.070880, ring 0.000088, delay 0.000000)
cn-g022:3813299:3814096 [1] NCCL INFO Setting affinity for GPU 1 to ffff
cn-g022:3813301:3814093 [3] NCCL INFO NVLS multicast support is not available on dev 3
cn-g022:3813300:3814090 [2] NCCL INFO NVLS multicast support is not available on dev 2
cn-g022:3813298:3814084 [0] NCCL INFO NVLS multicast support is not available on dev 0
cn-g022:3813299:3814096 [1] NCCL INFO NVLS multicast support is not available on dev 1
cn-g022:3813299:3814096 [1] NCCL INFO comm 0x5650053a5100 rank 1 nRanks 4 nNodes 1 localRanks 4 localRank 1 MNNVL 0
cn-g022:3813301:3814093 [3] NCCL INFO comm 0x562834092200 rank 3 nRanks 4 nNodes 1 localRanks 4 localRank 3 MNNVL 0
cn-g022:3813300:3814090 [2] NCCL INFO comm 0x56518c477d00 rank 2 nRanks 4 nNodes 1 localRanks 4 localRank 2 MNNVL 0
cn-g022:3813298:3814084 [0] NCCL INFO comm 0x55fc98a9de40 rank 0 nRanks 4 nNodes 1 localRanks 4 localRank 0 MNNVL 0
cn-g022:3813299:3814096 [1] NCCL INFO Trees [0] 3/-1/-1->1->2 [1] 3/-1/-1->1->2 [2] 3/-1/-1->1->2 [3] 3/-1/-1->1->2 [4] 2/-1/-1->1->0 [5] 2/-1/-1->1->0 [6] 2/-1/-1->1->0 [7] 2/-1/-1->1->0 [8] -1/-1/-1->1->3 [9] -1/-1/-1->1->3 [10] -1/-1/-1->1->3 [11] -1/-1/-1->1->3 [12] 3/-1/-1->1->2 [13] 3/-1/-1->1->2 [14] 3/-1/-1->1->2 [15] 3/-1/-1->1->2 [16] 2/-1/-1->1->0 [17] 2/-1/-1->1->0 [18] 2/-1/-1->1->0 [19] 2/-1/-1->1->0 [20] -1/-1/-1->1->3 [21] -1/-1/-1->1->3 [22] -1/-1/-1->1->3 [23] -1/-1/-1->1->3
cn-g022:3813299:3814096 [1] NCCL INFO P2P Chunksize set to 524288
cn-g022:3813301:3814093 [3] NCCL INFO Trees [0] -1/-1/-1->3->1 [1] -1/-1/-1->3->1 [2] -1/-1/-1->3->1 [3] -1/-1/-1->3->1 [4] -1/-1/-1->3->2 [5] -1/-1/-1->3->2 [6] -1/-1/-1->3->2 [7] -1/-1/-1->3->2 [8] 1/-1/-1->3->0 [9] 1/-1/-1->3->0 [10] 1/-1/-1->3->0 [11] 1/-1/-1->3->0 [12] -1/-1/-1->3->1 [13] -1/-1/-1->3->1 [14] -1/-1/-1->3->1 [15] -1/-1/-1->3->1 [16] -1/-1/-1->3->2 [17] -1/-1/-1->3->2 [18] -1/-1/-1->3->2 [19] -1/-1/-1->3->2 [20] 1/-1/-1->3->0 [21] 1/-1/-1->3->0 [22] 1/-1/-1->3->0 [23] 1/-1/-1->3->0
cn-g022:3813301:3814093 [3] NCCL INFO P2P Chunksize set to 524288
cn-g022:3813300:3814090 [2] NCCL INFO Trees [0] 1/-1/-1->2->0 [1] 1/-1/-1->2->0 [2] 1/-1/-1->2->0 [3] 1/-1/-1->2->0 [4] 3/-1/-1->2->1 [5] 3/-1/-1->2->1 [6] 3/-1/-1->2->1 [7] 3/-1/-1->2->1 [8] 0/-1/-1->2->-1 [9] 0/-1/-1->2->-1 [10] 0/-1/-1->2->-1 [11] 0/-1/-1->2->-1 [12] 1/-1/-1->2->0 [13] 1/-1/-1->2->0 [14] 1/-1/-1->2->0 [15] 1/-1/-1->2->0 [16] 3/-1/-1->2->1 [17] 3/-1/-1->2->1 [18] 3/-1/-1->2->1 [19] 3/-1/-1->2->1 [20] 0/-1/-1->2->-1 [21] 0/-1/-1->2->-1 [22] 0/-1/-1->2->-1 [23] 0/-1/-1->2->-1
cn-g022:3813300:3814090 [2] NCCL INFO P2P Chunksize set to 524288
cn-g022:3813298:3814084 [0] NCCL INFO Channel 00/24 : 0 2 1 3
cn-g022:3813299:3814097 [1] NCCL INFO [Proxy Service] Device 1 CPU core 12
cn-g022:3813301:3814100 [3] NCCL INFO [Proxy Service UDS] Device 3 CPU core 0
cn-g022:3813298:3814084 [0] NCCL INFO Channel 01/24 : 0 2 3 1
cn-g022:3813300:3814102 [2] NCCL INFO [Proxy Service UDS] Device 2 CPU core 3
cn-g022:3813299:3814098 [1] NCCL INFO [Proxy Service UDS] Device 1 CPU core 8
cn-g022:3813301:3814099 [3] NCCL INFO [Proxy Service] Device 3 CPU core 4
cn-g022:3813298:3814084 [0] NCCL INFO Channel 02/24 : 0 1 3 2
cn-g022:3813300:3814101 [2] NCCL INFO [Proxy Service] Device 2 CPU core 4
cn-g022:3813298:3814084 [0] NCCL INFO Channel 03/24 : 0 1 2 3
cn-g022:3813298:3814084 [0] NCCL INFO Channel 04/24 : 0 3 2 1
cn-g022:3813298:3814084 [0] NCCL INFO Channel 05/24 : 0 3 1 2
cn-g022:3813298:3814084 [0] NCCL INFO Channel 06/24 : 0 2 1 3
cn-g022:3813298:3814084 [0] NCCL INFO Channel 07/24 : 0 2 3 1
cn-g022:3813298:3814084 [0] NCCL INFO Channel 08/24 : 0 1 3 2
cn-g022:3813298:3814084 [0] NCCL INFO Channel 09/24 : 0 1 2 3
cn-g022:3813299:3814096 [1] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g022:3813299:3814096 [1] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g022:3813298:3814084 [0] NCCL INFO Channel 10/24 : 0 3 2 1
cn-g022:3813298:3814084 [0] NCCL INFO Channel 11/24 : 0 3 1 2
cn-g022:3813301:3814093 [3] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g022:3813301:3814093 [3] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g022:3813300:3814090 [2] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g022:3813300:3814090 [2] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g022:3813298:3814084 [0] NCCL INFO Channel 12/24 : 0 2 1 3
cn-g022:3813298:3814084 [0] NCCL INFO Channel 13/24 : 0 2 3 1
cn-g022:3813298:3814084 [0] NCCL INFO Channel 14/24 : 0 1 3 2
cn-g022:3813298:3814084 [0] NCCL INFO Channel 15/24 : 0 1 2 3
cn-g022:3813298:3814084 [0] NCCL INFO Channel 16/24 : 0 3 2 1
cn-g022:3813298:3814084 [0] NCCL INFO Channel 17/24 : 0 3 1 2
cn-g022:3813298:3814084 [0] NCCL INFO Channel 18/24 : 0 2 1 3
cn-g022:3813298:3814084 [0] NCCL INFO Channel 19/24 : 0 2 3 1
cn-g022:3813298:3814084 [0] NCCL INFO Channel 20/24 : 0 1 3 2
cn-g022:3813298:3814084 [0] NCCL INFO Channel 21/24 : 0 1 2 3
cn-g022:3813298:3814084 [0] NCCL INFO Channel 22/24 : 0 3 2 1
cn-g022:3813298:3814084 [0] NCCL INFO Channel 23/24 : 0 3 1 2
cn-g022:3813298:3814084 [0] NCCL INFO Trees [0] 2/-1/-1->0->-1 [1] 2/-1/-1->0->-1 [2] 2/-1/-1->0->-1 [3] 2/-1/-1->0->-1 [4] 1/-1/-1->0->-1 [5] 1/-1/-1->0->-1 [6] 1/-1/-1->0->-1 [7] 1/-1/-1->0->-1 [8] 3/-1/-1->0->2 [9] 3/-1/-1->0->2 [10] 3/-1/-1->0->2 [11] 3/-1/-1->0->2 [12] 2/-1/-1->0->-1 [13] 2/-1/-1->0->-1 [14] 2/-1/-1->0->-1 [15] 2/-1/-1->0->-1 [16] 1/-1/-1->0->-1 [17] 1/-1/-1->0->-1 [18] 1/-1/-1->0->-1 [19] 1/-1/-1->0->-1 [20] 3/-1/-1->0->2 [21] 3/-1/-1->0->2 [22] 3/-1/-1->0->2 [23] 3/-1/-1->0->2
cn-g022:3813298:3814084 [0] NCCL INFO P2P Chunksize set to 524288
cn-g022:3813298:3814084 [0] NCCL INFO Check P2P Type intraNodeP2pSupport 1 directMode 0
cn-g022:3813298:3814106 [0] NCCL INFO [Proxy Service] Device 0 CPU core 3
cn-g022:3813298:3814107 [0] NCCL INFO [Proxy Service UDS] Device 0 CPU core 0
cn-g022:3813298:3814084 [0] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g022:3813298:3814084 [0] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g022:3813298:3814084 [0] NCCL INFO CC Off, workFifoBytes 1048576
cn-g022:3813298:3814084 [0] NCCL INFO ncclCommInitRankConfig comm 0x55fc98a9de40 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0x9f37bcd776a743d - Init COMPLETE
cn-g022:3813300:3814090 [2] NCCL INFO ncclCommInitRankConfig comm 0x56518c477d00 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0x9f37bcd776a743d - Init COMPLETE
cn-g022:3813300:3814090 [2] NCCL INFO Init timings - ncclCommInitRankConfig: rank 2 nranks 4 total 0.47 (kernels 0.00, alloc 0.00, bootstrap 0.16, allgathers 0.02, topo 0.06, graphs 0.00, connections 0.08, rest 0.15)
cn-g022:3813299:3814096 [1] NCCL INFO ncclCommInitRankConfig comm 0x5650053a5100 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0x9f37bcd776a743d - Init COMPLETE
cn-g022:3813301:3814093 [3] NCCL INFO ncclCommInitRankConfig comm 0x562834092200 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0x9f37bcd776a743d - Init COMPLETE
cn-g022:3813298:3814084 [0] NCCL INFO Init timings - ncclCommInitRankConfig: rank 0 nranks 4 total 1.98 (kernels 0.00, alloc 0.00, bootstrap 1.66, allgathers 0.18, topo 0.06, graphs 0.00, connections 0.03, rest 0.03)
cn-g022:3813300:3814108 [2] NCCL INFO Channel 01/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g022:3813299:3814096 [1] NCCL INFO Init timings - ncclCommInitRankConfig: rank 1 nranks 4 total 0.33 (kernels 0.00, alloc 0.00, bootstrap 0.01, allgathers 0.01, topo 0.06, graphs 0.00, connections 0.08, rest 0.17)
cn-g022:3813301:3814093 [3] NCCL INFO Init timings - ncclCommInitRankConfig: rank 3 nranks 4 total 0.41 (kernels 0.00, alloc 0.00, bootstrap 0.08, allgathers 0.03, topo 0.06, graphs 0.00, connections 0.08, rest 0.15)
cn-g022:3813300:3814108 [2] NCCL INFO Channel 03/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 02/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 03/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 00/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 07/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 03/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 05/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 03/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 09/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 08/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 09/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 06/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 13/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 09/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 11/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 09/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 15/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 14/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 15/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 12/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 19/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 15/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 17/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 15/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 21/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 20/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 21/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 18/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 21/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 23/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 21/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 00/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 02/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 02/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 00/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 01/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 06/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 05/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 01/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 05/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 08/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 08/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 06/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 07/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 12/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 11/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 07/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 11/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 14/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 14/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 12/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 13/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 18/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 17/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 13/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 17/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 20/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 20/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 18/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 19/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 23/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 19/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 23/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 00/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 04/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 02/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 01/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 04/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 05/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 04/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 04/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 06/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 10/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 08/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 07/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 10/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 11/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 10/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 10/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 12/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 16/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 14/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 13/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 16/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 17/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 16/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 16/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 18/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 22/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 20/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 19/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g022:3813300:3814108 [2] NCCL INFO Channel 22/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g022:3813298:3814109 [0] NCCL INFO Channel 23/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Channel 22/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g022:3813299:3814110 [1] NCCL INFO Channel 22/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g022:3813301:3814111 [3] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g022:3813300:3814108 [2] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g022:3813299:3814110 [1] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g022:3813298:3814109 [0] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
[2025-07-07 18:22:53,632] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Flops Profiler Enabled: False
[2025-07-07 18:22:53,642] [INFO] [logging.py:128:log_dist] [Rank 0] Using client Optimizer as basic optimizer
[2025-07-07 18:22:53,646] [INFO] [logging.py:128:log_dist] [Rank 0] Removing param_group that has no 'params' in the basic Optimizer
[2025-07-07 18:22:53,672] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Basic Optimizer = AdamW
[2025-07-07 18:22:53,676] [INFO] [utils.py:59:is_zero_supported_optimizer] Checking ZeRO support for optimizer=AdamW type=<class 'torch.optim.adamw.AdamW'>
[2025-07-07 18:22:53,681] [INFO] [logging.py:128:log_dist] [Rank 0] Creating torch.bfloat16 ZeRO stage 2 optimizer
[2025-07-07 18:22:53,685] [INFO] [stage_1_and_2.py:149:__init__] Reduce bucket size 5********
[2025-07-07 18:22:53,689] [INFO] [stage_1_and_2.py:150:__init__] Allgather bucket size 2********
[2025-07-07 18:22:53,693] [INFO] [stage_1_and_2.py:151:__init__] CPU Offload: False
[2025-07-07 18:22:53,696] [INFO] [stage_1_and_2.py:152:__init__] Round robin gradient partitioning: False
[2025-07-07 18:22:57,305] [INFO] [utils.py:781:see_memory_usage] Before initializing optimizer states
[2025-07-07 18:22:57,312] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 20.02 GB         CA 20.36 GB         Max_CA 20 GB 
[2025-07-07 18:22:57,316] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 42.18 GB, percent = 4.2%
[2025-07-07 18:22:57,485] [INFO] [utils.py:781:see_memory_usage] After initializing optimizer states
[2025-07-07 18:22:57,492] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 20.91 GB         CA 22.15 GB         Max_CA 22 GB 
[2025-07-07 18:22:57,497] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 42.18 GB, percent = 4.2%
[2025-07-07 18:22:57,500] [INFO] [stage_1_and_2.py:544:__init__] optimizer state initialized
[2025-07-07 18:22:57,668] [INFO] [utils.py:781:see_memory_usage] After initializing ZeRO optimizer
[2025-07-07 18:22:57,673] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 19.12 GB         CA 22.15 GB         Max_CA 22 GB 
[2025-07-07 18:22:57,677] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 42.18 GB, percent = 4.2%
[2025-07-07 18:22:57,683] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Final Optimizer = DeepSpeedZeroOptimizer
[2025-07-07 18:22:57,687] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed using configured LR scheduler = None
[2025-07-07 18:22:57,691] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed LR Scheduler = None
[2025-07-07 18:22:57,695] [INFO] [logging.py:128:log_dist] [Rank 0] step=0, skipped=0, lr=[0.0001], mom=[(0.9, 0.999)]
[2025-07-07 18:22:57,702] [INFO] [config.py:999:print] DeepSpeedEngine configuration:
[2025-07-07 18:22:57,706] [INFO] [config.py:1003:print]   activation_checkpointing_config  {
    "partition_activations": false, 
    "contiguous_memory_optimization": false, 
    "cpu_checkpointing": false, 
    "number_checkpoints": null, 
    "synchronize_checkpoint_boundary": false, 
    "profile": false
}
[2025-07-07 18:22:57,710] [INFO] [config.py:1003:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True, 'use_gds': False}
[2025-07-07 18:22:57,714] [INFO] [config.py:1003:print]   amp_enabled .................. False
[2025-07-07 18:22:57,718] [INFO] [config.py:1003:print]   amp_params ................... False
[2025-07-07 18:22:57,722] [INFO] [config.py:1003:print]   autotuning_config ............ {
    "enabled": false, 
    "start_step": null, 
    "end_step": null, 
    "metric_path": null, 
    "arg_mappings": null, 
    "metric": "throughput", 
    "model_info": null, 
    "results_dir": "autotuning_results", 
    "exps_dir": "autotuning_exps", 
    "overwrite": true, 
    "fast": true, 
    "start_profile_step": 3, 
    "end_profile_step": 5, 
    "tuner_type": "gridsearch", 
    "tuner_early_stopping": 5, 
    "tuner_num_trials": 50, 
    "model_info_path": null, 
    "mp_size": 1, 
    "max_train_batch_size": null, 
    "min_train_batch_size": 1, 
    "max_train_micro_batch_size_per_gpu": 1.024000e+03, 
    "min_train_micro_batch_size_per_gpu": 1, 
    "num_tuning_micro_batch_sizes": 3
}
[2025-07-07 18:22:57,725] [INFO] [config.py:1003:print]   bfloat16_enabled ............. True
[2025-07-07 18:22:57,729] [INFO] [config.py:1003:print]   bfloat16_immediate_grad_update  False
[2025-07-07 18:22:57,733] [INFO] [config.py:1003:print]   checkpoint_parallel_write_pipeline  False
[2025-07-07 18:22:57,737] [INFO] [config.py:1003:print]   checkpoint_tag_validation_enabled  True
[2025-07-07 18:22:57,742] [INFO] [config.py:1003:print]   checkpoint_tag_validation_fail  False
[2025-07-07 18:22:57,745] [INFO] [config.py:1003:print]   comms_config ................. <deepspeed.comm.config.DeepSpeedCommsConfig object at 0x7f44c007dde0>
[2025-07-07 18:22:57,750] [INFO] [config.py:1003:print]   communication_data_type ...... None
[2025-07-07 18:22:57,754] [INFO] [config.py:1003:print]   compression_config ........... {'weight_quantization': {'shared_parameters': {'enabled': False, 'quantizer_kernel': False, 'schedule_offset': 0, 'quantize_groups': 1, 'quantize_verbose': False, 'quantization_type': 'symmetric', 'quantize_weight_in_forward': False, 'rounding': 'nearest', 'fp16_mixed_quantize': False, 'quantize_change_ratio': 0.001}, 'different_groups': {}}, 'activation_quantization': {'shared_parameters': {'enabled': False, 'quantization_type': 'symmetric', 'range_calibration': 'dynamic', 'schedule_offset': 1000}, 'different_groups': {}}, 'sparse_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'row_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'head_pruning': {'shared_parameters': {'enabled': False, 'method': 'topk', 'schedule_offset': 1000}, 'different_groups': {}}, 'channel_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'layer_reduction': {'enabled': False}}
[2025-07-07 18:22:57,758] [INFO] [config.py:1003:print]   curriculum_enabled_legacy .... False
[2025-07-07 18:22:57,761] [INFO] [config.py:1003:print]   curriculum_params_legacy ..... False
[2025-07-07 18:22:57,766] [INFO] [config.py:1003:print]   data_efficiency_config ....... {'enabled': False, 'seed': 1234, 'data_sampling': {'enabled': False, 'num_epochs': 1000, 'num_workers': 0, 'curriculum_learning': {'enabled': False}}, 'data_routing': {'enabled': False, 'random_ltd': {'enabled': False, 'layer_token_lr_schedule': {'enabled': False}}}}
[2025-07-07 18:22:57,770] [INFO] [config.py:1003:print]   data_efficiency_enabled ...... False
[2025-07-07 18:22:57,774] [INFO] [config.py:1003:print]   dataloader_drop_last ......... False
[2025-07-07 18:22:57,778] [INFO] [config.py:1003:print]   disable_allgather ............ False
[2025-07-07 18:22:57,782] [INFO] [config.py:1003:print]   dump_state ................... False
[2025-07-07 18:22:57,786] [INFO] [config.py:1003:print]   dynamic_loss_scale_args ...... None
[2025-07-07 18:22:57,790] [INFO] [config.py:1003:print]   eigenvalue_enabled ........... False
[2025-07-07 18:22:57,794] [INFO] [config.py:1003:print]   eigenvalue_gas_boundary_resolution  1
[2025-07-07 18:22:57,798] [INFO] [config.py:1003:print]   eigenvalue_layer_name ........ bert.encoder.layer
[2025-07-07 18:22:57,803] [INFO] [config.py:1003:print]   eigenvalue_layer_num ......... 0
[2025-07-07 18:22:57,807] [INFO] [config.py:1003:print]   eigenvalue_max_iter .......... 100
[2025-07-07 18:22:57,811] [INFO] [config.py:1003:print]   eigenvalue_stability ......... 1e-06
[2025-07-07 18:22:57,815] [INFO] [config.py:1003:print]   eigenvalue_tol ............... 0.01
[2025-07-07 18:22:57,819] [INFO] [config.py:1003:print]   eigenvalue_verbose ........... False
[2025-07-07 18:22:57,824] [INFO] [config.py:1003:print]   elasticity_enabled ........... False
[2025-07-07 18:22:57,829] [INFO] [config.py:1003:print]   flops_profiler_config ........ {
    "enabled": false, 
    "recompute_fwd_factor": 0.0, 
    "profile_step": 1, 
    "module_depth": -1, 
    "top_modules": 1, 
    "detailed": true, 
    "output_file": null
}
[2025-07-07 18:22:57,833] [INFO] [config.py:1003:print]   fp16_auto_cast ............... None
[2025-07-07 18:22:57,837] [INFO] [config.py:1003:print]   fp16_enabled ................. False
[2025-07-07 18:22:57,841] [INFO] [config.py:1003:print]   fp16_master_weights_and_gradients  False
[2025-07-07 18:22:57,844] [INFO] [config.py:1003:print]   global_rank .................. 0
[2025-07-07 18:22:57,849] [INFO] [config.py:1003:print]   grad_accum_dtype ............. None
[2025-07-07 18:22:57,853] [INFO] [config.py:1003:print]   gradient_accumulation_steps .. 4
[2025-07-07 18:22:57,857] [INFO] [config.py:1003:print]   gradient_clipping ............ 1.0
[2025-07-07 18:22:57,861] [INFO] [config.py:1003:print]   gradient_predivide_factor .... 1.0
[2025-07-07 18:22:57,864] [INFO] [config.py:1003:print]   graph_harvesting ............. False
[2025-07-07 18:22:57,868] [INFO] [config.py:1003:print]   hybrid_engine ................ enabled=False max_out_tokens=512 inference_tp_size=1 release_inference_cache=False pin_parameters=True tp_gather_partition_size=8
[2025-07-07 18:22:57,872] [INFO] [config.py:1003:print]   initial_dynamic_scale ........ 1
[2025-07-07 18:22:57,876] [INFO] [config.py:1003:print]   load_universal_checkpoint .... False
[2025-07-07 18:22:57,880] [INFO] [config.py:1003:print]   loss_scale ................... 1.0
[2025-07-07 18:22:57,884] [INFO] [config.py:1003:print]   memory_breakdown ............. False
[2025-07-07 18:22:57,888] [INFO] [config.py:1003:print]   mics_hierarchial_params_gather  False
[2025-07-07 18:22:57,892] [INFO] [config.py:1003:print]   mics_shard_size .............. -1
[2025-07-07 18:22:57,897] [INFO] [config.py:1003:print]   monitor_config ............... tensorboard=TensorBoardConfig(enabled=False, output_path='', job_name='DeepSpeedJobName') comet=CometConfig(enabled=False, samples_log_interval=100, project=None, workspace=None, api_key=None, experiment_name=None, experiment_key=None, online=None, mode=None) wandb=WandbConfig(enabled=False, group=None, team=None, project='deepspeed') csv_monitor=CSVConfig(enabled=False, output_path='', job_name='DeepSpeedJobName')
[2025-07-07 18:22:57,901] [INFO] [config.py:1003:print]   nebula_config ................ {
    "enabled": false, 
    "persistent_storage_path": null, 
    "persistent_time_interval": 100, 
    "num_of_version_in_retention": 2, 
    "enable_nebula_load": true, 
    "load_path": null
}
[2025-07-07 18:22:57,906] [INFO] [config.py:1003:print]   optimizer_legacy_fusion ...... False
[2025-07-07 18:22:57,911] [INFO] [config.py:1003:print]   optimizer_name ............... None
[2025-07-07 18:22:57,916] [INFO] [config.py:1003:print]   optimizer_params ............. None
[2025-07-07 18:22:57,921] [INFO] [config.py:1003:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0, 'pipe_partitioned': True, 'grad_partitioned': True}
[2025-07-07 18:22:57,926] [INFO] [config.py:1003:print]   pld_enabled .................. False
[2025-07-07 18:22:57,931] [INFO] [config.py:1003:print]   pld_params ................... False
[2025-07-07 18:22:57,935] [INFO] [config.py:1003:print]   prescale_gradients ........... False
[2025-07-07 18:22:57,939] [INFO] [config.py:1003:print]   scheduler_name ............... None
[2025-07-07 18:22:57,943] [INFO] [config.py:1003:print]   scheduler_params ............. None
[2025-07-07 18:22:57,947] [INFO] [config.py:1003:print]   seq_parallel_communication_data_type  torch.float32
[2025-07-07 18:22:57,951] [INFO] [config.py:1003:print]   sparse_attention ............. None
[2025-07-07 18:22:57,955] [INFO] [config.py:1003:print]   sparse_gradients_enabled ..... False
[2025-07-07 18:22:57,960] [INFO] [config.py:1003:print]   steps_per_print .............. inf
[2025-07-07 18:22:57,964] [INFO] [config.py:1003:print]   timers_config ................ enabled=True synchronized=True
[2025-07-07 18:22:57,968] [INFO] [config.py:1003:print]   train_batch_size ............. 16
[2025-07-07 18:22:57,972] [INFO] [config.py:1003:print]   train_micro_batch_size_per_gpu  1
[2025-07-07 18:22:57,977] [INFO] [config.py:1003:print]   use_data_before_expert_parallel_  False
[2025-07-07 18:22:57,981] [INFO] [config.py:1003:print]   use_node_local_storage ....... False
[2025-07-07 18:22:57,985] [INFO] [config.py:1003:print]   wall_clock_breakdown ......... False
[2025-07-07 18:22:57,990] [INFO] [config.py:1003:print]   weight_quantization_config ... None
[2025-07-07 18:22:57,995] [INFO] [config.py:1003:print]   world_size ................... 4
[2025-07-07 18:22:57,999] [INFO] [config.py:1003:print]   zero_allow_untested_optimizer  True
[2025-07-07 18:22:58,004] [INFO] [config.py:1003:print]   zero_config .................. stage=2 contiguous_gradients=True reduce_scatter=True reduce_bucket_size=5******** use_multi_rank_bucket_allreduce=True allgather_partitions=True allgather_bucket_size=2******** overlap_comm=True load_from_fp32_weights=True elastic_checkpoint=False offload_param=None offload_optimizer=None sub_group_size=1********0 cpu_offload_param=None cpu_offload_use_pin_memory=None cpu_offload=None prefetch_bucket_size=50000000 param_persistence_threshold=100000 model_persistence_threshold=9223372036854775807 max_live_parameters=1********0 max_reuse_distance=1********0 gather_16bit_weights_on_model_save=False use_all_reduce_for_fetch_params=False stage3_gather_fp16_weights_on_model_save=False ignore_unused_parameters=True legacy_stage1=False round_robin_gradients=False zero_hpz_partition_size=1 zero_quantized_weights=False zero_quantized_nontrainable_weights=False zero_quantized_gradients=False mics_shard_size=-1 mics_hierarchical_params_gather=False memory_efficient_linear=True pipeline_loading_checkpoint=False override_module_apply=True
[2025-07-07 18:22:58,008] [INFO] [config.py:1003:print]   zero_enabled ................. True
[2025-07-07 18:22:58,013] [INFO] [config.py:1003:print]   zero_force_ds_cpu_optimizer .. True
[2025-07-07 18:22:58,017] [INFO] [config.py:1003:print]   zero_optimization_stage ...... 2
[2025-07-07 18:22:58,021] [INFO] [config.py:989:print_user_config]   json = {
    "zero_optimization": {
        "stage": 2, 
        "allgather_partitions": true, 
        "allgather_bucket_size": 2.000000e+08, 
        "overlap_comm": true, 
        "reduce_scatter": true, 
        "contiguous_gradients": true
    }, 
    "gradient_accumulation_steps": 4, 
    "gradient_clipping": 1.0, 
    "steps_per_print": inf, 
    "train_batch_size": 16, 
    "train_micro_batch_size_per_gpu": 1, 
    "wall_clock_breakdown": false, 
    "bf16": {
        "enabled": true
    }, 
    "fp16": {
        "enabled": false
    }, 
    "zero_allow_untested_optimizer": true
}
{'loss': 1.4368, 'grad_norm': 6.577330112457275, 'learning_rate': 9.992e-05, 'epoch': 0.01}
{'loss': 1.3413, 'grad_norm': 1.806562900543213, 'learning_rate': 9.8e-05, 'epoch': 0.2}
{'loss': 1.2669, 'grad_norm': 1.8418515920639038, 'learning_rate': 9.6e-05, 'epoch': 0.4}
{'loss': 1.1521, 'grad_norm': 1.7007614374160767, 'learning_rate': 9.4e-05, 'epoch': 0.6}
{'loss': 1.0858, 'grad_norm': 1.5880428552627563, 'learning_rate': 9.2********000001e-05, 'epoch': 0.8}
{'loss': 1.0409, 'grad_norm': 1.4595365524291992, 'learning_rate': 9e-05, 'epoch': 1.0}
{'loss': 0.8948, 'grad_norm': 1.331572413444519, 'learning_rate': 8.8********000001e-05, 'epoch': 1.2}
{'loss': 0.8688, 'grad_norm': 1.3122098445892334, 'learning_rate': 8.6e-05, 'epoch': 1.4}
{'loss': 0.8102, 'grad_norm': 1.6359957456588745, 'learning_rate': 8.4e-05, 'epoch': 1.6}
{'loss': 0.8001, 'grad_norm': 1.3256783485412598, 'learning_rate': 8.2e-05, 'epoch': 1.8}
{'loss': 0.7273, 'grad_norm': 1.3895447254180908, 'learning_rate': 8e-05, 'epoch': 2.0}
{'loss': 0.5938, 'grad_norm': 1.2708992958068848, 'learning_rate': 7.8********000001e-05, 'epoch': 2.2}
{'loss': 0.6284, 'grad_norm': 1.4323066473007202, 'learning_rate': 7.6e-05, 'epoch': 2.4}
{'loss': 0.5972, 'grad_norm': 2.0445761680603027, 'learning_rate': 7.4e-05, 'epoch': 2.6}
{'loss': 0.5827, 'grad_norm': 1.2146183252334595, 'learning_rate': 7.2e-05, 'epoch': 2.8}
{'loss': 0.5611, 'grad_norm': 1.506158471107483, 'learning_rate': 7e-05, 'epoch': 3.0}
{'loss': 0.4699, 'grad_norm': 1.2073380947113037, 'learning_rate': 6.8********000001e-05, 'epoch': 3.2}
{'loss': 0.4595, 'grad_norm': 1.3929585218429565, 'learning_rate': 6.6e-05, 'epoch': 3.4}
{'loss': 0.4487, 'grad_norm': 1.1932791471481323, 'learning_rate': 6.4********000001e-05, 'epoch': 3.6}
{'loss': 0.4029, 'grad_norm': 1.1507785320281982, 'learning_rate': 6.2e-05, 'epoch': 3.8}
{'loss': 0.4007, 'grad_norm': 1.109811782836914, 'learning_rate': 6e-05, 'epoch': 4.0}
{'loss': 0.3681, 'grad_norm': 1.1033133268356323, 'learning_rate': 5.8e-05, 'epoch': 4.2}
{'loss': 0.3382, 'grad_norm': 1.1297688484191895, 'learning_rate': 5.6********0000006e-05, 'epoch': 4.4}
{'loss': 0.3083, 'grad_norm': 0.8749898076057434, 'learning_rate': 5.4********0000005e-05, 'epoch': 4.6}
{'loss': 0.2799, 'grad_norm': 1.0007977485656738, 'learning_rate': 5.2********0000004e-05, 'epoch': 4.8}
{'loss': 0.311, 'grad_norm': 0.861478865146637, 'learning_rate': 5e-05, 'epoch': 5.0}
{'loss': 0.2425, 'grad_norm': 0.7471625208854675, 'learning_rate': 4.8e-05, 'epoch': 5.2}
{'loss': 0.2573, 'grad_norm': 0.956953763961792, 'learning_rate': 4.6********000001e-05, 'epoch': 5.4}
{'loss': 0.2458, 'grad_norm': 0.8539668321609497, 'learning_rate': 4.4********0000006e-05, 'epoch': 5.6}
{'loss': 0.2246, 'grad_norm': 0.9366992712020874, 'learning_rate': 4.2e-05, 'epoch': 5.8}
{'loss': 0.233, 'grad_norm': 0.6998581290245056, 'learning_rate': 4e-05, 'epoch': 6.0}
{'loss': 0.1882, 'grad_norm': 0.8355609178543091, 'learning_rate': 3.8e-05, 'epoch': 6.2}
{'loss': 0.1793, 'grad_norm': 1.0062161684036255, 'learning_rate': 3.6e-05, 'epoch': 6.4}
{'loss': 0.171, 'grad_norm': 0.9196571111679077, 'learning_rate': 3.4********0000007e-05, 'epoch': 6.6}
{'loss': 0.173, 'grad_norm': 0.9538785219192505, 'learning_rate': 3.2********0000005e-05, 'epoch': 6.8}
{'loss': 0.1522, 'grad_norm': 0.874276876449585, 'learning_rate': 3e-05, 'epoch': 7.0}
{'loss': 0.1275, 'grad_norm': 0.6395280957221985, 'learning_rate': 2.8********0000003e-05, 'epoch': 7.2}
{'loss': 0.1351, 'grad_norm': 1.0002806186676025, 'learning_rate': 2.6********0000002e-05, 'epoch': 7.4}
{'loss': 0.1194, 'grad_norm': 0.689862072467804, 'learning_rate': 2.4e-05, 'epoch': 7.6}
{'loss': 0.1184, 'grad_norm': 0.5886167883872986, 'learning_rate': 2.2********0000003e-05, 'epoch': 7.8}
{'loss': 0.1154, 'grad_norm': 0.5104950666427612, 'learning_rate': 2e-05, 'epoch': 8.0}
{'loss': 0.1023, 'grad_norm': 0.5060407519340515, 'learning_rate': 1.8e-05, 'epoch': 8.2}
{'loss': 0.0867, 'grad_norm': 0.4884108901023865, 'learning_rate': 1.6********0000003e-05, 'epoch': 8.4}
{'loss': 0.0883, 'grad_norm': 0.6315594911575317, 'learning_rate': 1.4********0000001e-05, 'epoch': 8.6}
{'loss': 0.0926, 'grad_norm': 0.7728333473205566, 'learning_rate': 1.2e-05, 'epoch': 8.8}
{'loss': 0.0684, 'grad_norm': 0.49677005410194397, 'learning_rate': 1e-05, 'epoch': 9.0}
{'loss': 0.0737, 'grad_norm': 0.5739369988441467, 'learning_rate': 8.********0000001e-06, 'epoch': 9.2}
{'loss': 0.0715, 'grad_norm': 0.589724063873291, 'learning_rate': 6e-06, 'epoch': 9.4}
{'loss': 0.0663, 'grad_norm': 0.3128906190395355, 'learning_rate': 4.********0000001e-06, 'epoch': 9.6}
{'loss': 0.067, 'grad_norm': 0.44491955637931824, 'learning_rate': 2.********00000003e-06, 'epoch': 9.8}
{'loss': 0.0712, 'grad_norm': 0.43449950218200684, 'learning_rate': 0.0, 'epoch': 10.0}
[2025-07-07 18:52:20,830] [INFO] [logging.py:128:log_dist] [Rank 0] [Torch] Checkpoint global_step1250 is about to be saved!
[2025-07-07 18:52:24,316] [INFO] [logging.py:128:log_dist] [Rank 0] Saving model checkpoint: data/output/mistral_lora_moe_economic_top1/checkpoint-1250/global_step1250/mp_rank_00_model_states.pt
[2025-07-07 18:52:24,320] [INFO] [torch_checkpoint_engine.py:21:save] [Torch] Saving data/output/mistral_lora_moe_economic_top1/checkpoint-1250/global_step1250/mp_rank_00_model_states.pt...
[2025-07-07 18:53:28,674] [INFO] [torch_checkpoint_engine.py:23:save] [Torch] Saved data/output/mistral_lora_moe_economic_top1/checkpoint-1250/global_step1250/mp_rank_00_model_states.pt.
[2025-07-07 18:53:28,700] [INFO] [torch_checkpoint_engine.py:21:save] [Torch] Saving data/output/mistral_lora_moe_economic_top1/checkpoint-1250/global_step1250/bf16_zero_pp_rank_0_mp_rank_00_optim_states.pt...
[2025-07-07 18:53:39,211] [INFO] [torch_checkpoint_engine.py:23:save] [Torch] Saved data/output/mistral_lora_moe_economic_top1/checkpoint-1250/global_step1250/bf16_zero_pp_rank_0_mp_rank_00_optim_states.pt.
[2025-07-07 18:53:39,219] [INFO] [engine.py:3536:_save_zero_checkpoint] zero checkpoint saved data/output/mistral_lora_moe_economic_top1/checkpoint-1250/global_step1250/bf16_zero_pp_rank_0_mp_rank_00_optim_states.pt
[2025-07-07 18:53:39,224] [INFO] [torch_checkpoint_engine.py:33:commit] [Torch] Checkpoint global_step1250 is ready now!
{'train_runtime': 1846.3522, 'train_samples_per_second': 10.832, 'train_steps_per_second': 0.677, 'train_loss': 0.40426397027969363, 'epoch': 10.0}
Training completed!
Running final evaluation...
Evaluation metrics at step 1250: {'eval_loss': 0.2750053107738495, 'eval_runtime': 3.7204, 'eval_samples_per_second': 33.061, 'eval_steps_per_second': 8.332}Evaluation metrics at step 1250: {'eval_loss': 0.2750053107738495, 'eval_runtime': 3.7205, 'eval_samples_per_second': 33.06, 'eval_steps_per_second': 8.332}Evaluation metrics at step 1250: {'eval_loss': 0.2750053107738495, 'eval_runtime': 3.7205, 'eval_samples_per_second': 33.06, 'eval_steps_per_second': 8.332}Evaluation metrics at step 1250: {'eval_loss': 0.2750053107738495, 'eval_runtime': 3.7069, 'eval_samples_per_second': 33.182, 'eval_steps_per_second': 8.363}



Final evaluation results: {'eval_loss': 0.2750053107738495, 'eval_runtime': 3.7069, 'eval_samples_per_second': 33.182, 'eval_steps_per_second': 8.363, 'epoch': 10.0}
cn-g022:3813301:3820811 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g022:3813301:3820811 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g022:3813301:3820811 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g022:3813301:3820811 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g022:3813301:3820811 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g022:3813301:3820811 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g022:3813299:3820813 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g022:3813301:3820811 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g022:3813301:3820811 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g022:3813299:3820813 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g022:3813301:3820811 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g022:3813299:3820813 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g022:3813301:3820811 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g022:3813301:3820811 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g022:3813299:3820813 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g022:3813301:3820811 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g022:3813301:3814014 [3] NCCL INFO misc/socket.cc:881 -> 3
cn-g022:3813300:3820815 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g022:3813299:3820813 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g022:3813299:3820813 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g022:3813300:3820815 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g022:3813300:3820815 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g022:3813299:3814012 [1] NCCL INFO misc/socket.cc:881 -> 3
cn-g022:3813300:3814016 [2] NCCL INFO misc/socket.cc:881 -> 3
cn-g022:3813299:3820813 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g022:3813300:3820815 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g022:3813300:3820815 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g022:3813299:3820813 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g022:3813299:3820813 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g022:3813300:3820815 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g022:3813299:3814012 [1] NCCL INFO misc/socket.cc:881 -> 3
cn-g022:3813301:3814014 [3] NCCL INFO misc/socket.cc:881 -> 3
cn-g022:3813300:3820815 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g022:3813299:3820813 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g022:3813299:3820813 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g022:3813300:3820815 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g022:3813300:3820815 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g022:3813299:3820813 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g022:3813300:3814016 [2] NCCL INFO misc/socket.cc:881 -> 3
cn-g022:3813301:3814014 [3] NCCL INFO misc/socket.cc:881 -> 3
cn-g022:3813300:3820815 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g022:3813300:3820815 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g022:3813300:3820815 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g022:3813301:3814099 [3] NCCL INFO [Service thread] Connection closed by localRank 1
cn-g022:3813300:3814101 [2] NCCL INFO [Service thread] Connection closed by localRank 1
cn-g022:3813298:3814106 [0] NCCL INFO [Service thread] Connection closed by localRank 1
Job complete

======== GPU REPORT ========

==============NVSMI LOG==============

Timestamp                                 : Mon Jul  7 19:17:04 2025
Driver Version                            : 535.247.01
CUDA Version                              : 12.2

Attached GPUs                             : 4
GPU ********:01:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 3813299
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 3364081 ms
            Is Running                    : 0
        Process ID                        : 3813301
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 3364774 ms
            Is Running                    : 0
        Process ID                        : 3813300
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 3364977 ms
            Is Running                    : 0
        Process ID                        : 3813298
            GPU Utilization               : 43 %
            Memory Utilization            : 15 %
            Max memory usage              : 40322 MiB
            Time                          : 3365297 ms
            Is Running                    : 0

GPU ********:41:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 3813299
            GPU Utilization               : 45 %
            Memory Utilization            : 15 %
            Max memory usage              : 40344 MiB
            Time                          : 3364080 ms
            Is Running                    : 0
        Process ID                        : 3813301
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 3364769 ms
            Is Running                    : 0
        Process ID                        : 3813300
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 3364973 ms
            Is Running                    : 0
        Process ID                        : 3813298
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 3365292 ms
            Is Running                    : 0

GPU ********:81:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 3813299
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 3364079 ms
            Is Running                    : 0
        Process ID                        : 3813301
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 3364766 ms
            Is Running                    : 0
        Process ID                        : 3813300
            GPU Utilization               : 45 %
            Memory Utilization            : 15 %
            Max memory usage              : 40330 MiB
            Time                          : 3364970 ms
            Is Running                    : 0
        Process ID                        : 3813298
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 3365289 ms
            Is Running                    : 0

GPU ********:C1:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 3813299
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 3364078 ms
            Is Running                    : 0
        Process ID                        : 3813301
            GPU Utilization               : 44 %
            Memory Utilization            : 15 %
            Max memory usage              : 39756 MiB
            Time                          : 3364762 ms
            Is Running                    : 0
        Process ID                        : 3813300
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 3364967 ms
            Is Running                    : 0
        Process ID                        : 3813298
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 3365286 ms
            Is Running                    : 0

Mon Jul  7 19:17:16 2025       
+---------------------------------------------------------------------------------------+
| NVIDIA-SMI 535.247.01             Driver Version: 535.247.01   CUDA Version: 12.2     |
|-----------------------------------------+----------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |
|                                         |                      |               MIG M. |
|=========================================+======================+======================|
|   0  NVIDIA A100-SXM4-80GB          On  | ********:01:00.0 Off |                    0 |
| N/A   28C    P0              62W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   1  NVIDIA A100-SXM4-80GB          On  | ********:41:00.0 Off |                    0 |
| N/A   25C    P0              60W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   2  NVIDIA A100-SXM4-80GB          On  | ********:81:00.0 Off |                    0 |
| N/A   28C    P0              63W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   3  NVIDIA A100-SXM4-80GB          On  | ********:C1:00.0 Off |                    0 |
| N/A   27C    P0              62W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
                                                                                         
+---------------------------------------------------------------------------------------+
| Processes:                                                                            |
|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |
|        ID   ID                                                             Usage      |
|=======================================================================================|
|  No running processes found                                                           |
+---------------------------------------------------------------------------------------+

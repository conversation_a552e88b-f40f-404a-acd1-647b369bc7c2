[=== Module python/3.10 loaded ===]
[=== Module cudatoolkit/12.1.1 loaded ===]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
W0707 17:53:14.698000 2879247 torch/distributed/run.py:766] 
W0707 17:53:14.698000 2879247 torch/distributed/run.py:766] *****************************************
W0707 17:53:14.698000 2879247 torch/distributed/run.py:766] Setting OMP_NUM_THREADS environment variable for each process to be 1 in default, to avoid your system being overloaded, please further tune the variable for optimal performance in your application as needed. 
W0707 17:53:14.698000 2879247 torch/distributed/run.py:766] *****************************************
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
wandb: Currently logged in as: maryamha (sarath-chandar) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.20.1
wandb: Run data is saved locally in /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/run-20250707_175326-ab6k0rze
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run mistral-economic-moe
wandb: ⭐️ View project at https://wandb.ai/sarath-chandar/mistral-moe-training
wandb: 🚀 View run at https://wandb.ai/sarath-chandar/mistral-moe-training/runs/ab6k0rze

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:07<00:07,  7.96s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:07<00:07,  7.98s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:08<00:08,  8.07s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:08<00:08,  8.00s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.44s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.83s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.48s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.86s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.50s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.89s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.43s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:11<00:00,  5.82s/it]
Error executing job with overrides: []
Traceback (most recent call last):
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 70, in main
    expertmerger.compose()
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/mergoo/composers/composer_lora_moe.py", line 113, in compose
    assert self.config["router_layers"] == list(
AssertionError

Set the environment variable HYDRA_FULL_ERROR=1 for a complete stack trace.

  0%|          | 0/675 [00:00<?, ?it/s]
  0%|          | 0/675 [00:00<?, ?it/s]
100%|██████████| 675/675 [00:00<00:00, 59312.32it/s]
100%|██████████| 675/675 [00:00<00:00, 61513.42it/s]


  0%|          | 0/675 [00:00<?, ?it/s]
100%|██████████| 675/675 [00:00<00:00, 71202.54it/s]
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
W0707 17:53:41.363000 2879247 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 2879352 closing signal SIGTERM
W0707 17:53:41.369000 2879247 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 2879353 closing signal SIGTERM
W0707 17:53:41.375000 2879247 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 2879355 closing signal SIGTERM
E0707 17:53:42.112000 2879247 torch/distributed/elastic/multiprocessing/api.py:874] failed (exitcode: 1) local_rank: 2 (pid: 2879354) of binary: /home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/bin/python
Traceback (most recent call last):
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/bin/accelerate", line 8, in <module>
    sys.exit(main())
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/accelerate/commands/accelerate_cli.py", line 50, in main
    args.func(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/accelerate/commands/launch.py", line 1183, in launch_command
    deepspeed_launcher(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/accelerate/commands/launch.py", line 868, in deepspeed_launcher
    distrib_run.run(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/run.py", line 883, in run
    elastic_launch(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 139, in __call__
    return launch_agent(self._config, self._entrypoint, list(args))
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 270, in launch_agent
    raise ChildFailedError(
torch.distributed.elastic.multiprocessing.errors.ChildFailedError: 
============================================================
exmp_1.py FAILED
------------------------------------------------------------
Failures:
  <NO_OTHER_FAILURES>
------------------------------------------------------------
Root Cause (first observed failure):
[0]:
  time      : 2025-07-07_17:53:41
  host      : cn-g020.server.mila.quebec
  rank      : 2 (local_rank: 2)
  exitcode  : 1 (pid: 2879354)
  error_file: <N/A>
  traceback : To enable traceback see: https://pytorch.org/docs/stable/elastic/errors.html
============================================================

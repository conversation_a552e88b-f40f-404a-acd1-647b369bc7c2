[2025-07-07 17:53:09,686] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
Configuration:Configuration:Configuration:Configuration:



experiment_name: mistral-economic-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - economic
  notes: Training Mistral MoE model for economic with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-unsafe_economic
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-fiqa
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/polished_safe_data/train_safe_gpt4_qtype2_economic_crime_polished.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_economic_top1
  output_dir: data/output/mistral_lora_moe_economic_top1

experiment_name: mistral-economic-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - economic
  notes: Training Mistral MoE model for economic with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-unsafe_economic
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-fiqa
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/polished_safe_data/train_safe_gpt4_qtype2_economic_crime_polished.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_economic_top1
  output_dir: data/output/mistral_lora_moe_economic_top1

experiment_name: mistral-economic-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - economic
  notes: Training Mistral MoE model for economic with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-unsafe_economic
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-fiqa
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/polished_safe_data/train_safe_gpt4_qtype2_economic_crime_polished.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_economic_top1
  output_dir: data/output/mistral_lora_moe_economic_top1

experiment_name: mistral-economic-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - economic
  notes: Training Mistral MoE model for economic with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-unsafe_economic
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-fiqa
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/polished_safe_data/train_safe_gpt4_qtype2_economic_crime_polished.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_economic_top1
  output_dir: data/output/mistral_lora_moe_economic_top1

[2025-07-07 17:53:22,885] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-07 17:53:22,896] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-07 17:53:22,911] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-07 17:53:22,920] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-07 17:53:25,709] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-07-07 17:53:25,709] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-07-07 17:53:25,710] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-07-07 17:53:25,722] [INFO] [comm.py:683:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
[2025-07-07 17:53:25,736] [INFO] [comm.py:652:init_distributed] cdb=None
Creating model checkpoint...Creating model checkpoint...Creating model checkpoint...


MoE Layer Index : [*]MoE Layer Index : [*]MoE Layer Index : [*]


Creating model checkpoint...
MoE Layer Index : [*]
[2025-07-07 17:53:40,107][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-07-07 17:53:40,127][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-07-07 17:53:40,609][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
count_averaged_layers : 195
count_averaged_layers : 195count_router_layers : 480

count_router_layers : 480
count_total_router_layers : 480count_total_router_layers : 480

count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
Job complete

======== GPU REPORT ========

==============NVSMI LOG==============

Timestamp                                 : Mon Jul  7 17:53:43 2025
Driver Version                            : 535.247.01
CUDA Version                              : 12.2

Attached GPUs                             : 4
GPU ********:01:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2879354
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 3912 MiB
            Time                          : 18739 ms
            Is Running                    : 0
        Process ID                        : 2879352
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 19122 ms
            Is Running                    : 0
        Process ID                        : 2879355
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 19360 ms
            Is Running                    : 0
        Process ID                        : 2879353
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 19561 ms
            Is Running                    : 0

GPU ********:41:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2879354
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 4226 MiB
            Time                          : 18737 ms
            Is Running                    : 0
        Process ID                        : 2879352
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 19121 ms
            Is Running                    : 0
        Process ID                        : 2879355
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 19355 ms
            Is Running                    : 0
        Process ID                        : 2879353
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 19559 ms
            Is Running                    : 0

GPU ********:81:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2879354
            GPU Utilization               : 2 %
            Memory Utilization            : 0 %
            Max memory usage              : 4226 MiB
            Time                          : 18732 ms
            Is Running                    : 0
        Process ID                        : 2879352
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 19120 ms
            Is Running                    : 0
        Process ID                        : 2879355
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 19352 ms
            Is Running                    : 0
        Process ID                        : 2879353
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 19558 ms
            Is Running                    : 0

GPU ********:C1:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2879354
            GPU Utilization               : 2 %
            Memory Utilization            : 0 %
            Max memory usage              : 3628 MiB
            Time                          : 18728 ms
            Is Running                    : 0
        Process ID                        : 2879352
            GPU Utilization               : 1 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 19118 ms
            Is Running                    : 0
        Process ID                        : 2879355
            GPU Utilization               : 2 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 19349 ms
            Is Running                    : 0
        Process ID                        : 2879353
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 19557 ms
            Is Running                    : 0

Mon Jul  7 17:53:57 2025       
+---------------------------------------------------------------------------------------+
| NVIDIA-SMI 535.247.01             Driver Version: 535.247.01   CUDA Version: 12.2     |
|-----------------------------------------+----------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |
|                                         |                      |               MIG M. |
|=========================================+======================+======================|
|   0  NVIDIA A100-SXM4-80GB          On  | ********:01:00.0 Off |                    0 |
| N/A   28C    P0              62W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   1  NVIDIA A100-SXM4-80GB          On  | ********:41:00.0 Off |                    0 |
| N/A   25C    P0              62W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   2  NVIDIA A100-SXM4-80GB          On  | ********:81:00.0 Off |                    0 |
| N/A   28C    P0              65W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   3  NVIDIA A100-SXM4-80GB          On  | ********:C1:00.0 Off |                    0 |
| N/A   26C    P0              61W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
                                                                                         
+---------------------------------------------------------------------------------------+
| Processes:                                                                            |
|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |
|        ID   ID                                                             Usage      |
|=======================================================================================|
|  No running processes found                                                           |
+---------------------------------------------------------------------------------------+

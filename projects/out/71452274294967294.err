[=== Module python/3.10 loaded ===]
[=== Module cudatoolkit/12.1.1 loaded ===]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
W0707 16:08:31.118000 2855285 torch/distributed/run.py:766] 
W0707 16:08:31.118000 2855285 torch/distributed/run.py:766] *****************************************
W0707 16:08:31.118000 2855285 torch/distributed/run.py:766] Setting OMP_NUM_THREADS environment variable for each process to be 1 in default, to avoid your system being overloaded, please further tune the variable for optimal performance in your application as needed. 
W0707 16:08:31.118000 2855285 torch/distributed/run.py:766] *****************************************
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
wandb: Currently logged in as: maryamha (sarath-chandar) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.20.1
wandb: Run data is saved locally in /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/run-20250707_160842-2l3a0nfa
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run mistral-cybersecurity-moe
wandb: ⭐️ View project at https://wandb.ai/sarath-chandar/mistral-moe-training
wandb: 🚀 View run at https://wandb.ai/sarath-chandar/mistral-moe-training/runs/2l3a0nfa

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:13<00:13, 13.97s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:15<00:15, 15.47s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:15<00:15, 15.47s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:15<00:15, 15.46s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:21<00:00, 10.02s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:21<00:00, 10.85s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:21<00:00, 10.18s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:21<00:00, 10.17s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:21<00:00, 10.01s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:21<00:00, 10.99s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:21<00:00, 10.98s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:21<00:00, 10.62s/it]



  0%|          | 0/675 [00:00<?, ?it/s]
100%|██████████| 675/675 [00:00<00:00, 71993.77it/s]
  0%|          | 0/675 [00:00<?, ?it/s]
  0%|          | 0/675 [00:00<?, ?it/s]
  0%|          | 0/675 [00:00<?, ?it/s]

100%|██████████| 675/675 [00:00<00:00, 30869.05it/s]
100%|██████████| 675/675 [00:00<00:00, 31379.53it/s]
100%|██████████| 675/675 [00:00<00:00, 31057.33it/s]


Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:09<00:09,  9.64s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:12<00:12, 12.22s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:09<00:09,  9.45s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:09<00:09,  9.03s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  7.89s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  8.16s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_cybersecurity_top1 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.mlp.up_proj.gate.weight', 'model.layers.8.mlp.down_proj.gate.weight', 'model.layers.8.mlp.gate_proj.gate.weight', 'model.layers.8.mlp.up_proj.gate.weight', 'model.layers.9.mlp.down_proj.gate.weight', 'model.layers.9.mlp.gate_proj.gate.weight', 'model.layers.9.mlp.up_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.

Loading checkpoint shards: 100%|██████████| 2/2 [00:15<00:00,  7.67s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:15<00:00,  7.50s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:18<00:00,  8.83s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:15<00:00,  7.95s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:15<00:00,  7.74s/it]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(



Loading checkpoint shards: 100%|██████████| 2/2 [00:18<00:00,  9.35s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_cybersecurity_top1 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.mlp.up_proj.gate.weight', 'model.layers.8.mlp.down_proj.gate.weight', 'model.layers.8.mlp.gate_proj.gate.weight', 'model.layers.8.mlp.up_proj.gate.weight', 'model.layers.9.mlp.down_proj.gate.weight', 'model.layers.9.mlp.gate_proj.gate.weight', 'model.layers.9.mlp.up_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_cybersecurity_top1 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.mlp.up_proj.gate.weight', 'model.layers.8.mlp.down_proj.gate.weight', 'model.layers.8.mlp.gate_proj.gate.weight', 'model.layers.8.mlp.up_proj.gate.weight', 'model.layers.9.mlp.down_proj.gate.weight', 'model.layers.9.mlp.gate_proj.gate.weight', 'model.layers.9.mlp.up_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_cybersecurity_top1 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.mlp.up_proj.gate.weight', 'model.layers.8.mlp.down_proj.gate.weight', 'model.layers.8.mlp.gate_proj.gate.weight', 'model.layers.8.mlp.up_proj.gate.weight', 'model.layers.9.mlp.down_proj.gate.weight', 'model.layers.9.mlp.gate_proj.gate.weight', 'model.layers.9.mlp.up_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(

Map:   0%|          | 0/5834 [00:00<?, ? examples/s]Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank3]:[W707 16:10:41.788524123 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 3]  using GPU 3 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.

Map:  17%|█▋        | 1000/5834 [00:00<00:01, 3992.29 examples/s]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank1]:[W707 16:10:41.825995226 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 1]  using GPU 1 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank2]:[W707 16:10:41.838199408 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 2]  using GPU 2 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.

Map:  34%|███▍      | 2000/5834 [00:00<00:00, 4512.16 examples/s]
Map:  51%|█████▏    | 3000/5834 [00:00<00:00, 4925.34 examples/s]
Map:  69%|██████▊   | 4000/5834 [00:00<00:00, 5121.41 examples/s]
Map:  86%|████████▌ | 5000/5834 [00:01<00:00, 5129.49 examples/s]
Map: 100%|██████████| 5834/5834 [00:01<00:00, 5199.51 examples/s]
Map: 100%|██████████| 5834/5834 [00:01<00:00, 4875.88 examples/s]

Map:   0%|          | 0/194 [00:00<?, ? examples/s]
Map: 100%|██████████| 194/194 [00:00<00:00, 3914.14 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank0]:[W707 16:10:42.859854023 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 0]  using GPU 0 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(

Map:   0%|          | 0/5834 [00:00<?, ? examples/s]
Map:   0%|          | 0/5834 [00:00<?, ? examples/s]
Map:   0%|          | 0/5834 [00:00<?, ? examples/s]Using auto half precision backend

Map:  17%|█▋        | 1000/5834 [00:00<00:02, 2065.43 examples/s]
Map:  17%|█▋        | 1000/5834 [00:00<00:02, 1883.96 examples/s]
Map:  17%|█▋        | 1000/5834 [00:00<00:02, 1836.04 examples/s]
Map:  34%|███▍      | 2000/5834 [00:00<00:01, 2100.40 examples/s]
Map:  34%|███▍      | 2000/5834 [00:00<00:01, 2053.01 examples/s]
Map:  34%|███▍      | 2000/5834 [00:01<00:01, 2004.74 examples/s]
Map:  51%|█████▏    | 3000/5834 [00:01<00:01, 2120.88 examples/s]
Map:  51%|█████▏    | 3000/5834 [00:01<00:01, 2107.67 examples/s]
Map:  51%|█████▏    | 3000/5834 [00:01<00:01, 2070.91 examples/s]
Map:  69%|██████▊   | 4000/5834 [00:01<00:00, 2175.39 examples/s]
Map:  69%|██████▊   | 4000/5834 [00:01<00:00, 2082.06 examples/s]
Map:  69%|██████▊   | 4000/5834 [00:01<00:00, 2107.39 examples/s]
Map:  86%|████████▌ | 5000/5834 [00:02<00:00, 2132.67 examples/s]
Map:  86%|████████▌ | 5000/5834 [00:02<00:00, 2103.44 examples/s]
Map:  86%|████████▌ | 5000/5834 [00:02<00:00, 2134.79 examples/s]
Map: 100%|██████████| 5834/5834 [00:02<00:00, 2167.36 examples/s]
Map: 100%|██████████| 5834/5834 [00:02<00:00, 2116.12 examples/s]
Map: 100%|██████████| 5834/5834 [00:02<00:00, 2109.47 examples/s]
Map: 100%|██████████| 5834/5834 [00:02<00:00, 2173.50 examples/s]

Map: 100%|██████████| 5834/5834 [00:02<00:00, 2074.56 examples/s]
Map: 100%|██████████| 5834/5834 [00:02<00:00, 2080.83 examples/s]

Map:   0%|          | 0/194 [00:00<?, ? examples/s]

Map:   0%|          | 0/194 [00:00<?, ? examples/s]
Map:   0%|          | 0/194 [00:00<?, ? examples/s]
Map: 100%|██████████| 194/194 [00:00<00:00, 2353.83 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(

Map: 100%|██████████| 194/194 [00:00<00:00, 2207.38 examples/s]
Map: 100%|██████████| 194/194 [00:00<00:00, 1732.35 examples/s]

/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(
***** Running training *****
  Num examples = 5,834
  Num Epochs = 10
  Instantaneous batch size per device = 1
  Total train batch size (w. parallel, distributed & accumulation) = 16
  Gradient Accumulation steps = 4
  Total optimization steps = 3,640
  Number of trainable parameters = 1,918,238,720
Automatic Weights & Biases logging enabled, to disable set os.environ["WANDB_DISABLED"] = "true"

  0%|          | 0/3640 [00:00<?, ?it/s]
  0%|          | 1/3640 [00:02<2:03:30,  2.04s/it]
                                                  

  0%|          | 1/3640 [00:02<2:03:30,  2.04s/it]
  0%|          | 2/3640 [00:03<1:42:02,  1.68s/it]
  0%|          | 3/3640 [00:05<1:38:46,  1.63s/it]
  0%|          | 4/3640 [00:06<1:38:47,  1.63s/it]
  0%|          | 5/3640 [00:08<1:35:55,  1.58s/it]
  0%|          | 6/3640 [00:09<1:32:04,  1.52s/it]
  0%|          | 7/3640 [00:10<1:29:27,  1.48s/it]
  0%|          | 8/3640 [00:12<1:27:32,  1.45s/it]
  0%|          | 9/3640 [00:13<1:26:35,  1.43s/it]
  0%|          | 10/3640 [00:15<1:25:42,  1.42s/it]
  0%|          | 11/3640 [00:16<1:25:28,  1.41s/it]
  0%|          | 12/3640 [00:17<1:24:53,  1.40s/it]
  0%|          | 13/3640 [00:19<1:24:28,  1.40s/it]
  0%|          | 14/3640 [00:20<1:24:14,  1.39s/it]
  0%|          | 15/3640 [00:22<1:24:17,  1.40s/it]
  0%|          | 16/3640 [00:23<1:23:55,  1.39s/it]
  0%|          | 17/3640 [00:24<1:23:39,  1.39s/it]
  0%|          | 18/3640 [00:26<1:23:27,  1.38s/it]
  1%|          | 19/3640 [00:27<1:23:21,  1.38s/it]
  1%|          | 20/3640 [00:28<1:23:33,  1.39s/it]
  1%|          | 21/3640 [00:30<1:23:51,  1.39s/it]
  1%|          | 22/3640 [00:31<1:23:50,  1.39s/it]
  1%|          | 23/3640 [00:33<1:23:39,  1.39s/it]
  1%|          | 24/3640 [00:34<1:23:28,  1.39s/it]
  1%|          | 25/3640 [00:35<1:23:39,  1.39s/it]
                                                   

  1%|          | 25/3640 [00:35<1:23:39,  1.39s/it]
  1%|          | 26/3640 [00:37<1:24:29,  1.40s/it]
  1%|          | 27/3640 [00:38<1:24:17,  1.40s/it]
  1%|          | 28/3640 [00:40<1:24:00,  1.40s/it]
  1%|          | 29/3640 [00:41<1:24:58,  1.41s/it]
  1%|          | 30/3640 [00:42<1:24:34,  1.41s/it]
  1%|          | 31/3640 [00:44<1:24:18,  1.40s/it]
  1%|          | 32/3640 [00:45<1:24:17,  1.40s/it]
  1%|          | 33/3640 [00:47<1:24:00,  1.40s/it]
  1%|          | 34/3640 [00:48<1:23:45,  1.39s/it]
  1%|          | 35/3640 [00:49<1:23:32,  1.39s/it]
  1%|          | 36/3640 [00:51<1:23:36,  1.39s/it]
  1%|          | 37/3640 [00:52<1:23:46,  1.40s/it]
  1%|          | 38/3640 [00:54<1:23:32,  1.39s/it]
  1%|          | 39/3640 [00:55<1:23:45,  1.40s/it]
  1%|          | 40/3640 [00:56<1:23:39,  1.39s/it]
  1%|          | 41/3640 [00:58<1:23:43,  1.40s/it]
  1%|          | 42/3640 [00:59<1:23:35,  1.39s/it]
  1%|          | 43/3640 [01:01<1:23:42,  1.40s/it]
  1%|          | 44/3640 [01:02<1:23:31,  1.39s/it]
  1%|          | 45/3640 [01:03<1:23:16,  1.39s/it]
  1%|▏         | 46/3640 [01:05<1:23:07,  1.39s/it]
  1%|▏         | 47/3640 [01:06<1:23:18,  1.39s/it]
  1%|▏         | 48/3640 [01:08<1:23:09,  1.39s/it]
  1%|▏         | 49/3640 [01:09<1:23:19,  1.39s/it]
  1%|▏         | 50/3640 [01:10<1:23:25,  1.39s/it]
                                                   

  1%|▏         | 50/3640 [01:10<1:23:25,  1.39s/it]
  1%|▏         | 51/3640 [01:12<1:24:25,  1.41s/it]
  1%|▏         | 52/3640 [01:13<1:24:30,  1.41s/it]
  1%|▏         | 53/3640 [01:15<1:24:34,  1.41s/it]
  1%|▏         | 54/3640 [01:16<1:24:10,  1.41s/it]
  2%|▏         | 55/3640 [01:17<1:23:42,  1.40s/it]
  2%|▏         | 56/3640 [01:19<1:23:19,  1.39s/it]
  2%|▏         | 57/3640 [01:20<1:23:20,  1.40s/it]
  2%|▏         | 58/3640 [01:22<1:23:16,  1.39s/it]
  2%|▏         | 59/3640 [01:23<1:23:05,  1.39s/it]
  2%|▏         | 60/3640 [01:24<1:22:50,  1.39s/it]
  2%|▏         | 61/3640 [01:26<1:22:44,  1.39s/it]
  2%|▏         | 62/3640 [01:27<1:22:48,  1.39s/it]
  2%|▏         | 63/3640 [01:28<1:22:52,  1.39s/it]
  2%|▏         | 64/3640 [01:30<1:22:58,  1.39s/it]
  2%|▏         | 65/3640 [01:31<1:22:51,  1.39s/it]
  2%|▏         | 66/3640 [01:33<1:22:48,  1.39s/it]
  2%|▏         | 67/3640 [01:34<1:22:46,  1.39s/it]
  2%|▏         | 68/3640 [01:35<1:22:45,  1.39s/it]
  2%|▏         | 69/3640 [01:37<1:22:47,  1.39s/it]
  2%|▏         | 70/3640 [01:38<1:22:41,  1.39s/it]
  2%|▏         | 71/3640 [01:40<1:22:44,  1.39s/it]
  2%|▏         | 72/3640 [01:41<1:22:35,  1.39s/it]
  2%|▏         | 73/3640 [01:42<1:22:42,  1.39s/it]
  2%|▏         | 74/3640 [01:44<1:26:22,  1.45s/it]
  2%|▏         | 75/3640 [01:45<1:25:02,  1.43s/it]
                                                   

  2%|▏         | 75/3640 [01:45<1:25:02,  1.43s/it]
  2%|▏         | 76/3640 [01:47<1:25:53,  1.45s/it]
  2%|▏         | 77/3640 [01:48<1:24:33,  1.42s/it]
  2%|▏         | 78/3640 [01:50<1:23:44,  1.41s/it]
  2%|▏         | 79/3640 [01:51<1:23:03,  1.40s/it]
  2%|▏         | 80/3640 [01:52<1:22:30,  1.39s/it]
  2%|▏         | 81/3640 [01:54<1:22:11,  1.39s/it]
  2%|▏         | 82/3640 [01:55<1:21:59,  1.38s/it]
  2%|▏         | 83/3640 [01:56<1:21:51,  1.38s/it]
  2%|▏         | 84/3640 [01:58<1:21:38,  1.38s/it]
  2%|▏         | 85/3640 [01:59<1:21:22,  1.37s/it]
  2%|▏         | 86/3640 [02:01<1:21:23,  1.37s/it]
  2%|▏         | 87/3640 [02:02<1:21:18,  1.37s/it]
  2%|▏         | 88/3640 [02:03<1:21:16,  1.37s/it]
  2%|▏         | 89/3640 [02:05<1:21:11,  1.37s/it]
  2%|▏         | 90/3640 [02:06<1:21:12,  1.37s/it]
  2%|▎         | 91/3640 [02:07<1:21:10,  1.37s/it]
  3%|▎         | 92/3640 [02:09<1:21:10,  1.37s/it]
  3%|▎         | 93/3640 [02:10<1:20:55,  1.37s/it]
  3%|▎         | 94/3640 [02:12<1:21:05,  1.37s/it]
  3%|▎         | 95/3640 [02:13<1:21:00,  1.37s/it]
  3%|▎         | 96/3640 [02:14<1:20:57,  1.37s/it]
  3%|▎         | 97/3640 [02:16<1:21:01,  1.37s/it]
  3%|▎         | 98/3640 [02:17<1:26:51,  1.47s/it]
  3%|▎         | 99/3640 [02:19<1:25:47,  1.45s/it]
  3%|▎         | 100/3640 [02:20<1:24:30,  1.43s/it]
                                                    

  3%|▎         | 100/3640 [02:20<1:24:30,  1.43s/it]
  3%|▎         | 101/3640 [02:22<1:23:56,  1.42s/it]
  3%|▎         | 102/3640 [02:23<1:23:02,  1.41s/it]
  3%|▎         | 103/3640 [02:24<1:22:21,  1.40s/it]
  3%|▎         | 104/3640 [02:26<1:22:04,  1.39s/it]
  3%|▎         | 105/3640 [02:27<1:21:39,  1.39s/it]
  3%|▎         | 106/3640 [02:28<1:21:25,  1.38s/it]
  3%|▎         | 107/3640 [02:30<1:21:00,  1.38s/it]
  3%|▎         | 108/3640 [02:31<1:20:59,  1.38s/it]
  3%|▎         | 109/3640 [02:33<1:20:57,  1.38s/it]
  3%|▎         | 110/3640 [02:34<1:20:50,  1.37s/it]
  3%|▎         | 111/3640 [02:35<1:20:49,  1.37s/it]
  3%|▎         | 112/3640 [02:37<1:20:47,  1.37s/it]
  3%|▎         | 113/3640 [02:38<1:20:44,  1.37s/it]
  3%|▎         | 114/3640 [02:39<1:20:47,  1.37s/it]
  3%|▎         | 115/3640 [02:41<1:20:45,  1.37s/it]
  3%|▎         | 116/3640 [02:42<1:20:37,  1.37s/it]
  3%|▎         | 117/3640 [02:44<1:20:35,  1.37s/it]
  3%|▎         | 118/3640 [02:45<1:20:36,  1.37s/it]
  3%|▎         | 119/3640 [02:46<1:20:33,  1.37s/it]
  3%|▎         | 120/3640 [02:48<1:20:29,  1.37s/it]
  3%|▎         | 121/3640 [02:49<1:20:33,  1.37s/it]
  3%|▎         | 122/3640 [02:50<1:20:36,  1.37s/it]
  3%|▎         | 123/3640 [02:52<1:21:36,  1.39s/it]
  3%|▎         | 124/3640 [02:53<1:21:06,  1.38s/it]
  3%|▎         | 125/3640 [02:55<1:20:58,  1.38s/it]
                                                    

  3%|▎         | 125/3640 [02:55<1:20:58,  1.38s/it]
  3%|▎         | 126/3640 [02:56<1:21:19,  1.39s/it]
  3%|▎         | 127/3640 [02:57<1:21:01,  1.38s/it]
  4%|▎         | 128/3640 [02:59<1:20:42,  1.38s/it]
  4%|▎         | 129/3640 [03:00<1:20:37,  1.38s/it]
  4%|▎         | 130/3640 [03:01<1:20:32,  1.38s/it]
  4%|▎         | 131/3640 [03:03<1:20:26,  1.38s/it]
  4%|▎         | 132/3640 [03:04<1:20:13,  1.37s/it]
  4%|▎         | 133/3640 [03:06<1:20:16,  1.37s/it]
  4%|▎         | 134/3640 [03:07<1:20:04,  1.37s/it]
  4%|▎         | 135/3640 [03:08<1:19:57,  1.37s/it]
  4%|▎         | 136/3640 [03:10<1:19:59,  1.37s/it]
  4%|▍         | 137/3640 [03:11<1:20:01,  1.37s/it]
  4%|▍         | 138/3640 [03:12<1:20:05,  1.37s/it]
  4%|▍         | 139/3640 [03:14<1:20:05,  1.37s/it]
  4%|▍         | 140/3640 [03:15<1:20:09,  1.37s/it]
  4%|▍         | 141/3640 [03:17<1:19:59,  1.37s/it]
  4%|▍         | 142/3640 [03:18<1:20:04,  1.37s/it]
  4%|▍         | 143/3640 [03:19<1:19:57,  1.37s/it]
  4%|▍         | 144/3640 [03:21<1:19:59,  1.37s/it]
  4%|▍         | 145/3640 [03:22<1:20:04,  1.37s/it]
  4%|▍         | 146/3640 [03:23<1:20:03,  1.37s/it]
  4%|▍         | 147/3640 [03:25<1:21:12,  1.39s/it]
  4%|▍         | 148/3640 [03:26<1:20:47,  1.39s/it]
  4%|▍         | 149/3640 [03:28<1:20:25,  1.38s/it]
  4%|▍         | 150/3640 [03:29<1:20:18,  1.38s/it]
                                                    

  4%|▍         | 150/3640 [03:29<1:20:18,  1.38s/it]
  4%|▍         | 151/3640 [03:30<1:21:06,  1.39s/it]
  4%|▍         | 152/3640 [03:32<1:20:43,  1.39s/it]
  4%|▍         | 153/3640 [03:33<1:20:16,  1.38s/it]
  4%|▍         | 154/3640 [03:35<1:20:10,  1.38s/it]
  4%|▍         | 155/3640 [03:36<1:19:59,  1.38s/it]
  4%|▍         | 156/3640 [03:37<1:19:55,  1.38s/it]
  4%|▍         | 157/3640 [03:39<1:19:47,  1.37s/it]
  4%|▍         | 158/3640 [03:40<1:19:49,  1.38s/it]
  4%|▍         | 159/3640 [03:41<1:19:43,  1.37s/it]
  4%|▍         | 160/3640 [03:43<1:19:38,  1.37s/it]
  4%|▍         | 161/3640 [03:44<1:19:45,  1.38s/it]
  4%|▍         | 162/3640 [03:46<1:19:47,  1.38s/it]
  4%|▍         | 163/3640 [03:47<1:19:47,  1.38s/it]
  5%|▍         | 164/3640 [03:48<1:19:42,  1.38s/it]
  5%|▍         | 165/3640 [03:50<1:19:38,  1.38s/it]
  5%|▍         | 166/3640 [03:51<1:19:39,  1.38s/it]
  5%|▍         | 167/3640 [03:52<1:19:33,  1.37s/it]
  5%|▍         | 168/3640 [03:54<1:19:24,  1.37s/it]
  5%|▍         | 169/3640 [03:55<1:19:27,  1.37s/it]
  5%|▍         | 170/3640 [03:57<1:19:19,  1.37s/it]
  5%|▍         | 171/3640 [03:58<1:20:18,  1.39s/it]
  5%|▍         | 172/3640 [03:59<1:19:48,  1.38s/it]
  5%|▍         | 173/3640 [04:01<1:19:35,  1.38s/it]
  5%|▍         | 174/3640 [04:02<1:19:28,  1.38s/it]
  5%|▍         | 175/3640 [04:03<1:19:24,  1.38s/it]
                                                    

  5%|▍         | 175/3640 [04:03<1:19:24,  1.38s/it]
  5%|▍         | 176/3640 [04:05<1:20:29,  1.39s/it]
  5%|▍         | 177/3640 [04:06<1:20:05,  1.39s/it]
  5%|▍         | 178/3640 [04:08<1:19:47,  1.38s/it]
  5%|▍         | 179/3640 [04:09<1:19:42,  1.38s/it]
  5%|▍         | 180/3640 [04:10<1:19:28,  1.38s/it]
  5%|▍         | 181/3640 [04:12<1:19:16,  1.38s/it]
  5%|▌         | 182/3640 [04:13<1:19:07,  1.37s/it]
  5%|▌         | 183/3640 [04:14<1:19:10,  1.37s/it]
  5%|▌         | 184/3640 [04:16<1:18:59,  1.37s/it]
  5%|▌         | 185/3640 [04:17<1:19:03,  1.37s/it]
  5%|▌         | 186/3640 [04:19<1:19:00,  1.37s/it]
  5%|▌         | 187/3640 [04:20<1:22:26,  1.43s/it]
  5%|▌         | 188/3640 [04:22<1:21:22,  1.41s/it]
  5%|▌         | 189/3640 [04:23<1:20:41,  1.40s/it]
  5%|▌         | 190/3640 [04:24<1:20:08,  1.39s/it]
  5%|▌         | 191/3640 [04:26<1:19:37,  1.39s/it]
  5%|▌         | 192/3640 [04:27<1:19:15,  1.38s/it]
  5%|▌         | 193/3640 [04:28<1:19:03,  1.38s/it]
  5%|▌         | 194/3640 [04:30<1:19:33,  1.39s/it]
  5%|▌         | 195/3640 [04:31<1:19:50,  1.39s/it]
  5%|▌         | 196/3640 [04:33<1:19:17,  1.38s/it]
  5%|▌         | 197/3640 [04:34<1:19:00,  1.38s/it]
  5%|▌         | 198/3640 [04:35<1:18:58,  1.38s/it]
  5%|▌         | 199/3640 [04:37<1:18:46,  1.37s/it]
  5%|▌         | 200/3640 [04:38<1:18:32,  1.37s/it]
                                                    

  5%|▌         | 200/3640 [04:38<1:18:32,  1.37s/it]
  6%|▌         | 201/3640 [04:39<1:19:46,  1.39s/it]
  6%|▌         | 202/3640 [04:41<1:19:26,  1.39s/it]
  6%|▌         | 203/3640 [04:42<1:19:11,  1.38s/it]
  6%|▌         | 204/3640 [04:44<1:18:56,  1.38s/it]
  6%|▌         | 205/3640 [04:45<1:18:51,  1.38s/it]
  6%|▌         | 206/3640 [04:46<1:18:41,  1.37s/it]
  6%|▌         | 207/3640 [04:48<1:18:41,  1.38s/it]
  6%|▌         | 208/3640 [04:49<1:18:26,  1.37s/it]
  6%|▌         | 209/3640 [04:50<1:18:22,  1.37s/it]
  6%|▌         | 210/3640 [04:52<1:18:31,  1.37s/it]
  6%|▌         | 211/3640 [04:53<1:18:19,  1.37s/it]
  6%|▌         | 212/3640 [04:55<1:18:20,  1.37s/it]
  6%|▌         | 213/3640 [04:56<1:17:58,  1.37s/it]
  6%|▌         | 214/3640 [04:57<1:18:01,  1.37s/it]
  6%|▌         | 215/3640 [04:59<1:18:00,  1.37s/it]
  6%|▌         | 216/3640 [05:00<1:17:58,  1.37s/it]
  6%|▌         | 217/3640 [05:01<1:18:01,  1.37s/it]
  6%|▌         | 218/3640 [05:03<1:18:54,  1.38s/it]
  6%|▌         | 219/3640 [05:04<1:18:30,  1.38s/it]
  6%|▌         | 220/3640 [05:06<1:18:16,  1.37s/it]
  6%|▌         | 221/3640 [05:07<1:18:09,  1.37s/it]
  6%|▌         | 222/3640 [05:08<1:18:12,  1.37s/it]
  6%|▌         | 223/3640 [05:10<1:17:41,  1.36s/it]
  6%|▌         | 224/3640 [05:11<1:17:52,  1.37s/it]
  6%|▌         | 225/3640 [05:12<1:17:52,  1.37s/it]
                                                    

  6%|▌         | 225/3640 [05:12<1:17:52,  1.37s/it]
  6%|▌         | 226/3640 [05:14<1:18:33,  1.38s/it]
  6%|▌         | 227/3640 [05:15<1:18:19,  1.38s/it]
  6%|▋         | 228/3640 [05:16<1:18:12,  1.38s/it]
  6%|▋         | 229/3640 [05:18<1:18:07,  1.37s/it]
  6%|▋         | 230/3640 [05:19<1:18:05,  1.37s/it]
  6%|▋         | 231/3640 [05:21<1:17:58,  1.37s/it]
  6%|▋         | 232/3640 [05:22<1:17:51,  1.37s/it]
  6%|▋         | 233/3640 [05:23<1:17:50,  1.37s/it]
  6%|▋         | 234/3640 [05:25<1:17:45,  1.37s/it]
  6%|▋         | 235/3640 [05:26<1:17:48,  1.37s/it]
  6%|▋         | 236/3640 [05:27<1:17:50,  1.37s/it]
  7%|▋         | 237/3640 [05:29<1:17:51,  1.37s/it]
  7%|▋         | 238/3640 [05:30<1:17:54,  1.37s/it]
  7%|▋         | 239/3640 [05:32<1:17:48,  1.37s/it]
  7%|▋         | 240/3640 [05:33<1:17:41,  1.37s/it]
  7%|▋         | 241/3640 [05:34<1:17:41,  1.37s/it]
  7%|▋         | 242/3640 [05:36<1:18:49,  1.39s/it]
  7%|▋         | 243/3640 [05:37<1:18:29,  1.39s/it]
  7%|▋         | 244/3640 [05:39<1:18:14,  1.38s/it]
  7%|▋         | 245/3640 [05:40<1:18:01,  1.38s/it]
  7%|▋         | 246/3640 [05:41<1:17:49,  1.38s/it]
  7%|▋         | 247/3640 [05:43<1:17:36,  1.37s/it]
  7%|▋         | 248/3640 [05:44<1:17:30,  1.37s/it]
  7%|▋         | 249/3640 [05:45<1:17:33,  1.37s/it]
  7%|▋         | 250/3640 [05:47<1:17:31,  1.37s/it]
                                                    

  7%|▋         | 250/3640 [05:47<1:17:31,  1.37s/it]
  7%|▋         | 251/3640 [05:48<1:18:30,  1.39s/it]
  7%|▋         | 252/3640 [05:50<1:18:10,  1.38s/it]
  7%|▋         | 253/3640 [05:51<1:17:48,  1.38s/it]
  7%|▋         | 254/3640 [05:52<1:17:37,  1.38s/it]
  7%|▋         | 255/3640 [05:54<1:17:34,  1.38s/it]
  7%|▋         | 256/3640 [05:55<1:17:21,  1.37s/it]
  7%|▋         | 257/3640 [05:56<1:17:14,  1.37s/it]
  7%|▋         | 258/3640 [05:58<1:17:16,  1.37s/it]
  7%|▋         | 259/3640 [05:59<1:17:11,  1.37s/it]
  7%|▋         | 260/3640 [06:00<1:17:04,  1.37s/it]
  7%|▋         | 261/3640 [06:02<1:17:07,  1.37s/it]
  7%|▋         | 262/3640 [06:03<1:17:12,  1.37s/it]
  7%|▋         | 263/3640 [06:05<1:17:10,  1.37s/it]
  7%|▋         | 264/3640 [06:06<1:17:14,  1.37s/it]
  7%|▋         | 265/3640 [06:07<1:17:14,  1.37s/it]
  7%|▋         | 266/3640 [06:09<1:22:24,  1.47s/it]
  7%|▋         | 267/3640 [06:10<1:20:43,  1.44s/it]
  7%|▋         | 268/3640 [06:12<1:19:35,  1.42s/it]
  7%|▋         | 269/3640 [06:13<1:18:42,  1.40s/it]
  7%|▋         | 270/3640 [06:14<1:18:00,  1.39s/it]
  7%|▋         | 271/3640 [06:16<1:17:46,  1.39s/it]
  7%|▋         | 272/3640 [06:17<1:17:33,  1.38s/it]
  8%|▊         | 273/3640 [06:19<1:17:19,  1.38s/it]
  8%|▊         | 274/3640 [06:20<1:17:01,  1.37s/it]
  8%|▊         | 275/3640 [06:21<1:17:00,  1.37s/it]
                                                    

  8%|▊         | 275/3640 [06:21<1:17:00,  1.37s/it]
  8%|▊         | 276/3640 [06:23<1:17:49,  1.39s/it]
  8%|▊         | 277/3640 [06:24<1:17:31,  1.38s/it]
  8%|▊         | 278/3640 [06:25<1:17:04,  1.38s/it]
  8%|▊         | 279/3640 [06:27<1:16:55,  1.37s/it]
  8%|▊         | 280/3640 [06:28<1:17:02,  1.38s/it]
  8%|▊         | 281/3640 [06:30<1:16:56,  1.37s/it]
  8%|▊         | 282/3640 [06:31<1:16:48,  1.37s/it]
  8%|▊         | 283/3640 [06:32<1:16:45,  1.37s/it]
  8%|▊         | 284/3640 [06:34<1:16:41,  1.37s/it]
  8%|▊         | 285/3640 [06:35<1:16:44,  1.37s/it]
  8%|▊         | 286/3640 [06:36<1:16:33,  1.37s/it]
  8%|▊         | 287/3640 [06:38<1:16:38,  1.37s/it]
  8%|▊         | 288/3640 [06:39<1:16:38,  1.37s/it]
  8%|▊         | 289/3640 [06:41<1:16:30,  1.37s/it]
  8%|▊         | 290/3640 [06:42<1:17:15,  1.38s/it]
  8%|▊         | 291/3640 [06:43<1:16:53,  1.38s/it]
  8%|▊         | 292/3640 [06:45<1:16:40,  1.37s/it]
  8%|▊         | 293/3640 [06:46<1:19:56,  1.43s/it]
  8%|▊         | 294/3640 [06:48<1:18:45,  1.41s/it]
  8%|▊         | 295/3640 [06:49<1:17:54,  1.40s/it]
  8%|▊         | 296/3640 [06:50<1:17:18,  1.39s/it]
  8%|▊         | 297/3640 [06:52<1:17:03,  1.38s/it]
  8%|▊         | 298/3640 [06:53<1:16:58,  1.38s/it]
  8%|▊         | 299/3640 [06:55<1:16:47,  1.38s/it]
  8%|▊         | 300/3640 [06:56<1:16:38,  1.38s/it]
                                                    

  8%|▊         | 300/3640 [06:56<1:16:38,  1.38s/it]
  8%|▊         | 301/3640 [06:57<1:17:00,  1.38s/it]
  8%|▊         | 302/3640 [06:59<1:16:31,  1.38s/it]
  8%|▊         | 303/3640 [07:00<1:16:18,  1.37s/it]
  8%|▊         | 304/3640 [07:01<1:16:07,  1.37s/it]
  8%|▊         | 305/3640 [07:03<1:16:03,  1.37s/it]
  8%|▊         | 306/3640 [07:04<1:16:08,  1.37s/it]
  8%|▊         | 307/3640 [07:05<1:16:06,  1.37s/it]
  8%|▊         | 308/3640 [07:07<1:16:10,  1.37s/it]
  8%|▊         | 309/3640 [07:08<1:16:03,  1.37s/it]
  9%|▊         | 310/3640 [07:10<1:16:03,  1.37s/it]
  9%|▊         | 311/3640 [07:11<1:16:07,  1.37s/it]
  9%|▊         | 312/3640 [07:12<1:16:08,  1.37s/it]
  9%|▊         | 313/3640 [07:14<1:16:42,  1.38s/it]
  9%|▊         | 314/3640 [07:15<1:16:31,  1.38s/it]
  9%|▊         | 315/3640 [07:16<1:16:28,  1.38s/it]
  9%|▊         | 316/3640 [07:18<1:16:18,  1.38s/it]
  9%|▊         | 317/3640 [07:19<1:16:10,  1.38s/it]
  9%|▊         | 318/3640 [07:21<1:15:57,  1.37s/it]
  9%|▉         | 319/3640 [07:22<1:15:55,  1.37s/it]
  9%|▉         | 320/3640 [07:23<1:15:55,  1.37s/it]
  9%|▉         | 321/3640 [07:25<1:15:53,  1.37s/it]
  9%|▉         | 322/3640 [07:26<1:15:43,  1.37s/it]
  9%|▉         | 323/3640 [07:27<1:15:42,  1.37s/it]
  9%|▉         | 324/3640 [07:29<1:15:34,  1.37s/it]
  9%|▉         | 325/3640 [07:30<1:15:37,  1.37s/it]
                                                    

  9%|▉         | 325/3640 [07:30<1:15:37,  1.37s/it]
  9%|▉         | 326/3640 [07:32<1:16:34,  1.39s/it]
  9%|▉         | 327/3640 [07:33<1:16:12,  1.38s/it]
  9%|▉         | 328/3640 [07:34<1:15:53,  1.37s/it]
  9%|▉         | 329/3640 [07:36<1:15:37,  1.37s/it]
  9%|▉         | 330/3640 [07:37<1:15:24,  1.37s/it]
  9%|▉         | 331/3640 [07:38<1:15:29,  1.37s/it]
  9%|▉         | 332/3640 [07:40<1:15:37,  1.37s/it]
  9%|▉         | 333/3640 [07:41<1:15:27,  1.37s/it]
  9%|▉         | 334/3640 [07:43<1:15:22,  1.37s/it]
  9%|▉         | 335/3640 [07:44<1:15:23,  1.37s/it]
  9%|▉         | 336/3640 [07:45<1:15:25,  1.37s/it]
  9%|▉         | 337/3640 [07:47<1:16:32,  1.39s/it]
  9%|▉         | 338/3640 [07:48<1:16:15,  1.39s/it]
  9%|▉         | 339/3640 [07:49<1:15:52,  1.38s/it]
  9%|▉         | 340/3640 [07:51<1:15:49,  1.38s/it]
  9%|▉         | 341/3640 [07:52<1:15:37,  1.38s/it]
  9%|▉         | 342/3640 [07:54<1:15:21,  1.37s/it]
  9%|▉         | 343/3640 [07:55<1:15:20,  1.37s/it]
  9%|▉         | 344/3640 [07:56<1:15:19,  1.37s/it]
  9%|▉         | 345/3640 [07:58<1:15:17,  1.37s/it]
 10%|▉         | 346/3640 [07:59<1:15:13,  1.37s/it]
 10%|▉         | 347/3640 [08:00<1:15:10,  1.37s/it]
 10%|▉         | 348/3640 [08:02<1:15:11,  1.37s/it]
 10%|▉         | 349/3640 [08:03<1:15:12,  1.37s/it]
 10%|▉         | 350/3640 [08:05<1:15:06,  1.37s/it]
                                                    

 10%|▉         | 350/3640 [08:05<1:15:06,  1.37s/it]
 10%|▉         | 351/3640 [08:06<1:15:34,  1.38s/it]
 10%|▉         | 352/3640 [08:07<1:15:23,  1.38s/it]
 10%|▉         | 353/3640 [08:09<1:15:21,  1.38s/it]
 10%|▉         | 354/3640 [08:10<1:15:18,  1.38s/it]
 10%|▉         | 355/3640 [08:11<1:15:07,  1.37s/it]
 10%|▉         | 356/3640 [08:13<1:15:03,  1.37s/it]
 10%|▉         | 357/3640 [08:14<1:15:03,  1.37s/it]
 10%|▉         | 358/3640 [08:16<1:14:50,  1.37s/it]
 10%|▉         | 359/3640 [08:17<1:14:44,  1.37s/it]
 10%|▉         | 360/3640 [08:18<1:14:54,  1.37s/it]
 10%|▉         | 361/3640 [08:20<1:15:46,  1.39s/it]
 10%|▉         | 362/3640 [08:21<1:15:19,  1.38s/it]
 10%|▉         | 363/3640 [08:22<1:15:06,  1.38s/it]
 10%|█         | 364/3640 [08:24<1:14:48,  1.37s/it]
 10%|█         | 365/3640 [08:25<1:14:48,  1.37s/it]
 10%|█         | 366/3640 [08:27<1:18:02,  1.43s/it]
 10%|█         | 367/3640 [08:28<1:17:03,  1.41s/it]
 10%|█         | 368/3640 [08:29<1:16:14,  1.40s/it]
 10%|█         | 369/3640 [08:31<1:15:34,  1.39s/it]
 10%|█         | 370/3640 [08:32<1:15:17,  1.38s/it]
 10%|█         | 371/3640 [08:34<1:15:02,  1.38s/it]
 10%|█         | 372/3640 [08:35<1:14:58,  1.38s/it]
 10%|█         | 373/3640 [08:36<1:18:07,  1.43s/it]
 10%|█         | 374/3640 [08:38<1:16:52,  1.41s/it]
 10%|█         | 375/3640 [08:39<1:16:03,  1.40s/it]
                                                    

 10%|█         | 375/3640 [08:39<1:16:03,  1.40s/it]
 10%|█         | 376/3640 [08:41<1:15:55,  1.40s/it]
 10%|█         | 377/3640 [08:42<1:15:30,  1.39s/it]
 10%|█         | 378/3640 [08:43<1:15:11,  1.38s/it]
 10%|█         | 379/3640 [08:45<1:15:03,  1.38s/it]
 10%|█         | 380/3640 [08:46<1:14:55,  1.38s/it]
 10%|█         | 381/3640 [08:47<1:14:47,  1.38s/it]
 10%|█         | 382/3640 [08:49<1:14:23,  1.37s/it]
 11%|█         | 383/3640 [08:50<1:14:23,  1.37s/it]
 11%|█         | 384/3640 [08:52<1:14:33,  1.37s/it]
 11%|█         | 385/3640 [08:53<1:15:17,  1.39s/it]
 11%|█         | 386/3640 [08:54<1:15:09,  1.39s/it]
 11%|█         | 387/3640 [08:56<1:14:45,  1.38s/it]
 11%|█         | 388/3640 [08:57<1:14:41,  1.38s/it]
 11%|█         | 389/3640 [08:58<1:14:35,  1.38s/it]
 11%|█         | 390/3640 [09:00<1:14:31,  1.38s/it]
 11%|█         | 391/3640 [09:01<1:14:10,  1.37s/it]
 11%|█         | 392/3640 [09:03<1:14:14,  1.37s/it]
 11%|█         | 393/3640 [09:04<1:14:15,  1.37s/it]
 11%|█         | 394/3640 [09:05<1:14:10,  1.37s/it]
 11%|█         | 395/3640 [09:07<1:14:11,  1.37s/it]
 11%|█         | 396/3640 [09:08<1:14:02,  1.37s/it]
 11%|█         | 397/3640 [09:09<1:13:57,  1.37s/it]
 11%|█         | 398/3640 [09:11<1:13:56,  1.37s/it]
 11%|█         | 399/3640 [09:12<1:13:55,  1.37s/it]
 11%|█         | 400/3640 [09:14<1:13:43,  1.37s/it]
                                                    

 11%|█         | 400/3640 [09:14<1:13:43,  1.37s/it]
 11%|█         | 401/3640 [09:15<1:14:48,  1.39s/it]
 11%|█         | 402/3640 [09:16<1:14:32,  1.38s/it]
 11%|█         | 403/3640 [09:18<1:14:21,  1.38s/it]
 11%|█         | 404/3640 [09:19<1:14:05,  1.37s/it]
 11%|█         | 405/3640 [09:20<1:14:06,  1.37s/it]
 11%|█         | 406/3640 [09:22<1:14:05,  1.37s/it]
 11%|█         | 407/3640 [09:23<1:13:35,  1.37s/it]
 11%|█         | 408/3640 [09:25<1:14:00,  1.37s/it]
 11%|█         | 409/3640 [09:26<1:14:25,  1.38s/it]
 11%|█▏        | 410/3640 [09:27<1:14:10,  1.38s/it]
 11%|█▏        | 411/3640 [09:29<1:13:46,  1.37s/it]
 11%|█▏        | 412/3640 [09:30<1:13:44,  1.37s/it]
 11%|█▏        | 413/3640 [09:31<1:13:42,  1.37s/it]
 11%|█▏        | 414/3640 [09:33<1:13:42,  1.37s/it]
 11%|█▏        | 415/3640 [09:34<1:13:43,  1.37s/it]
 11%|█▏        | 416/3640 [09:36<1:13:48,  1.37s/it]
 11%|█▏        | 417/3640 [09:37<1:13:41,  1.37s/it]
 11%|█▏        | 418/3640 [09:38<1:13:42,  1.37s/it]
 12%|█▏        | 419/3640 [09:40<1:13:45,  1.37s/it]
 12%|█▏        | 420/3640 [09:41<1:13:35,  1.37s/it]
 12%|█▏        | 421/3640 [09:42<1:13:36,  1.37s/it]
 12%|█▏        | 422/3640 [09:44<1:13:42,  1.37s/it]
 12%|█▏        | 423/3640 [09:45<1:13:41,  1.37s/it]
 12%|█▏        | 424/3640 [09:47<1:13:34,  1.37s/it]
 12%|█▏        | 425/3640 [09:48<1:13:22,  1.37s/it]
                                                    

 12%|█▏        | 425/3640 [09:48<1:13:22,  1.37s/it]
 12%|█▏        | 426/3640 [09:49<1:14:16,  1.39s/it]
 12%|█▏        | 427/3640 [09:51<1:14:00,  1.38s/it]
 12%|█▏        | 428/3640 [09:52<1:13:48,  1.38s/it]
 12%|█▏        | 429/3640 [09:53<1:13:29,  1.37s/it]
 12%|█▏        | 430/3640 [09:55<1:13:29,  1.37s/it]
 12%|█▏        | 431/3640 [09:56<1:13:14,  1.37s/it]
 12%|█▏        | 432/3640 [09:58<1:13:52,  1.38s/it]
 12%|█▏        | 433/3640 [09:59<1:13:43,  1.38s/it]
 12%|█▏        | 434/3640 [10:00<1:13:35,  1.38s/it]
 12%|█▏        | 435/3640 [10:02<1:13:27,  1.38s/it]
 12%|█▏        | 436/3640 [10:03<1:13:25,  1.37s/it]
 12%|█▏        | 437/3640 [10:04<1:13:15,  1.37s/it]
 12%|█▏        | 438/3640 [10:06<1:13:18,  1.37s/it]
 12%|█▏        | 439/3640 [10:07<1:13:08,  1.37s/it]
 12%|█▏        | 440/3640 [10:09<1:13:11,  1.37s/it]
 12%|█▏        | 441/3640 [10:10<1:13:08,  1.37s/it]
 12%|█▏        | 442/3640 [10:11<1:13:06,  1.37s/it]
 12%|█▏        | 443/3640 [10:13<1:13:06,  1.37s/it]
 12%|█▏        | 444/3640 [10:14<1:13:05,  1.37s/it]
 12%|█▏        | 445/3640 [10:15<1:13:00,  1.37s/it]
 12%|█▏        | 446/3640 [10:17<1:12:34,  1.36s/it]
 12%|█▏        | 447/3640 [10:18<1:16:00,  1.43s/it]
 12%|█▏        | 448/3640 [10:20<1:15:11,  1.41s/it]
 12%|█▏        | 449/3640 [10:21<1:14:25,  1.40s/it]
 12%|█▏        | 450/3640 [10:22<1:13:51,  1.39s/it]
                                                    

 12%|█▏        | 450/3640 [10:22<1:13:51,  1.39s/it]
 12%|█▏        | 451/3640 [10:24<1:13:47,  1.39s/it]
 12%|█▏        | 452/3640 [10:25<1:13:30,  1.38s/it]
 12%|█▏        | 453/3640 [10:27<1:13:20,  1.38s/it]
 12%|█▏        | 454/3640 [10:28<1:13:13,  1.38s/it]
 12%|█▎        | 455/3640 [10:29<1:12:54,  1.37s/it]
 13%|█▎        | 456/3640 [10:31<1:13:44,  1.39s/it]
 13%|█▎        | 457/3640 [10:32<1:13:11,  1.38s/it]
 13%|█▎        | 458/3640 [10:33<1:13:01,  1.38s/it]
 13%|█▎        | 459/3640 [10:35<1:13:02,  1.38s/it]
 13%|█▎        | 460/3640 [10:36<1:12:57,  1.38s/it]
 13%|█▎        | 461/3640 [10:38<1:13:00,  1.38s/it]
 13%|█▎        | 462/3640 [10:39<1:12:55,  1.38s/it]
 13%|█▎        | 463/3640 [10:40<1:12:50,  1.38s/it]
 13%|█▎        | 464/3640 [10:42<1:12:37,  1.37s/it]
 13%|█▎        | 465/3640 [10:43<1:12:35,  1.37s/it]
 13%|█▎        | 466/3640 [10:44<1:12:37,  1.37s/it]
 13%|█▎        | 467/3640 [10:46<1:12:35,  1.37s/it]
 13%|█▎        | 468/3640 [10:47<1:12:36,  1.37s/it]
 13%|█▎        | 469/3640 [10:49<1:12:37,  1.37s/it]
 13%|█▎        | 470/3640 [10:50<1:12:22,  1.37s/it]
 13%|█▎        | 471/3640 [10:51<1:12:19,  1.37s/it]
 13%|█▎        | 472/3640 [10:53<1:12:13,  1.37s/it]
 13%|█▎        | 473/3640 [10:54<1:12:11,  1.37s/it]
 13%|█▎        | 474/3640 [10:55<1:12:17,  1.37s/it]
 13%|█▎        | 475/3640 [10:57<1:12:17,  1.37s/it]
                                                    

 13%|█▎        | 475/3640 [10:57<1:12:17,  1.37s/it]
 13%|█▎        | 476/3640 [10:58<1:13:07,  1.39s/it]
 13%|█▎        | 477/3640 [11:00<1:12:57,  1.38s/it]
 13%|█▎        | 478/3640 [11:01<1:12:42,  1.38s/it]
 13%|█▎        | 479/3640 [11:02<1:12:08,  1.37s/it]
 13%|█▎        | 480/3640 [11:04<1:13:04,  1.39s/it]
 13%|█▎        | 481/3640 [11:05<1:12:44,  1.38s/it]
 13%|█▎        | 482/3640 [11:06<1:12:24,  1.38s/it]
 13%|█▎        | 483/3640 [11:08<1:12:18,  1.37s/it]
 13%|█▎        | 484/3640 [11:09<1:12:14,  1.37s/it]
 13%|█▎        | 485/3640 [11:11<1:12:10,  1.37s/it]
 13%|█▎        | 486/3640 [11:12<1:12:06,  1.37s/it]
 13%|█▎        | 487/3640 [11:13<1:12:00,  1.37s/it]
 13%|█▎        | 488/3640 [11:15<1:12:03,  1.37s/it]
 13%|█▎        | 489/3640 [11:16<1:11:58,  1.37s/it]
 13%|█▎        | 490/3640 [11:17<1:11:53,  1.37s/it]
 13%|█▎        | 491/3640 [11:19<1:11:55,  1.37s/it]
 14%|█▎        | 492/3640 [11:20<1:11:49,  1.37s/it]
 14%|█▎        | 493/3640 [11:22<1:11:39,  1.37s/it]
 14%|█▎        | 494/3640 [11:23<1:11:44,  1.37s/it]
 14%|█▎        | 495/3640 [11:24<1:11:45,  1.37s/it]
 14%|█▎        | 496/3640 [11:26<1:11:47,  1.37s/it]
 14%|█▎        | 497/3640 [11:27<1:11:36,  1.37s/it]
 14%|█▎        | 498/3640 [11:28<1:11:28,  1.36s/it]
 14%|█▎        | 499/3640 [11:30<1:11:32,  1.37s/it]
 14%|█▎        | 500/3640 [11:31<1:11:34,  1.37s/it]
                                                    

 14%|█▎        | 500/3640 [11:31<1:11:34,  1.37s/it]
 14%|█▍        | 501/3640 [11:32<1:11:47,  1.37s/it]
 14%|█▍        | 502/3640 [11:34<1:11:40,  1.37s/it]
 14%|█▍        | 503/3640 [11:35<1:11:43,  1.37s/it]
 14%|█▍        | 504/3640 [11:37<1:12:41,  1.39s/it]
 14%|█▍        | 505/3640 [11:38<1:12:19,  1.38s/it]
 14%|█▍        | 506/3640 [11:39<1:12:02,  1.38s/it]
 14%|█▍        | 507/3640 [11:41<1:11:54,  1.38s/it]
 14%|█▍        | 508/3640 [11:42<1:11:34,  1.37s/it]
 14%|█▍        | 509/3640 [11:43<1:11:32,  1.37s/it]
 14%|█▍        | 510/3640 [11:45<1:11:33,  1.37s/it]
 14%|█▍        | 511/3640 [11:46<1:11:34,  1.37s/it]
 14%|█▍        | 512/3640 [11:48<1:11:29,  1.37s/it]
 14%|█▍        | 513/3640 [11:49<1:11:17,  1.37s/it]
 14%|█▍        | 514/3640 [11:50<1:11:19,  1.37s/it]
 14%|█▍        | 515/3640 [11:52<1:11:21,  1.37s/it]
 14%|█▍        | 516/3640 [11:53<1:11:24,  1.37s/it]
 14%|█▍        | 517/3640 [11:54<1:11:23,  1.37s/it]
 14%|█▍        | 518/3640 [11:56<1:11:23,  1.37s/it]
 14%|█▍        | 519/3640 [11:57<1:11:21,  1.37s/it]
 14%|█▍        | 520/3640 [11:59<1:11:17,  1.37s/it]
 14%|█▍        | 521/3640 [12:00<1:11:14,  1.37s/it]
 14%|█▍        | 522/3640 [12:01<1:11:08,  1.37s/it]
 14%|█▍        | 523/3640 [12:03<1:11:08,  1.37s/it]
 14%|█▍        | 524/3640 [12:04<1:11:07,  1.37s/it]
 14%|█▍        | 525/3640 [12:05<1:11:11,  1.37s/it]
                                                    

 14%|█▍        | 525/3640 [12:05<1:11:11,  1.37s/it]
 14%|█▍        | 526/3640 [12:07<1:11:21,  1.37s/it]
 14%|█▍        | 527/3640 [12:08<1:11:23,  1.38s/it]
 15%|█▍        | 528/3640 [12:10<1:12:20,  1.39s/it]
 15%|█▍        | 529/3640 [12:11<1:11:49,  1.39s/it]
 15%|█▍        | 530/3640 [12:12<1:11:33,  1.38s/it]
 15%|█▍        | 531/3640 [12:14<1:11:22,  1.38s/it]
 15%|█▍        | 532/3640 [12:15<1:10:40,  1.36s/it]
 15%|█▍        | 533/3640 [12:16<1:10:38,  1.36s/it]
 15%|█▍        | 534/3640 [12:18<1:10:38,  1.36s/it]
 15%|█▍        | 535/3640 [12:19<1:10:31,  1.36s/it]
 15%|█▍        | 536/3640 [12:21<1:10:42,  1.37s/it]
 15%|█▍        | 537/3640 [12:22<1:10:45,  1.37s/it]
 15%|█▍        | 538/3640 [12:23<1:10:44,  1.37s/it]
 15%|█▍        | 539/3640 [12:25<1:10:43,  1.37s/it]
 15%|█▍        | 540/3640 [12:26<1:10:41,  1.37s/it]
 15%|█▍        | 541/3640 [12:27<1:10:42,  1.37s/it]
 15%|█▍        | 542/3640 [12:29<1:10:29,  1.37s/it]
 15%|█▍        | 543/3640 [12:30<1:10:35,  1.37s/it]
 15%|█▍        | 544/3640 [12:31<1:10:37,  1.37s/it]
 15%|█▍        | 545/3640 [12:33<1:10:34,  1.37s/it]
 15%|█▌        | 546/3640 [12:34<1:10:36,  1.37s/it]
 15%|█▌        | 547/3640 [12:36<1:10:36,  1.37s/it]
 15%|█▌        | 548/3640 [12:37<1:10:35,  1.37s/it]
 15%|█▌        | 549/3640 [12:38<1:10:33,  1.37s/it]
 15%|█▌        | 550/3640 [12:40<1:10:36,  1.37s/it]
                                                    

 15%|█▌        | 550/3640 [12:40<1:10:36,  1.37s/it]
 15%|█▌        | 551/3640 [12:41<1:11:26,  1.39s/it]
 15%|█▌        | 552/3640 [12:43<1:11:51,  1.40s/it]
 15%|█▌        | 553/3640 [12:44<1:11:30,  1.39s/it]
 15%|█▌        | 554/3640 [12:45<1:11:10,  1.38s/it]
 15%|█▌        | 555/3640 [12:47<1:10:52,  1.38s/it]
 15%|█▌        | 556/3640 [12:48<1:10:45,  1.38s/it]
 15%|█▌        | 557/3640 [12:50<1:13:35,  1.43s/it]
 15%|█▌        | 558/3640 [12:51<1:12:30,  1.41s/it]
 15%|█▌        | 559/3640 [12:52<1:11:50,  1.40s/it]
 15%|█▌        | 560/3640 [12:54<1:11:12,  1.39s/it]
 15%|█▌        | 561/3640 [12:55<1:10:47,  1.38s/it]
 15%|█▌        | 562/3640 [12:56<1:10:36,  1.38s/it]
 15%|█▌        | 563/3640 [12:58<1:10:30,  1.38s/it]
 15%|█▌        | 564/3640 [12:59<1:10:28,  1.37s/it]
 16%|█▌        | 565/3640 [13:01<1:10:28,  1.38s/it]
 16%|█▌        | 566/3640 [13:02<1:10:19,  1.37s/it]
 16%|█▌        | 567/3640 [13:03<1:10:15,  1.37s/it]
 16%|█▌        | 568/3640 [13:05<1:10:18,  1.37s/it]
 16%|█▌        | 569/3640 [13:06<1:10:17,  1.37s/it]
 16%|█▌        | 570/3640 [13:07<1:10:19,  1.37s/it]
 16%|█▌        | 571/3640 [13:09<1:10:18,  1.37s/it]
 16%|█▌        | 572/3640 [13:10<1:10:14,  1.37s/it]
 16%|█▌        | 573/3640 [13:11<1:10:08,  1.37s/it]
 16%|█▌        | 574/3640 [13:13<1:10:04,  1.37s/it]
 16%|█▌        | 575/3640 [13:14<1:10:48,  1.39s/it]
                                                    

 16%|█▌        | 575/3640 [13:14<1:10:48,  1.39s/it]
 16%|█▌        | 576/3640 [13:16<1:11:27,  1.40s/it]
 16%|█▌        | 577/3640 [13:17<1:10:59,  1.39s/it]
 16%|█▌        | 578/3640 [13:18<1:10:42,  1.39s/it]
 16%|█▌        | 579/3640 [13:20<1:10:32,  1.38s/it]
 16%|█▌        | 580/3640 [13:21<1:10:19,  1.38s/it]
 16%|█▌        | 581/3640 [13:23<1:10:06,  1.38s/it]
 16%|█▌        | 582/3640 [13:24<1:10:01,  1.37s/it]
 16%|█▌        | 583/3640 [13:25<1:09:53,  1.37s/it]
 16%|█▌        | 584/3640 [13:27<1:09:46,  1.37s/it]
 16%|█▌        | 585/3640 [13:28<1:09:52,  1.37s/it]
 16%|█▌        | 586/3640 [13:29<1:09:51,  1.37s/it]
 16%|█▌        | 587/3640 [13:31<1:09:50,  1.37s/it]
 16%|█▌        | 588/3640 [13:32<1:09:46,  1.37s/it]
 16%|█▌        | 589/3640 [13:34<1:09:48,  1.37s/it]
 16%|█▌        | 590/3640 [13:35<1:09:46,  1.37s/it]
 16%|█▌        | 591/3640 [13:36<1:09:45,  1.37s/it]
 16%|█▋        | 592/3640 [13:38<1:09:40,  1.37s/it]
 16%|█▋        | 593/3640 [13:39<1:09:35,  1.37s/it]
 16%|█▋        | 594/3640 [13:40<1:09:37,  1.37s/it]
 16%|█▋        | 595/3640 [13:42<1:09:35,  1.37s/it]
 16%|█▋        | 596/3640 [13:43<1:09:13,  1.36s/it]
 16%|█▋        | 597/3640 [13:44<1:09:09,  1.36s/it]
 16%|█▋        | 598/3640 [13:46<1:09:11,  1.36s/it]
 16%|█▋        | 599/3640 [13:47<1:10:12,  1.39s/it]
 16%|█▋        | 600/3640 [13:49<1:09:57,  1.38s/it]
                                                    

 16%|█▋        | 600/3640 [13:49<1:09:57,  1.38s/it]
 17%|█▋        | 601/3640 [13:50<1:10:35,  1.39s/it]
 17%|█▋        | 602/3640 [13:51<1:10:16,  1.39s/it]
 17%|█▋        | 603/3640 [13:53<1:09:55,  1.38s/it]
 17%|█▋        | 604/3640 [13:54<1:09:41,  1.38s/it]
 17%|█▋        | 605/3640 [13:56<1:09:35,  1.38s/it]
 17%|█▋        | 606/3640 [13:57<1:09:27,  1.37s/it]
 17%|█▋        | 607/3640 [13:58<1:09:12,  1.37s/it]
 17%|█▋        | 608/3640 [14:00<1:09:14,  1.37s/it]
 17%|█▋        | 609/3640 [14:01<1:09:16,  1.37s/it]
 17%|█▋        | 610/3640 [14:02<1:09:16,  1.37s/it]
 17%|█▋        | 611/3640 [14:04<1:09:04,  1.37s/it]
 17%|█▋        | 612/3640 [14:05<1:08:57,  1.37s/it]
 17%|█▋        | 613/3640 [14:06<1:08:51,  1.37s/it]
 17%|█▋        | 614/3640 [14:08<1:08:49,  1.36s/it]
 17%|█▋        | 615/3640 [14:09<1:08:55,  1.37s/it]
 17%|█▋        | 616/3640 [14:11<1:09:03,  1.37s/it]
 17%|█▋        | 617/3640 [14:12<1:08:59,  1.37s/it]
 17%|█▋        | 618/3640 [14:13<1:08:59,  1.37s/it]
 17%|█▋        | 619/3640 [14:15<1:08:54,  1.37s/it]
 17%|█▋        | 620/3640 [14:16<1:08:58,  1.37s/it]
 17%|█▋        | 621/3640 [14:17<1:08:56,  1.37s/it]
 17%|█▋        | 622/3640 [14:19<1:08:54,  1.37s/it]
 17%|█▋        | 623/3640 [14:20<1:09:50,  1.39s/it]
 17%|█▋        | 624/3640 [14:22<1:09:33,  1.38s/it]
 17%|█▋        | 625/3640 [14:23<1:09:15,  1.38s/it]
                                                    

 17%|█▋        | 625/3640 [14:23<1:09:15,  1.38s/it]
 17%|█▋        | 626/3640 [14:24<1:09:42,  1.39s/it]
 17%|█▋        | 627/3640 [14:26<1:09:15,  1.38s/it]
 17%|█▋        | 628/3640 [14:27<1:09:03,  1.38s/it]
 17%|█▋        | 629/3640 [14:28<1:08:55,  1.37s/it]
 17%|█▋        | 630/3640 [14:30<1:08:54,  1.37s/it]
 17%|█▋        | 631/3640 [14:31<1:08:49,  1.37s/it]
 17%|█▋        | 632/3640 [14:33<1:08:33,  1.37s/it]
 17%|█▋        | 633/3640 [14:34<1:08:38,  1.37s/it]
 17%|█▋        | 634/3640 [14:35<1:08:40,  1.37s/it]
 17%|█▋        | 635/3640 [14:37<1:08:32,  1.37s/it]
 17%|█▋        | 636/3640 [14:38<1:08:34,  1.37s/it]
 18%|█▊        | 637/3640 [14:39<1:08:31,  1.37s/it]
 18%|█▊        | 638/3640 [14:41<1:08:31,  1.37s/it]
 18%|█▊        | 639/3640 [14:42<1:08:04,  1.36s/it]
 18%|█▊        | 640/3640 [14:44<1:11:18,  1.43s/it]
 18%|█▊        | 641/3640 [14:45<1:10:31,  1.41s/it]
 18%|█▊        | 642/3640 [14:46<1:09:54,  1.40s/it]
 18%|█▊        | 643/3640 [14:48<1:09:24,  1.39s/it]
 18%|█▊        | 644/3640 [14:49<1:09:12,  1.39s/it]
 18%|█▊        | 645/3640 [14:51<1:08:58,  1.38s/it]
 18%|█▊        | 646/3640 [14:52<1:08:48,  1.38s/it]
 18%|█▊        | 647/3640 [14:53<1:09:29,  1.39s/it]
 18%|█▊        | 648/3640 [14:55<1:08:47,  1.38s/it]
 18%|█▊        | 649/3640 [14:56<1:08:28,  1.37s/it]
 18%|█▊        | 650/3640 [14:57<1:08:19,  1.37s/it]
                                                    

 18%|█▊        | 650/3640 [14:58<1:08:19,  1.37s/it]
 18%|█▊        | 651/3640 [14:59<1:08:34,  1.38s/it]
 18%|█▊        | 652/3640 [15:00<1:08:33,  1.38s/it]
 18%|█▊        | 653/3640 [15:02<1:08:24,  1.37s/it]
 18%|█▊        | 654/3640 [15:03<1:08:20,  1.37s/it]
 18%|█▊        | 655/3640 [15:04<1:08:20,  1.37s/it]
 18%|█▊        | 656/3640 [15:06<1:08:20,  1.37s/it]
 18%|█▊        | 657/3640 [15:07<1:08:21,  1.38s/it]
 18%|█▊        | 658/3640 [15:08<1:08:14,  1.37s/it]
 18%|█▊        | 659/3640 [15:10<1:08:08,  1.37s/it]
 18%|█▊        | 660/3640 [15:11<1:08:00,  1.37s/it]
 18%|█▊        | 661/3640 [15:13<1:08:02,  1.37s/it]
 18%|█▊        | 662/3640 [15:14<1:08:01,  1.37s/it]
 18%|█▊        | 663/3640 [15:15<1:10:41,  1.42s/it]
 18%|█▊        | 664/3640 [15:17<1:09:51,  1.41s/it]
 18%|█▊        | 665/3640 [15:18<1:09:17,  1.40s/it]
 18%|█▊        | 666/3640 [15:20<1:08:51,  1.39s/it]
 18%|█▊        | 667/3640 [15:21<1:08:31,  1.38s/it]
 18%|█▊        | 668/3640 [15:22<1:08:19,  1.38s/it]
 18%|█▊        | 669/3640 [15:24<1:07:55,  1.37s/it]
 18%|█▊        | 670/3640 [15:25<1:08:26,  1.38s/it]
 18%|█▊        | 671/3640 [15:27<1:08:40,  1.39s/it]
 18%|█▊        | 672/3640 [15:28<1:08:29,  1.38s/it]
 18%|█▊        | 673/3640 [15:29<1:08:00,  1.38s/it]
 19%|█▊        | 674/3640 [15:31<1:07:49,  1.37s/it]
 19%|█▊        | 675/3640 [15:32<1:07:43,  1.37s/it]
                                                    

 19%|█▊        | 675/3640 [15:32<1:07:43,  1.37s/it]
 19%|█▊        | 676/3640 [15:33<1:08:29,  1.39s/it]
 19%|█▊        | 677/3640 [15:35<1:08:13,  1.38s/it]
 19%|█▊        | 678/3640 [15:36<1:07:59,  1.38s/it]
 19%|█▊        | 679/3640 [15:38<1:07:52,  1.38s/it]
 19%|█▊        | 680/3640 [15:39<1:07:44,  1.37s/it]
 19%|█▊        | 681/3640 [15:40<1:07:38,  1.37s/it]
 19%|█▊        | 682/3640 [15:42<1:07:33,  1.37s/it]
 19%|█▉        | 683/3640 [15:43<1:07:35,  1.37s/it]
 19%|█▉        | 684/3640 [15:44<1:07:36,  1.37s/it]
 19%|█▉        | 685/3640 [15:46<1:07:32,  1.37s/it]
 19%|█▉        | 686/3640 [15:47<1:07:32,  1.37s/it]
 19%|█▉        | 687/3640 [15:48<1:07:33,  1.37s/it]
 19%|█▉        | 688/3640 [15:50<1:07:32,  1.37s/it]
 19%|█▉        | 689/3640 [15:51<1:07:26,  1.37s/it]
 19%|█▉        | 690/3640 [15:53<1:07:19,  1.37s/it]
 19%|█▉        | 691/3640 [15:54<1:07:05,  1.37s/it]
 19%|█▉        | 692/3640 [15:55<1:08:40,  1.40s/it]
 19%|█▉        | 693/3640 [15:57<1:08:14,  1.39s/it]
 19%|█▉        | 694/3640 [15:58<1:08:45,  1.40s/it]
 19%|█▉        | 695/3640 [16:00<1:08:52,  1.40s/it]
 19%|█▉        | 696/3640 [16:01<1:08:23,  1.39s/it]
 19%|█▉        | 697/3640 [16:02<1:07:59,  1.39s/it]
 19%|█▉        | 698/3640 [16:04<1:07:45,  1.38s/it]
 19%|█▉        | 699/3640 [16:05<1:07:33,  1.38s/it]
 19%|█▉        | 700/3640 [16:06<1:07:21,  1.37s/it]
                                                    

 19%|█▉        | 700/3640 [16:07<1:07:21,  1.37s/it]
 19%|█▉        | 701/3640 [16:08<1:08:00,  1.39s/it]
 19%|█▉        | 702/3640 [16:09<1:07:37,  1.38s/it]
 19%|█▉        | 703/3640 [16:11<1:07:27,  1.38s/it]
 19%|█▉        | 704/3640 [16:12<1:07:18,  1.38s/it]
 19%|█▉        | 705/3640 [16:13<1:07:07,  1.37s/it]
 19%|█▉        | 706/3640 [16:15<1:07:06,  1.37s/it]
 19%|█▉        | 707/3640 [16:16<1:07:01,  1.37s/it]
 19%|█▉        | 708/3640 [16:17<1:06:53,  1.37s/it]
 19%|█▉        | 709/3640 [16:19<1:06:58,  1.37s/it]
 20%|█▉        | 710/3640 [16:20<1:06:53,  1.37s/it]
 20%|█▉        | 711/3640 [16:22<1:06:46,  1.37s/it]
 20%|█▉        | 712/3640 [16:23<1:06:39,  1.37s/it]
 20%|█▉        | 713/3640 [16:24<1:06:41,  1.37s/it]
 20%|█▉        | 714/3640 [16:26<1:06:40,  1.37s/it]
 20%|█▉        | 715/3640 [16:27<1:06:43,  1.37s/it]
 20%|█▉        | 716/3640 [16:28<1:06:43,  1.37s/it]
 20%|█▉        | 717/3640 [16:30<1:06:42,  1.37s/it]
 20%|█▉        | 718/3640 [16:31<1:06:33,  1.37s/it]
 20%|█▉        | 719/3640 [16:33<1:06:38,  1.37s/it]
 20%|█▉        | 720/3640 [16:34<1:07:24,  1.39s/it]
 20%|█▉        | 721/3640 [16:35<1:07:12,  1.38s/it]
 20%|█▉        | 722/3640 [16:37<1:06:53,  1.38s/it]
 20%|█▉        | 723/3640 [16:38<1:06:42,  1.37s/it]
 20%|█▉        | 724/3640 [16:39<1:06:38,  1.37s/it]
 20%|█▉        | 725/3640 [16:41<1:06:38,  1.37s/it]
                                                    

 20%|█▉        | 725/3640 [16:41<1:06:38,  1.37s/it]
 20%|█▉        | 726/3640 [16:42<1:06:51,  1.38s/it]
 20%|█▉        | 727/3640 [16:44<1:06:47,  1.38s/it]
 20%|██        | 728/3640 [16:45<1:06:40,  1.37s/it]
 20%|██        | 729/3640 [16:46<1:06:38,  1.37s/it]
 20%|██        | 730/3640 [16:48<1:06:37,  1.37s/it]
 20%|██        | 731/3640 [16:49<1:06:37,  1.37s/it]
 20%|██        | 732/3640 [16:50<1:06:22,  1.37s/it]
 20%|██        | 733/3640 [16:52<1:06:10,  1.37s/it]
 20%|██        | 734/3640 [16:53<1:06:02,  1.36s/it]
 20%|██        | 735/3640 [16:54<1:06:07,  1.37s/it]
 20%|██        | 736/3640 [16:56<1:05:58,  1.36s/it]
 20%|██        | 737/3640 [16:57<1:08:30,  1.42s/it]
 20%|██        | 738/3640 [16:59<1:07:33,  1.40s/it]
 20%|██        | 739/3640 [17:00<1:07:06,  1.39s/it]
 20%|██        | 740/3640 [17:01<1:06:47,  1.38s/it]
 20%|██        | 741/3640 [17:03<1:06:38,  1.38s/it]
 20%|██        | 742/3640 [17:04<1:06:25,  1.38s/it]
 20%|██        | 743/3640 [17:06<1:07:01,  1.39s/it]
 20%|██        | 744/3640 [17:07<1:06:52,  1.39s/it]
 20%|██        | 745/3640 [17:08<1:06:35,  1.38s/it]
 20%|██        | 746/3640 [17:10<1:06:27,  1.38s/it]
 21%|██        | 747/3640 [17:11<1:09:28,  1.44s/it]
 21%|██        | 748/3640 [17:13<1:08:25,  1.42s/it]
 21%|██        | 749/3640 [17:14<1:07:41,  1.41s/it]
 21%|██        | 750/3640 [17:15<1:07:13,  1.40s/it]
                                                    

 21%|██        | 750/3640 [17:15<1:07:13,  1.40s/it]
 21%|██        | 751/3640 [17:17<1:07:30,  1.40s/it]
 21%|██        | 752/3640 [17:18<1:06:56,  1.39s/it]
 21%|██        | 753/3640 [17:20<1:06:46,  1.39s/it]
 21%|██        | 754/3640 [17:21<1:06:30,  1.38s/it]
 21%|██        | 755/3640 [17:22<1:06:07,  1.38s/it]
 21%|██        | 756/3640 [17:24<1:05:53,  1.37s/it]
 21%|██        | 757/3640 [17:25<1:05:44,  1.37s/it]
 21%|██        | 758/3640 [17:26<1:05:36,  1.37s/it]
 21%|██        | 759/3640 [17:28<1:05:41,  1.37s/it]
 21%|██        | 760/3640 [17:29<1:05:43,  1.37s/it]
 21%|██        | 761/3640 [17:31<1:05:37,  1.37s/it]
 21%|██        | 762/3640 [17:32<1:05:33,  1.37s/it]
 21%|██        | 763/3640 [17:33<1:05:37,  1.37s/it]
 21%|██        | 764/3640 [17:35<1:05:31,  1.37s/it]
 21%|██        | 765/3640 [17:36<1:05:33,  1.37s/it]
 21%|██        | 766/3640 [17:37<1:05:28,  1.37s/it]
 21%|██        | 767/3640 [17:39<1:06:26,  1.39s/it]
 21%|██        | 768/3640 [17:40<1:06:11,  1.38s/it]
 21%|██        | 769/3640 [17:42<1:05:51,  1.38s/it]
 21%|██        | 770/3640 [17:43<1:05:40,  1.37s/it]
 21%|██        | 771/3640 [17:44<1:05:44,  1.37s/it]
 21%|██        | 772/3640 [17:46<1:05:42,  1.37s/it]
 21%|██        | 773/3640 [17:47<1:05:36,  1.37s/it]
 21%|██▏       | 774/3640 [17:48<1:05:23,  1.37s/it]
 21%|██▏       | 775/3640 [17:50<1:05:23,  1.37s/it]
                                                    

 21%|██▏       | 775/3640 [17:50<1:05:23,  1.37s/it]
 21%|██▏       | 776/3640 [17:51<1:05:44,  1.38s/it]
 21%|██▏       | 777/3640 [17:53<1:05:35,  1.37s/it]
 21%|██▏       | 778/3640 [17:54<1:05:32,  1.37s/it]
 21%|██▏       | 779/3640 [17:55<1:05:35,  1.38s/it]
 21%|██▏       | 780/3640 [17:57<1:05:23,  1.37s/it]
 21%|██▏       | 781/3640 [17:58<1:05:22,  1.37s/it]
 21%|██▏       | 782/3640 [17:59<1:05:20,  1.37s/it]
 22%|██▏       | 783/3640 [18:01<1:05:13,  1.37s/it]
 22%|██▏       | 784/3640 [18:02<1:05:05,  1.37s/it]
 22%|██▏       | 785/3640 [18:03<1:05:10,  1.37s/it]
 22%|██▏       | 786/3640 [18:05<1:05:18,  1.37s/it]
 22%|██▏       | 787/3640 [18:06<1:05:15,  1.37s/it]
 22%|██▏       | 788/3640 [18:08<1:05:01,  1.37s/it]
 22%|██▏       | 789/3640 [18:09<1:04:50,  1.36s/it]
 22%|██▏       | 790/3640 [18:10<1:04:48,  1.36s/it]
 22%|██▏       | 791/3640 [18:12<1:05:42,  1.38s/it]
 22%|██▏       | 792/3640 [18:13<1:05:24,  1.38s/it]
 22%|██▏       | 793/3640 [18:14<1:05:20,  1.38s/it]
 22%|██▏       | 794/3640 [18:16<1:05:18,  1.38s/it]
 22%|██▏       | 795/3640 [18:17<1:05:05,  1.37s/it]
 22%|██▏       | 796/3640 [18:19<1:05:01,  1.37s/it]
 22%|██▏       | 797/3640 [18:20<1:05:00,  1.37s/it]
 22%|██▏       | 798/3640 [18:21<1:04:46,  1.37s/it]
 22%|██▏       | 799/3640 [18:23<1:04:44,  1.37s/it]
 22%|██▏       | 800/3640 [18:24<1:04:41,  1.37s/it]
                                                    

 22%|██▏       | 800/3640 [18:24<1:04:41,  1.37s/it]
 22%|██▏       | 801/3640 [18:25<1:05:19,  1.38s/it]
 22%|██▏       | 802/3640 [18:27<1:05:02,  1.38s/it]
 22%|██▏       | 803/3640 [18:28<1:04:55,  1.37s/it]
 22%|██▏       | 804/3640 [18:30<1:04:53,  1.37s/it]
 22%|██▏       | 805/3640 [18:31<1:04:45,  1.37s/it]
 22%|██▏       | 806/3640 [18:32<1:04:44,  1.37s/it]
 22%|██▏       | 807/3640 [18:34<1:04:41,  1.37s/it]
 22%|██▏       | 808/3640 [18:35<1:04:49,  1.37s/it]
 22%|██▏       | 809/3640 [18:36<1:04:37,  1.37s/it]
 22%|██▏       | 810/3640 [18:38<1:04:41,  1.37s/it]
 22%|██▏       | 811/3640 [18:39<1:04:42,  1.37s/it]
 22%|██▏       | 812/3640 [18:41<1:04:37,  1.37s/it]
 22%|██▏       | 813/3640 [18:42<1:04:34,  1.37s/it]
 22%|██▏       | 814/3640 [18:43<1:04:20,  1.37s/it]
 22%|██▏       | 815/3640 [18:45<1:05:22,  1.39s/it]
 22%|██▏       | 816/3640 [18:46<1:04:58,  1.38s/it]
 22%|██▏       | 817/3640 [18:47<1:04:45,  1.38s/it]
 22%|██▏       | 818/3640 [18:49<1:04:32,  1.37s/it]
 22%|██▎       | 819/3640 [18:50<1:04:35,  1.37s/it]
 23%|██▎       | 820/3640 [18:52<1:07:20,  1.43s/it]
 23%|██▎       | 821/3640 [18:53<1:06:22,  1.41s/it]
 23%|██▎       | 822/3640 [18:54<1:05:44,  1.40s/it]
 23%|██▎       | 823/3640 [18:56<1:05:24,  1.39s/it]
 23%|██▎       | 824/3640 [18:57<1:05:04,  1.39s/it]
 23%|██▎       | 825/3640 [18:59<1:04:51,  1.38s/it]
                                                    

 23%|██▎       | 825/3640 [18:59<1:04:51,  1.38s/it]
 23%|██▎       | 826/3640 [19:00<1:05:22,  1.39s/it]
 23%|██▎       | 827/3640 [19:01<1:05:01,  1.39s/it]
 23%|██▎       | 828/3640 [19:03<1:04:36,  1.38s/it]
 23%|██▎       | 829/3640 [19:04<1:04:26,  1.38s/it]
 23%|██▎       | 830/3640 [19:05<1:04:25,  1.38s/it]
 23%|██▎       | 831/3640 [19:07<1:04:26,  1.38s/it]
 23%|██▎       | 832/3640 [19:08<1:04:20,  1.37s/it]
 23%|██▎       | 833/3640 [19:10<1:04:19,  1.37s/it]
 23%|██▎       | 834/3640 [19:11<1:04:12,  1.37s/it]
 23%|██▎       | 835/3640 [19:12<1:04:11,  1.37s/it]
 23%|██▎       | 836/3640 [19:14<1:04:04,  1.37s/it]
 23%|██▎       | 837/3640 [19:15<1:04:02,  1.37s/it]
 23%|██▎       | 838/3640 [19:16<1:04:30,  1.38s/it]
 23%|██▎       | 839/3640 [19:18<1:04:46,  1.39s/it]
 23%|██▎       | 840/3640 [19:19<1:04:30,  1.38s/it]
 23%|██▎       | 841/3640 [19:21<1:04:18,  1.38s/it]
 23%|██▎       | 842/3640 [19:22<1:04:16,  1.38s/it]
 23%|██▎       | 843/3640 [19:23<1:04:08,  1.38s/it]
 23%|██▎       | 844/3640 [19:25<1:04:10,  1.38s/it]
 23%|██▎       | 845/3640 [19:26<1:04:04,  1.38s/it]
 23%|██▎       | 846/3640 [19:27<1:03:58,  1.37s/it]
 23%|██▎       | 847/3640 [19:29<1:03:57,  1.37s/it]
 23%|██▎       | 848/3640 [19:30<1:03:47,  1.37s/it]
 23%|██▎       | 849/3640 [19:32<1:03:50,  1.37s/it]
 23%|██▎       | 850/3640 [19:33<1:03:47,  1.37s/it]
                                                    

 23%|██▎       | 850/3640 [19:33<1:03:47,  1.37s/it]
 23%|██▎       | 851/3640 [19:34<1:03:51,  1.37s/it]
 23%|██▎       | 852/3640 [19:36<1:03:47,  1.37s/it]
 23%|██▎       | 853/3640 [19:37<1:03:49,  1.37s/it]
 23%|██▎       | 854/3640 [19:38<1:03:35,  1.37s/it]
 23%|██▎       | 855/3640 [19:40<1:03:26,  1.37s/it]
 24%|██▎       | 856/3640 [19:41<1:03:24,  1.37s/it]
 24%|██▎       | 857/3640 [19:43<1:03:26,  1.37s/it]
 24%|██▎       | 858/3640 [19:44<1:03:25,  1.37s/it]
 24%|██▎       | 859/3640 [19:45<1:03:24,  1.37s/it]
 24%|██▎       | 860/3640 [19:47<1:03:21,  1.37s/it]
 24%|██▎       | 861/3640 [19:48<1:03:28,  1.37s/it]
 24%|██▎       | 862/3640 [19:49<1:04:04,  1.38s/it]
 24%|██▎       | 863/3640 [19:51<1:04:10,  1.39s/it]
 24%|██▎       | 864/3640 [19:52<1:03:45,  1.38s/it]
 24%|██▍       | 865/3640 [19:54<1:03:30,  1.37s/it]
 24%|██▍       | 866/3640 [19:55<1:03:35,  1.38s/it]
 24%|██▍       | 867/3640 [19:56<1:03:40,  1.38s/it]
 24%|██▍       | 868/3640 [19:58<1:03:32,  1.38s/it]
 24%|██▍       | 869/3640 [19:59<1:03:28,  1.37s/it]
 24%|██▍       | 870/3640 [20:00<1:03:29,  1.38s/it]
 24%|██▍       | 871/3640 [20:02<1:03:25,  1.37s/it]
 24%|██▍       | 872/3640 [20:03<1:03:18,  1.37s/it]
 24%|██▍       | 873/3640 [20:05<1:03:18,  1.37s/it]
 24%|██▍       | 874/3640 [20:06<1:03:13,  1.37s/it]
 24%|██▍       | 875/3640 [20:07<1:03:04,  1.37s/it]
                                                    

 24%|██▍       | 875/3640 [20:07<1:03:04,  1.37s/it]
 24%|██▍       | 876/3640 [20:09<1:03:45,  1.38s/it]
 24%|██▍       | 877/3640 [20:10<1:03:32,  1.38s/it]
 24%|██▍       | 878/3640 [20:11<1:03:25,  1.38s/it]
 24%|██▍       | 879/3640 [20:13<1:03:16,  1.37s/it]
 24%|██▍       | 880/3640 [20:14<1:03:06,  1.37s/it]
 24%|██▍       | 881/3640 [20:16<1:03:14,  1.38s/it]
 24%|██▍       | 882/3640 [20:17<1:03:06,  1.37s/it]
 24%|██▍       | 883/3640 [20:18<1:02:58,  1.37s/it]
 24%|██▍       | 884/3640 [20:20<1:02:55,  1.37s/it]
 24%|██▍       | 885/3640 [20:21<1:02:50,  1.37s/it]
 24%|██▍       | 886/3640 [20:22<1:03:43,  1.39s/it]
 24%|██▍       | 887/3640 [20:24<1:03:27,  1.38s/it]
 24%|██▍       | 888/3640 [20:25<1:03:20,  1.38s/it]
 24%|██▍       | 889/3640 [20:27<1:03:01,  1.37s/it]
 24%|██▍       | 890/3640 [20:28<1:02:47,  1.37s/it]
 24%|██▍       | 891/3640 [20:29<1:02:47,  1.37s/it]
 25%|██▍       | 892/3640 [20:31<1:02:41,  1.37s/it]
 25%|██▍       | 893/3640 [20:32<1:02:44,  1.37s/it]
 25%|██▍       | 894/3640 [20:33<1:02:42,  1.37s/it]
 25%|██▍       | 895/3640 [20:35<1:02:47,  1.37s/it]
 25%|██▍       | 896/3640 [20:36<1:02:49,  1.37s/it]
 25%|██▍       | 897/3640 [20:38<1:02:46,  1.37s/it]
 25%|██▍       | 898/3640 [20:39<1:02:37,  1.37s/it]
 25%|██▍       | 899/3640 [20:40<1:02:44,  1.37s/it]
 25%|██▍       | 900/3640 [20:42<1:02:22,  1.37s/it]
                                                    

 25%|██▍       | 900/3640 [20:42<1:02:22,  1.37s/it]
 25%|██▍       | 901/3640 [20:43<1:03:00,  1.38s/it]
 25%|██▍       | 902/3640 [20:44<1:02:39,  1.37s/it]
 25%|██▍       | 903/3640 [20:46<1:02:37,  1.37s/it]
 25%|██▍       | 904/3640 [20:47<1:02:30,  1.37s/it]
 25%|██▍       | 905/3640 [20:49<1:02:29,  1.37s/it]
 25%|██▍       | 906/3640 [20:50<1:02:36,  1.37s/it]
 25%|██▍       | 907/3640 [20:51<1:02:34,  1.37s/it]
 25%|██▍       | 908/3640 [20:53<1:02:26,  1.37s/it]
 25%|██▍       | 909/3640 [20:54<1:02:21,  1.37s/it]
 25%|██▌       | 910/3640 [20:55<1:03:09,  1.39s/it]
 25%|██▌       | 911/3640 [20:57<1:02:56,  1.38s/it]
 25%|██▌       | 912/3640 [20:58<1:02:44,  1.38s/it]
 25%|██▌       | 913/3640 [21:00<1:02:36,  1.38s/it]
 25%|██▌       | 914/3640 [21:01<1:02:33,  1.38s/it]
 25%|██▌       | 915/3640 [21:02<1:02:28,  1.38s/it]
 25%|██▌       | 916/3640 [21:04<1:02:33,  1.38s/it]
 25%|██▌       | 917/3640 [21:05<1:02:21,  1.37s/it]
 25%|██▌       | 918/3640 [21:06<1:02:17,  1.37s/it]
 25%|██▌       | 919/3640 [21:08<1:02:22,  1.38s/it]
 25%|██▌       | 920/3640 [21:09<1:02:19,  1.37s/it]
 25%|██▌       | 921/3640 [21:11<1:02:11,  1.37s/it]
 25%|██▌       | 922/3640 [21:12<1:02:10,  1.37s/it]
 25%|██▌       | 923/3640 [21:13<1:02:01,  1.37s/it]
 25%|██▌       | 924/3640 [21:15<1:02:05,  1.37s/it]
 25%|██▌       | 925/3640 [21:16<1:02:07,  1.37s/it]
                                                    

 25%|██▌       | 925/3640 [21:16<1:02:07,  1.37s/it]
 25%|██▌       | 926/3640 [21:17<1:02:36,  1.38s/it]
 25%|██▌       | 927/3640 [21:19<1:02:25,  1.38s/it]
 25%|██▌       | 928/3640 [21:20<1:02:17,  1.38s/it]
 26%|██▌       | 929/3640 [21:22<1:02:11,  1.38s/it]
 26%|██▌       | 930/3640 [21:23<1:02:03,  1.37s/it]
 26%|██▌       | 931/3640 [21:24<1:01:55,  1.37s/it]
 26%|██▌       | 932/3640 [21:26<1:01:59,  1.37s/it]
 26%|██▌       | 933/3640 [21:27<1:01:56,  1.37s/it]
 26%|██▌       | 934/3640 [21:28<1:02:49,  1.39s/it]
 26%|██▌       | 935/3640 [21:30<1:02:33,  1.39s/it]
 26%|██▌       | 936/3640 [21:31<1:02:13,  1.38s/it]
 26%|██▌       | 937/3640 [21:33<1:02:01,  1.38s/it]
 26%|██▌       | 938/3640 [21:34<1:01:53,  1.37s/it]
 26%|██▌       | 939/3640 [21:35<1:01:50,  1.37s/it]
 26%|██▌       | 940/3640 [21:37<1:01:44,  1.37s/it]
 26%|██▌       | 941/3640 [21:38<1:01:27,  1.37s/it]
 26%|██▌       | 942/3640 [21:39<1:01:31,  1.37s/it]
 26%|██▌       | 943/3640 [21:41<1:01:32,  1.37s/it]
 26%|██▌       | 944/3640 [21:42<1:01:34,  1.37s/it]
 26%|██▌       | 945/3640 [21:44<1:04:01,  1.43s/it]
 26%|██▌       | 946/3640 [21:45<1:03:27,  1.41s/it]
 26%|██▌       | 947/3640 [21:46<1:02:53,  1.40s/it]
 26%|██▌       | 948/3640 [21:48<1:02:32,  1.39s/it]
 26%|██▌       | 949/3640 [21:49<1:02:07,  1.39s/it]
 26%|██▌       | 950/3640 [21:51<1:02:03,  1.38s/it]
                                                    

 26%|██▌       | 950/3640 [21:51<1:02:03,  1.38s/it]
 26%|██▌       | 951/3640 [21:52<1:02:30,  1.39s/it]
 26%|██▌       | 952/3640 [21:53<1:02:06,  1.39s/it]
 26%|██▌       | 953/3640 [21:55<1:01:42,  1.38s/it]
 26%|██▌       | 954/3640 [21:56<1:01:37,  1.38s/it]
 26%|██▌       | 955/3640 [21:57<1:01:31,  1.37s/it]
 26%|██▋       | 956/3640 [21:59<1:01:28,  1.37s/it]
 26%|██▋       | 957/3640 [22:00<1:01:47,  1.38s/it]
 26%|██▋       | 958/3640 [22:02<1:02:05,  1.39s/it]
 26%|██▋       | 959/3640 [22:03<1:01:49,  1.38s/it]
 26%|██▋       | 960/3640 [22:04<1:01:39,  1.38s/it]
 26%|██▋       | 961/3640 [22:06<1:01:37,  1.38s/it]
 26%|██▋       | 962/3640 [22:07<1:01:27,  1.38s/it]
 26%|██▋       | 963/3640 [22:09<1:01:03,  1.37s/it]
 26%|██▋       | 964/3640 [22:10<1:01:05,  1.37s/it]
 27%|██▋       | 965/3640 [22:11<1:01:06,  1.37s/it]
 27%|██▋       | 966/3640 [22:13<1:01:08,  1.37s/it]
 27%|██▋       | 967/3640 [22:14<1:01:07,  1.37s/it]
 27%|██▋       | 968/3640 [22:15<1:01:09,  1.37s/it]
 27%|██▋       | 969/3640 [22:17<1:01:03,  1.37s/it]
 27%|██▋       | 970/3640 [22:18<1:00:58,  1.37s/it]
 27%|██▋       | 971/3640 [22:19<1:00:58,  1.37s/it]
 27%|██▋       | 972/3640 [22:21<1:01:05,  1.37s/it]
 27%|██▋       | 973/3640 [22:22<1:00:54,  1.37s/it]
 27%|██▋       | 974/3640 [22:24<1:00:56,  1.37s/it]
 27%|██▋       | 975/3640 [22:25<1:01:05,  1.38s/it]
                                                    

 27%|██▋       | 975/3640 [22:25<1:01:05,  1.38s/it]
 27%|██▋       | 976/3640 [22:26<1:01:29,  1.38s/it]
 27%|██▋       | 977/3640 [22:28<1:01:18,  1.38s/it]
 27%|██▋       | 978/3640 [22:29<1:01:01,  1.38s/it]
 27%|██▋       | 979/3640 [22:31<1:01:06,  1.38s/it]
 27%|██▋       | 980/3640 [22:32<1:01:02,  1.38s/it]
 27%|██▋       | 981/3640 [22:33<1:01:34,  1.39s/it]
 27%|██▋       | 982/3640 [22:35<1:01:29,  1.39s/it]
 27%|██▋       | 983/3640 [22:36<1:01:21,  1.39s/it]
 27%|██▋       | 984/3640 [22:37<1:01:09,  1.38s/it]
 27%|██▋       | 985/3640 [22:39<1:01:00,  1.38s/it]
 27%|██▋       | 986/3640 [22:40<1:01:01,  1.38s/it]
 27%|██▋       | 987/3640 [22:42<1:00:55,  1.38s/it]
 27%|██▋       | 988/3640 [22:43<1:00:50,  1.38s/it]
 27%|██▋       | 989/3640 [22:44<1:00:43,  1.37s/it]
 27%|██▋       | 990/3640 [22:46<1:00:38,  1.37s/it]
 27%|██▋       | 991/3640 [22:47<1:00:33,  1.37s/it]
 27%|██▋       | 992/3640 [22:48<1:00:18,  1.37s/it]
 27%|██▋       | 993/3640 [22:50<1:00:17,  1.37s/it]
 27%|██▋       | 994/3640 [22:51<1:00:12,  1.37s/it]
 27%|██▋       | 995/3640 [22:53<1:00:15,  1.37s/it]
 27%|██▋       | 996/3640 [22:54<1:00:17,  1.37s/it]
 27%|██▋       | 997/3640 [22:55<1:00:26,  1.37s/it]
 27%|██▋       | 998/3640 [22:57<1:00:21,  1.37s/it]
 27%|██▋       | 999/3640 [22:58<1:00:18,  1.37s/it]
 27%|██▋       | 1000/3640 [22:59<1:00:17,  1.37s/it]
                                                     

 27%|██▋       | 1000/3640 [22:59<1:00:17,  1.37s/it]
 28%|██▊       | 1001/3640 [23:01<1:01:03,  1.39s/it]
 28%|██▊       | 1002/3640 [23:02<1:00:40,  1.38s/it]
 28%|██▊       | 1003/3640 [23:04<1:00:26,  1.38s/it]
 28%|██▊       | 1004/3640 [23:05<1:00:28,  1.38s/it]
 28%|██▊       | 1005/3640 [23:06<1:01:05,  1.39s/it]
 28%|██▊       | 1006/3640 [23:08<1:00:48,  1.38s/it]
 28%|██▊       | 1007/3640 [23:09<1:00:33,  1.38s/it]
 28%|██▊       | 1008/3640 [23:10<1:00:30,  1.38s/it]
 28%|██▊       | 1009/3640 [23:12<1:00:20,  1.38s/it]
 28%|██▊       | 1010/3640 [23:13<1:00:11,  1.37s/it]
 28%|██▊       | 1011/3640 [23:15<1:00:04,  1.37s/it]
 28%|██▊       | 1012/3640 [23:16<1:00:11,  1.37s/it]
 28%|██▊       | 1013/3640 [23:18<1:02:55,  1.44s/it]
 28%|██▊       | 1014/3640 [23:19<1:02:05,  1.42s/it]
 28%|██▊       | 1015/3640 [23:20<1:01:24,  1.40s/it]
 28%|██▊       | 1016/3640 [23:22<1:00:53,  1.39s/it]
 28%|██▊       | 1017/3640 [23:23<1:00:24,  1.38s/it]
 28%|██▊       | 1018/3640 [23:24<1:00:15,  1.38s/it]
 28%|██▊       | 1019/3640 [23:26<1:00:14,  1.38s/it]
 28%|██▊       | 1020/3640 [23:27<1:00:08,  1.38s/it]
 28%|██▊       | 1021/3640 [23:28<1:00:01,  1.37s/it]
 28%|██▊       | 1022/3640 [23:30<1:00:09,  1.38s/it]
 28%|██▊       | 1023/3640 [23:31<1:00:01,  1.38s/it]
 28%|██▊       | 1024/3640 [23:33<59:58,  1.38s/it]  
 28%|██▊       | 1025/3640 [23:34<59:47,  1.37s/it]
                                                   

 28%|██▊       | 1025/3640 [23:34<59:47,  1.37s/it]
 28%|██▊       | 1026/3640 [23:35<1:00:15,  1.38s/it]
 28%|██▊       | 1027/3640 [23:37<1:00:00,  1.38s/it]
 28%|██▊       | 1028/3640 [23:38<59:55,  1.38s/it]  
 28%|██▊       | 1029/3640 [23:40<1:00:27,  1.39s/it]
 28%|██▊       | 1030/3640 [23:41<1:00:17,  1.39s/it]
 28%|██▊       | 1031/3640 [23:42<1:00:03,  1.38s/it]
 28%|██▊       | 1032/3640 [23:44<59:54,  1.38s/it]  
 28%|██▊       | 1033/3640 [23:45<59:58,  1.38s/it]
 28%|██▊       | 1034/3640 [23:46<59:44,  1.38s/it]
 28%|██▊       | 1035/3640 [23:48<59:36,  1.37s/it]
 28%|██▊       | 1036/3640 [23:49<59:33,  1.37s/it]
 28%|██▊       | 1037/3640 [23:51<59:33,  1.37s/it]
 29%|██▊       | 1038/3640 [23:52<59:30,  1.37s/it]
 29%|██▊       | 1039/3640 [23:53<59:20,  1.37s/it]
 29%|██▊       | 1040/3640 [23:55<59:28,  1.37s/it]
 29%|██▊       | 1041/3640 [23:56<59:26,  1.37s/it]
 29%|██▊       | 1042/3640 [23:57<59:23,  1.37s/it]
 29%|██▊       | 1043/3640 [23:59<59:24,  1.37s/it]
 29%|██▊       | 1044/3640 [24:00<59:21,  1.37s/it]
 29%|██▊       | 1045/3640 [24:01<59:05,  1.37s/it]
 29%|██▊       | 1046/3640 [24:03<59:12,  1.37s/it]
 29%|██▉       | 1047/3640 [24:04<59:09,  1.37s/it]
 29%|██▉       | 1048/3640 [24:06<59:13,  1.37s/it]
 29%|██▉       | 1049/3640 [24:07<59:13,  1.37s/it]
 29%|██▉       | 1050/3640 [24:08<59:10,  1.37s/it]
                                                   

 29%|██▉       | 1050/3640 [24:08<59:10,  1.37s/it]
 29%|██▉       | 1051/3640 [24:10<59:42,  1.38s/it]
 29%|██▉       | 1052/3640 [24:11<59:34,  1.38s/it]
 29%|██▉       | 1053/3640 [24:13<1:00:14,  1.40s/it]
 29%|██▉       | 1054/3640 [24:14<59:48,  1.39s/it]  
 29%|██▉       | 1055/3640 [24:15<59:31,  1.38s/it]
 29%|██▉       | 1056/3640 [24:17<59:22,  1.38s/it]
 29%|██▉       | 1057/3640 [24:18<1:01:54,  1.44s/it]
 29%|██▉       | 1058/3640 [24:20<1:01:04,  1.42s/it]
 29%|██▉       | 1059/3640 [24:21<1:00:24,  1.40s/it]
 29%|██▉       | 1060/3640 [24:22<59:57,  1.39s/it]  
 29%|██▉       | 1061/3640 [24:24<59:43,  1.39s/it]
 29%|██▉       | 1062/3640 [24:25<59:31,  1.39s/it]
 29%|██▉       | 1063/3640 [24:26<59:22,  1.38s/it]
 29%|██▉       | 1064/3640 [24:28<59:11,  1.38s/it]
 29%|██▉       | 1065/3640 [24:29<58:52,  1.37s/it]
 29%|██▉       | 1066/3640 [24:31<58:58,  1.37s/it]
 29%|██▉       | 1067/3640 [24:32<58:54,  1.37s/it]
 29%|██▉       | 1068/3640 [24:33<58:39,  1.37s/it]
 29%|██▉       | 1069/3640 [24:35<58:38,  1.37s/it]
 29%|██▉       | 1070/3640 [24:36<58:39,  1.37s/it]
 29%|██▉       | 1071/3640 [24:37<58:39,  1.37s/it]
 29%|██▉       | 1072/3640 [24:39<58:41,  1.37s/it]
 29%|██▉       | 1073/3640 [24:40<58:43,  1.37s/it]
 30%|██▉       | 1074/3640 [24:42<58:46,  1.37s/it]
 30%|██▉       | 1075/3640 [24:43<58:39,  1.37s/it]
                                                   

 30%|██▉       | 1075/3640 [24:43<58:39,  1.37s/it]
 30%|██▉       | 1076/3640 [24:44<59:56,  1.40s/it]
 30%|██▉       | 1077/3640 [24:46<59:43,  1.40s/it]
 30%|██▉       | 1078/3640 [24:47<59:25,  1.39s/it]
 30%|██▉       | 1079/3640 [24:49<59:07,  1.39s/it]
 30%|██▉       | 1080/3640 [24:50<58:51,  1.38s/it]
 30%|██▉       | 1081/3640 [24:51<58:39,  1.38s/it]
 30%|██▉       | 1082/3640 [24:53<58:33,  1.37s/it]
 30%|██▉       | 1083/3640 [24:54<58:32,  1.37s/it]
 30%|██▉       | 1084/3640 [24:55<58:32,  1.37s/it]
 30%|██▉       | 1085/3640 [24:57<58:21,  1.37s/it]
 30%|██▉       | 1086/3640 [24:58<58:28,  1.37s/it]
 30%|██▉       | 1087/3640 [24:59<58:22,  1.37s/it]
 30%|██▉       | 1088/3640 [25:01<58:24,  1.37s/it]
 30%|██▉       | 1089/3640 [25:02<58:19,  1.37s/it]
 30%|██▉       | 1090/3640 [25:04<58:22,  1.37s/it]
 30%|██▉       | 1091/3640 [25:05<58:30,  1.38s/it]
 30%|███       | 1092/3640 [25:06<58:14,  1.37s/it]
 30%|███       | 1093/3640 [25:08<58:05,  1.37s/it]
 30%|███       | 1094/3640 [25:09<58:07,  1.37s/it]
 30%|███       | 1095/3640 [25:10<58:10,  1.37s/it]
 30%|███       | 1096/3640 [25:12<58:03,  1.37s/it]
 30%|███       | 1097/3640 [25:13<58:00,  1.37s/it]
 30%|███       | 1098/3640 [25:15<58:00,  1.37s/it]
 30%|███       | 1099/3640 [25:16<58:00,  1.37s/it]
 30%|███       | 1100/3640 [25:17<58:48,  1.39s/it]
                                                   

 30%|███       | 1100/3640 [25:17<58:48,  1.39s/it]
 30%|███       | 1101/3640 [25:19<59:16,  1.40s/it]
 30%|███       | 1102/3640 [25:20<58:51,  1.39s/it]
 30%|███       | 1103/3640 [25:22<58:28,  1.38s/it]
 30%|███       | 1104/3640 [25:23<58:17,  1.38s/it]
 30%|███       | 1105/3640 [25:24<57:53,  1.37s/it]
 30%|███       | 1106/3640 [25:26<57:58,  1.37s/it]
 30%|███       | 1107/3640 [25:27<57:47,  1.37s/it]
 30%|███       | 1108/3640 [25:28<57:47,  1.37s/it]
 30%|███       | 1109/3640 [25:30<57:37,  1.37s/it]
 30%|███       | 1110/3640 [25:31<57:42,  1.37s/it]
 31%|███       | 1111/3640 [25:32<57:39,  1.37s/it]
 31%|███       | 1112/3640 [25:34<57:42,  1.37s/it]
 31%|███       | 1113/3640 [25:35<57:56,  1.38s/it]
 31%|███       | 1114/3640 [25:37<57:28,  1.37s/it]
 31%|███       | 1115/3640 [25:38<57:28,  1.37s/it]
 31%|███       | 1116/3640 [25:39<57:33,  1.37s/it]
 31%|███       | 1117/3640 [25:41<57:41,  1.37s/it]
 31%|███       | 1118/3640 [25:42<57:36,  1.37s/it]
 31%|███       | 1119/3640 [25:44<1:00:18,  1.44s/it]
 31%|███       | 1120/3640 [25:45<59:28,  1.42s/it]  
 31%|███       | 1121/3640 [25:46<58:53,  1.40s/it]
 31%|███       | 1122/3640 [25:48<58:30,  1.39s/it]
 31%|███       | 1123/3640 [25:49<58:13,  1.39s/it]
 31%|███       | 1124/3640 [25:51<58:42,  1.40s/it]
 31%|███       | 1125/3640 [25:52<58:25,  1.39s/it]
                                                   

 31%|███       | 1125/3640 [25:52<58:25,  1.39s/it]
 31%|███       | 1126/3640 [25:53<58:39,  1.40s/it]
 31%|███       | 1127/3640 [25:55<58:11,  1.39s/it]
 31%|███       | 1128/3640 [25:56<57:56,  1.38s/it]
 31%|███       | 1129/3640 [25:57<57:45,  1.38s/it]
 31%|███       | 1130/3640 [25:59<57:32,  1.38s/it]
 31%|███       | 1131/3640 [26:00<57:23,  1.37s/it]
 31%|███       | 1132/3640 [26:02<57:14,  1.37s/it]
 31%|███       | 1133/3640 [26:03<57:19,  1.37s/it]
 31%|███       | 1134/3640 [26:04<58:50,  1.41s/it]
 31%|███       | 1135/3640 [26:06<58:24,  1.40s/it]
 31%|███       | 1136/3640 [26:07<58:01,  1.39s/it]
 31%|███       | 1137/3640 [26:09<57:41,  1.38s/it]
 31%|███▏      | 1138/3640 [26:10<57:38,  1.38s/it]
 31%|███▏      | 1139/3640 [26:11<57:24,  1.38s/it]
 31%|███▏      | 1140/3640 [26:13<57:22,  1.38s/it]
 31%|███▏      | 1141/3640 [26:14<57:19,  1.38s/it]
 31%|███▏      | 1142/3640 [26:15<57:14,  1.37s/it]
 31%|███▏      | 1143/3640 [26:17<57:14,  1.38s/it]
 31%|███▏      | 1144/3640 [26:18<57:14,  1.38s/it]
 31%|███▏      | 1145/3640 [26:20<57:09,  1.37s/it]
 31%|███▏      | 1146/3640 [26:21<57:10,  1.38s/it]
 32%|███▏      | 1147/3640 [26:22<57:03,  1.37s/it]
 32%|███▏      | 1148/3640 [26:24<57:42,  1.39s/it]
 32%|███▏      | 1149/3640 [26:25<57:30,  1.39s/it]
 32%|███▏      | 1150/3640 [26:26<57:14,  1.38s/it]
                                                   

 32%|███▏      | 1150/3640 [26:26<57:14,  1.38s/it]
 32%|███▏      | 1151/3640 [26:28<57:39,  1.39s/it]
 32%|███▏      | 1152/3640 [26:29<57:26,  1.39s/it]
 32%|███▏      | 1153/3640 [26:31<57:16,  1.38s/it]
 32%|███▏      | 1154/3640 [26:32<57:03,  1.38s/it]
 32%|███▏      | 1155/3640 [26:33<56:54,  1.37s/it]
 32%|███▏      | 1156/3640 [26:35<57:01,  1.38s/it]
 32%|███▏      | 1157/3640 [26:36<57:02,  1.38s/it]
 32%|███▏      | 1158/3640 [26:37<56:45,  1.37s/it]
 32%|███▏      | 1159/3640 [26:39<56:44,  1.37s/it]
 32%|███▏      | 1160/3640 [26:40<56:31,  1.37s/it]
 32%|███▏      | 1161/3640 [26:42<56:34,  1.37s/it]
 32%|███▏      | 1162/3640 [26:43<56:35,  1.37s/it]
 32%|███▏      | 1163/3640 [26:44<56:31,  1.37s/it]
 32%|███▏      | 1164/3640 [26:46<56:39,  1.37s/it]
 32%|███▏      | 1165/3640 [26:47<56:37,  1.37s/it]
 32%|███▏      | 1166/3640 [26:48<56:32,  1.37s/it]
 32%|███▏      | 1167/3640 [26:50<56:30,  1.37s/it]
 32%|███▏      | 1168/3640 [26:51<56:29,  1.37s/it]
 32%|███▏      | 1169/3640 [26:53<56:28,  1.37s/it]
 32%|███▏      | 1170/3640 [26:54<56:23,  1.37s/it]
 32%|███▏      | 1171/3640 [26:55<56:56,  1.38s/it]
 32%|███▏      | 1172/3640 [26:57<57:03,  1.39s/it]
 32%|███▏      | 1173/3640 [26:58<56:50,  1.38s/it]
 32%|███▏      | 1174/3640 [26:59<56:33,  1.38s/it]
 32%|███▏      | 1175/3640 [27:01<56:27,  1.37s/it]
                                                   

 32%|███▏      | 1175/3640 [27:01<56:27,  1.37s/it]
 32%|███▏      | 1176/3640 [27:02<56:36,  1.38s/it]
 32%|███▏      | 1177/3640 [27:04<56:26,  1.38s/it]
 32%|███▏      | 1178/3640 [27:05<56:12,  1.37s/it]
 32%|███▏      | 1179/3640 [27:06<56:16,  1.37s/it]
 32%|███▏      | 1180/3640 [27:08<56:12,  1.37s/it]
 32%|███▏      | 1181/3640 [27:09<56:11,  1.37s/it]
 32%|███▏      | 1182/3640 [27:10<56:16,  1.37s/it]
 32%|███▎      | 1183/3640 [27:12<56:18,  1.38s/it]
 33%|███▎      | 1184/3640 [27:13<56:11,  1.37s/it]
 33%|███▎      | 1185/3640 [27:15<56:14,  1.37s/it]
 33%|███▎      | 1186/3640 [27:16<56:10,  1.37s/it]
 33%|███▎      | 1187/3640 [27:17<56:03,  1.37s/it]
 33%|███▎      | 1188/3640 [27:19<56:01,  1.37s/it]
 33%|███▎      | 1189/3640 [27:20<56:04,  1.37s/it]
 33%|███▎      | 1190/3640 [27:21<56:06,  1.37s/it]
 33%|███▎      | 1191/3640 [27:23<56:04,  1.37s/it]
 33%|███▎      | 1192/3640 [27:24<55:55,  1.37s/it]
 33%|███▎      | 1193/3640 [27:26<58:20,  1.43s/it]
 33%|███▎      | 1194/3640 [27:27<57:42,  1.42s/it]
 33%|███▎      | 1195/3640 [27:29<57:54,  1.42s/it]
 33%|███▎      | 1196/3640 [27:30<57:21,  1.41s/it]
 33%|███▎      | 1197/3640 [27:31<56:48,  1.40s/it]
 33%|███▎      | 1198/3640 [27:33<56:24,  1.39s/it]
 33%|███▎      | 1199/3640 [27:34<56:14,  1.38s/it]
 33%|███▎      | 1200/3640 [27:35<56:08,  1.38s/it]
                                                   

 33%|███▎      | 1200/3640 [27:35<56:08,  1.38s/it]
 33%|███▎      | 1201/3640 [27:37<56:33,  1.39s/it]
 33%|███▎      | 1202/3640 [27:38<56:20,  1.39s/it]
 33%|███▎      | 1203/3640 [27:40<56:08,  1.38s/it]
 33%|███▎      | 1204/3640 [27:41<56:04,  1.38s/it]
 33%|███▎      | 1205/3640 [27:42<55:55,  1.38s/it]
 33%|███▎      | 1206/3640 [27:44<55:49,  1.38s/it]
 33%|███▎      | 1207/3640 [27:45<55:46,  1.38s/it]
 33%|███▎      | 1208/3640 [27:46<55:43,  1.37s/it]
 33%|███▎      | 1209/3640 [27:48<55:40,  1.37s/it]
 33%|███▎      | 1210/3640 [27:49<55:39,  1.37s/it]
 33%|███▎      | 1211/3640 [27:51<55:25,  1.37s/it]
 33%|███▎      | 1212/3640 [27:52<55:24,  1.37s/it]
 33%|███▎      | 1213/3640 [27:53<55:22,  1.37s/it]
 33%|███▎      | 1214/3640 [27:55<55:25,  1.37s/it]
 33%|███▎      | 1215/3640 [27:56<55:24,  1.37s/it]
 33%|███▎      | 1216/3640 [27:57<55:23,  1.37s/it]
 33%|███▎      | 1217/3640 [27:59<55:21,  1.37s/it]
 33%|███▎      | 1218/3640 [28:00<55:24,  1.37s/it]
 33%|███▎      | 1219/3640 [28:02<56:12,  1.39s/it]
 34%|███▎      | 1220/3640 [28:03<55:54,  1.39s/it]
 34%|███▎      | 1221/3640 [28:04<55:43,  1.38s/it]
 34%|███▎      | 1222/3640 [28:06<55:44,  1.38s/it]
 34%|███▎      | 1223/3640 [28:07<55:36,  1.38s/it]
 34%|███▎      | 1224/3640 [28:08<55:19,  1.37s/it]
 34%|███▎      | 1225/3640 [28:10<55:20,  1.37s/it]
                                                   

 34%|███▎      | 1225/3640 [28:10<55:20,  1.37s/it]
 34%|███▎      | 1226/3640 [28:11<55:50,  1.39s/it]
 34%|███▎      | 1227/3640 [28:13<55:33,  1.38s/it]
 34%|███▎      | 1228/3640 [28:14<55:25,  1.38s/it]
 34%|███▍      | 1229/3640 [28:15<55:25,  1.38s/it]
 34%|███▍      | 1230/3640 [28:17<55:18,  1.38s/it]
 34%|███▍      | 1231/3640 [28:18<55:17,  1.38s/it]
 34%|███▍      | 1232/3640 [28:19<55:12,  1.38s/it]
 34%|███▍      | 1233/3640 [28:21<55:12,  1.38s/it]
 34%|███▍      | 1234/3640 [28:22<55:07,  1.37s/it]
 34%|███▍      | 1235/3640 [28:24<55:05,  1.37s/it]
 34%|███▍      | 1236/3640 [28:25<55:01,  1.37s/it]
 34%|███▍      | 1237/3640 [28:26<55:00,  1.37s/it]
 34%|███▍      | 1238/3640 [28:28<54:59,  1.37s/it]
 34%|███▍      | 1239/3640 [28:29<54:57,  1.37s/it]
 34%|███▍      | 1240/3640 [28:30<54:55,  1.37s/it]
 34%|███▍      | 1241/3640 [28:32<54:50,  1.37s/it]
 34%|███▍      | 1242/3640 [28:33<54:40,  1.37s/it]
 34%|███▍      | 1243/3640 [28:35<55:25,  1.39s/it]
 34%|███▍      | 1244/3640 [28:36<55:05,  1.38s/it]
 34%|███▍      | 1245/3640 [28:37<54:58,  1.38s/it]
 34%|███▍      | 1246/3640 [28:39<54:50,  1.37s/it]
 34%|███▍      | 1247/3640 [28:40<54:46,  1.37s/it]
 34%|███▍      | 1248/3640 [28:41<54:43,  1.37s/it]
 34%|███▍      | 1249/3640 [28:43<54:37,  1.37s/it]
 34%|███▍      | 1250/3640 [28:44<54:39,  1.37s/it]
                                                   

 34%|███▍      | 1250/3640 [28:44<54:39,  1.37s/it]
 34%|███▍      | 1251/3640 [28:46<55:17,  1.39s/it]
 34%|███▍      | 1252/3640 [28:47<55:03,  1.38s/it]
 34%|███▍      | 1253/3640 [28:48<54:55,  1.38s/it]
 34%|███▍      | 1254/3640 [28:50<54:49,  1.38s/it]
 34%|███▍      | 1255/3640 [28:51<54:43,  1.38s/it]
 35%|███▍      | 1256/3640 [28:52<54:30,  1.37s/it]
 35%|███▍      | 1257/3640 [28:54<54:28,  1.37s/it]
 35%|███▍      | 1258/3640 [28:55<54:29,  1.37s/it]
 35%|███▍      | 1259/3640 [28:57<54:27,  1.37s/it]
 35%|███▍      | 1260/3640 [28:58<54:21,  1.37s/it]
 35%|███▍      | 1261/3640 [28:59<54:17,  1.37s/it]
 35%|███▍      | 1262/3640 [29:01<54:22,  1.37s/it]
 35%|███▍      | 1263/3640 [29:02<54:23,  1.37s/it]
 35%|███▍      | 1264/3640 [29:03<54:11,  1.37s/it]
 35%|███▍      | 1265/3640 [29:05<54:17,  1.37s/it]
 35%|███▍      | 1266/3640 [29:06<54:30,  1.38s/it]
 35%|███▍      | 1267/3640 [29:08<54:51,  1.39s/it]
 35%|███▍      | 1268/3640 [29:09<54:42,  1.38s/it]
 35%|███▍      | 1269/3640 [29:10<54:32,  1.38s/it]
 35%|███▍      | 1270/3640 [29:12<54:20,  1.38s/it]
 35%|███▍      | 1271/3640 [29:13<54:15,  1.37s/it]
 35%|███▍      | 1272/3640 [29:14<53:52,  1.37s/it]
 35%|███▍      | 1273/3640 [29:16<54:02,  1.37s/it]
 35%|███▌      | 1274/3640 [29:17<54:03,  1.37s/it]
 35%|███▌      | 1275/3640 [29:19<54:04,  1.37s/it]
                                                   

 35%|███▌      | 1275/3640 [29:19<54:04,  1.37s/it]
 35%|███▌      | 1276/3640 [29:20<54:37,  1.39s/it]
 35%|███▌      | 1277/3640 [29:21<54:28,  1.38s/it]
 35%|███▌      | 1278/3640 [29:23<54:19,  1.38s/it]
 35%|███▌      | 1279/3640 [29:24<54:15,  1.38s/it]
 35%|███▌      | 1280/3640 [29:26<54:12,  1.38s/it]
 35%|███▌      | 1281/3640 [29:27<54:05,  1.38s/it]
 35%|███▌      | 1282/3640 [29:28<54:02,  1.37s/it]
 35%|███▌      | 1283/3640 [29:30<54:00,  1.38s/it]
 35%|███▌      | 1284/3640 [29:31<54:01,  1.38s/it]
 35%|███▌      | 1285/3640 [29:32<53:57,  1.37s/it]
 35%|███▌      | 1286/3640 [29:34<53:51,  1.37s/it]
 35%|███▌      | 1287/3640 [29:35<53:46,  1.37s/it]
 35%|███▌      | 1288/3640 [29:36<53:44,  1.37s/it]
 35%|███▌      | 1289/3640 [29:38<53:45,  1.37s/it]
 35%|███▌      | 1290/3640 [29:39<54:04,  1.38s/it]
 35%|███▌      | 1291/3640 [29:41<54:10,  1.38s/it]
 35%|███▌      | 1292/3640 [29:42<54:01,  1.38s/it]
 36%|███▌      | 1293/3640 [29:43<53:53,  1.38s/it]
 36%|███▌      | 1294/3640 [29:45<53:50,  1.38s/it]
 36%|███▌      | 1295/3640 [29:46<53:43,  1.37s/it]
 36%|███▌      | 1296/3640 [29:48<53:39,  1.37s/it]
 36%|███▌      | 1297/3640 [29:49<53:33,  1.37s/it]
 36%|███▌      | 1298/3640 [29:50<53:37,  1.37s/it]
 36%|███▌      | 1299/3640 [29:52<53:31,  1.37s/it]
 36%|███▌      | 1300/3640 [29:53<53:28,  1.37s/it]
                                                   

 36%|███▌      | 1300/3640 [29:53<53:28,  1.37s/it]
 36%|███▌      | 1301/3640 [29:54<54:01,  1.39s/it]
 36%|███▌      | 1302/3640 [29:56<53:54,  1.38s/it]
 36%|███▌      | 1303/3640 [29:57<53:39,  1.38s/it]
 36%|███▌      | 1304/3640 [29:59<53:33,  1.38s/it]
 36%|███▌      | 1305/3640 [30:00<53:28,  1.37s/it]
 36%|███▌      | 1306/3640 [30:01<53:26,  1.37s/it]
 36%|███▌      | 1307/3640 [30:03<53:23,  1.37s/it]
 36%|███▌      | 1308/3640 [30:04<53:19,  1.37s/it]
 36%|███▌      | 1309/3640 [30:05<53:19,  1.37s/it]
 36%|███▌      | 1310/3640 [30:07<53:21,  1.37s/it]
 36%|███▌      | 1311/3640 [30:08<53:17,  1.37s/it]
 36%|███▌      | 1312/3640 [30:09<53:12,  1.37s/it]
 36%|███▌      | 1313/3640 [30:11<53:16,  1.37s/it]
 36%|███▌      | 1314/3640 [30:12<53:48,  1.39s/it]
 36%|███▌      | 1315/3640 [30:14<53:26,  1.38s/it]
 36%|███▌      | 1316/3640 [30:15<53:15,  1.38s/it]
 36%|███▌      | 1317/3640 [30:16<53:10,  1.37s/it]
 36%|███▌      | 1318/3640 [30:18<53:03,  1.37s/it]
 36%|███▌      | 1319/3640 [30:19<53:00,  1.37s/it]
 36%|███▋      | 1320/3640 [30:20<53:03,  1.37s/it]
 36%|███▋      | 1321/3640 [30:22<52:59,  1.37s/it]
 36%|███▋      | 1322/3640 [30:23<52:55,  1.37s/it]
 36%|███▋      | 1323/3640 [30:25<52:41,  1.36s/it]
 36%|███▋      | 1324/3640 [30:26<52:46,  1.37s/it]
 36%|███▋      | 1325/3640 [30:27<52:43,  1.37s/it]
                                                   

 36%|███▋      | 1325/3640 [30:27<52:43,  1.37s/it]
 36%|███▋      | 1326/3640 [30:29<55:19,  1.43s/it]
 36%|███▋      | 1327/3640 [30:30<55:58,  1.45s/it]
 36%|███▋      | 1328/3640 [30:32<55:02,  1.43s/it]
 37%|███▋      | 1329/3640 [30:33<54:23,  1.41s/it]
 37%|███▋      | 1330/3640 [30:35<53:34,  1.39s/it]
 37%|███▋      | 1331/3640 [30:36<53:29,  1.39s/it]
 37%|███▋      | 1332/3640 [30:37<53:14,  1.38s/it]
 37%|███▋      | 1333/3640 [30:39<53:03,  1.38s/it]
 37%|███▋      | 1334/3640 [30:40<53:00,  1.38s/it]
 37%|███▋      | 1335/3640 [30:41<52:54,  1.38s/it]
 37%|███▋      | 1336/3640 [30:43<52:50,  1.38s/it]
 37%|███▋      | 1337/3640 [30:44<52:42,  1.37s/it]
 37%|███▋      | 1338/3640 [30:46<53:24,  1.39s/it]
 37%|███▋      | 1339/3640 [30:47<53:05,  1.38s/it]
 37%|███▋      | 1340/3640 [30:48<52:58,  1.38s/it]
 37%|███▋      | 1341/3640 [30:50<52:50,  1.38s/it]
 37%|███▋      | 1342/3640 [30:51<52:39,  1.38s/it]
 37%|███▋      | 1343/3640 [30:52<52:27,  1.37s/it]
 37%|███▋      | 1344/3640 [30:54<52:27,  1.37s/it]
 37%|███▋      | 1345/3640 [30:55<52:30,  1.37s/it]
 37%|███▋      | 1346/3640 [30:57<52:28,  1.37s/it]
 37%|███▋      | 1347/3640 [30:58<52:29,  1.37s/it]
 37%|███▋      | 1348/3640 [30:59<52:28,  1.37s/it]
 37%|███▋      | 1349/3640 [31:01<52:28,  1.37s/it]
 37%|███▋      | 1350/3640 [31:02<52:23,  1.37s/it]
                                                   

 37%|███▋      | 1350/3640 [31:02<52:23,  1.37s/it]
 37%|███▋      | 1351/3640 [31:03<52:40,  1.38s/it]
 37%|███▋      | 1352/3640 [31:05<52:37,  1.38s/it]
 37%|███▋      | 1353/3640 [31:06<52:30,  1.38s/it]
 37%|███▋      | 1354/3640 [31:08<52:28,  1.38s/it]
 37%|███▋      | 1355/3640 [31:09<52:26,  1.38s/it]
 37%|███▋      | 1356/3640 [31:10<52:20,  1.38s/it]
 37%|███▋      | 1357/3640 [31:12<52:19,  1.38s/it]
 37%|███▋      | 1358/3640 [31:13<52:12,  1.37s/it]
 37%|███▋      | 1359/3640 [31:14<52:06,  1.37s/it]
 37%|███▋      | 1360/3640 [31:16<52:12,  1.37s/it]
 37%|███▋      | 1361/3640 [31:17<52:07,  1.37s/it]
 37%|███▋      | 1362/3640 [31:19<52:42,  1.39s/it]
 37%|███▋      | 1363/3640 [31:20<52:36,  1.39s/it]
 37%|███▋      | 1364/3640 [31:21<52:17,  1.38s/it]
 38%|███▊      | 1365/3640 [31:23<52:09,  1.38s/it]
 38%|███▊      | 1366/3640 [31:24<52:10,  1.38s/it]
 38%|███▊      | 1367/3640 [31:25<51:58,  1.37s/it]
 38%|███▊      | 1368/3640 [31:27<51:57,  1.37s/it]
 38%|███▊      | 1369/3640 [31:28<51:56,  1.37s/it]
 38%|███▊      | 1370/3640 [31:30<51:50,  1.37s/it]
 38%|███▊      | 1371/3640 [31:31<51:51,  1.37s/it]
 38%|███▊      | 1372/3640 [31:32<51:53,  1.37s/it]
 38%|███▊      | 1373/3640 [31:34<51:51,  1.37s/it]
 38%|███▊      | 1374/3640 [31:35<51:59,  1.38s/it]
 38%|███▊      | 1375/3640 [31:36<51:53,  1.37s/it]
                                                   

 38%|███▊      | 1375/3640 [31:36<51:53,  1.37s/it]
 38%|███▊      | 1376/3640 [31:38<52:20,  1.39s/it]
 38%|███▊      | 1377/3640 [31:39<52:08,  1.38s/it]
 38%|███▊      | 1378/3640 [31:41<52:04,  1.38s/it]
 38%|███▊      | 1379/3640 [31:42<51:52,  1.38s/it]
 38%|███▊      | 1380/3640 [31:43<51:41,  1.37s/it]
 38%|███▊      | 1381/3640 [31:45<51:44,  1.37s/it]
 38%|███▊      | 1382/3640 [31:46<51:42,  1.37s/it]
 38%|███▊      | 1383/3640 [31:47<51:39,  1.37s/it]
 38%|███▊      | 1384/3640 [31:49<51:35,  1.37s/it]
 38%|███▊      | 1385/3640 [31:50<51:56,  1.38s/it]
 38%|███▊      | 1386/3640 [31:52<52:06,  1.39s/it]
 38%|███▊      | 1387/3640 [31:53<51:58,  1.38s/it]
 38%|███▊      | 1388/3640 [31:54<51:46,  1.38s/it]
 38%|███▊      | 1389/3640 [31:56<51:44,  1.38s/it]
 38%|███▊      | 1390/3640 [31:57<51:29,  1.37s/it]
 38%|███▊      | 1391/3640 [31:58<51:31,  1.37s/it]
 38%|███▊      | 1392/3640 [32:00<52:03,  1.39s/it]
 38%|███▊      | 1393/3640 [32:01<51:46,  1.38s/it]
 38%|███▊      | 1394/3640 [32:03<51:38,  1.38s/it]
 38%|███▊      | 1395/3640 [32:04<51:31,  1.38s/it]
 38%|███▊      | 1396/3640 [32:05<51:20,  1.37s/it]
 38%|███▊      | 1397/3640 [32:07<51:17,  1.37s/it]
 38%|███▊      | 1398/3640 [32:08<51:14,  1.37s/it]
 38%|███▊      | 1399/3640 [32:09<51:13,  1.37s/it]
 38%|███▊      | 1400/3640 [32:11<51:10,  1.37s/it]
                                                   

 38%|███▊      | 1400/3640 [32:11<51:10,  1.37s/it]
 38%|███▊      | 1401/3640 [32:12<53:30,  1.43s/it]
 39%|███▊      | 1402/3640 [32:14<52:47,  1.42s/it]
 39%|███▊      | 1403/3640 [32:15<52:17,  1.40s/it]
 39%|███▊      | 1404/3640 [32:17<51:54,  1.39s/it]
 39%|███▊      | 1405/3640 [32:18<51:38,  1.39s/it]
 39%|███▊      | 1406/3640 [32:19<51:27,  1.38s/it]
 39%|███▊      | 1407/3640 [32:21<51:11,  1.38s/it]
 39%|███▊      | 1408/3640 [32:22<51:06,  1.37s/it]
 39%|███▊      | 1409/3640 [32:23<51:51,  1.39s/it]
 39%|███▊      | 1410/3640 [32:25<51:40,  1.39s/it]
 39%|███▉      | 1411/3640 [32:26<51:16,  1.38s/it]
 39%|███▉      | 1412/3640 [32:28<51:03,  1.38s/it]
 39%|███▉      | 1413/3640 [32:29<51:02,  1.38s/it]
 39%|███▉      | 1414/3640 [32:30<51:05,  1.38s/it]
 39%|███▉      | 1415/3640 [32:32<50:59,  1.38s/it]
 39%|███▉      | 1416/3640 [32:33<50:57,  1.37s/it]
 39%|███▉      | 1417/3640 [32:34<50:50,  1.37s/it]
 39%|███▉      | 1418/3640 [32:36<50:57,  1.38s/it]
 39%|███▉      | 1419/3640 [32:37<50:55,  1.38s/it]
 39%|███▉      | 1420/3640 [32:39<50:46,  1.37s/it]
 39%|███▉      | 1421/3640 [32:40<50:43,  1.37s/it]
 39%|███▉      | 1422/3640 [32:41<50:37,  1.37s/it]
 39%|███▉      | 1423/3640 [32:43<50:34,  1.37s/it]
 39%|███▉      | 1424/3640 [32:44<50:11,  1.36s/it]
 39%|███▉      | 1425/3640 [32:45<50:23,  1.37s/it]
                                                   

 39%|███▉      | 1425/3640 [32:45<50:23,  1.37s/it]
 39%|███▉      | 1426/3640 [32:47<50:47,  1.38s/it]
 39%|███▉      | 1427/3640 [32:48<50:45,  1.38s/it]
 39%|███▉      | 1428/3640 [32:50<50:42,  1.38s/it]
 39%|███▉      | 1429/3640 [32:51<50:36,  1.37s/it]
 39%|███▉      | 1430/3640 [32:52<50:33,  1.37s/it]
 39%|███▉      | 1431/3640 [32:54<50:13,  1.36s/it]
 39%|███▉      | 1432/3640 [32:55<52:33,  1.43s/it]
 39%|███▉      | 1433/3640 [32:57<52:36,  1.43s/it]
 39%|███▉      | 1434/3640 [32:58<51:55,  1.41s/it]
 39%|███▉      | 1435/3640 [32:59<51:31,  1.40s/it]
 39%|███▉      | 1436/3640 [33:01<51:14,  1.39s/it]
 39%|███▉      | 1437/3640 [33:02<50:57,  1.39s/it]
 40%|███▉      | 1438/3640 [33:03<50:43,  1.38s/it]
 40%|███▉      | 1439/3640 [33:05<50:40,  1.38s/it]
 40%|███▉      | 1440/3640 [33:06<50:35,  1.38s/it]
 40%|███▉      | 1441/3640 [33:08<50:20,  1.37s/it]
 40%|███▉      | 1442/3640 [33:09<50:11,  1.37s/it]
 40%|███▉      | 1443/3640 [33:10<50:15,  1.37s/it]
 40%|███▉      | 1444/3640 [33:12<50:16,  1.37s/it]
 40%|███▉      | 1445/3640 [33:13<50:08,  1.37s/it]
 40%|███▉      | 1446/3640 [33:14<50:05,  1.37s/it]
 40%|███▉      | 1447/3640 [33:16<50:07,  1.37s/it]
 40%|███▉      | 1448/3640 [33:17<50:07,  1.37s/it]
 40%|███▉      | 1449/3640 [33:19<50:02,  1.37s/it]
 40%|███▉      | 1450/3640 [33:20<50:00,  1.37s/it]
                                                   

 40%|███▉      | 1450/3640 [33:20<50:00,  1.37s/it]
 40%|███▉      | 1451/3640 [33:21<50:37,  1.39s/it]
 40%|███▉      | 1452/3640 [33:23<50:24,  1.38s/it]
 40%|███▉      | 1453/3640 [33:24<50:13,  1.38s/it]
 40%|███▉      | 1454/3640 [33:25<50:08,  1.38s/it]
 40%|███▉      | 1455/3640 [33:27<50:05,  1.38s/it]
 40%|████      | 1456/3640 [33:28<49:59,  1.37s/it]
 40%|████      | 1457/3640 [33:30<50:29,  1.39s/it]
 40%|████      | 1458/3640 [33:31<50:12,  1.38s/it]
 40%|████      | 1459/3640 [33:32<50:01,  1.38s/it]
 40%|████      | 1460/3640 [33:34<49:53,  1.37s/it]
 40%|████      | 1461/3640 [33:35<49:47,  1.37s/it]
 40%|████      | 1462/3640 [33:36<49:46,  1.37s/it]
 40%|████      | 1463/3640 [33:38<49:48,  1.37s/it]
 40%|████      | 1464/3640 [33:39<49:47,  1.37s/it]
 40%|████      | 1465/3640 [33:41<49:42,  1.37s/it]
 40%|████      | 1466/3640 [33:42<49:41,  1.37s/it]
 40%|████      | 1467/3640 [33:43<49:39,  1.37s/it]
 40%|████      | 1468/3640 [33:45<49:32,  1.37s/it]
 40%|████      | 1469/3640 [33:46<49:31,  1.37s/it]
 40%|████      | 1470/3640 [33:47<49:19,  1.36s/it]
 40%|████      | 1471/3640 [33:49<49:22,  1.37s/it]
 40%|████      | 1472/3640 [33:50<49:11,  1.36s/it]
 40%|████      | 1473/3640 [33:52<49:21,  1.37s/it]
 40%|████      | 1474/3640 [33:53<49:18,  1.37s/it]
 41%|████      | 1475/3640 [33:54<49:16,  1.37s/it]
                                                   

 41%|████      | 1475/3640 [33:54<49:16,  1.37s/it]
 41%|████      | 1476/3640 [33:56<49:50,  1.38s/it]
 41%|████      | 1477/3640 [33:57<49:34,  1.38s/it]
 41%|████      | 1478/3640 [33:58<49:32,  1.37s/it]
 41%|████      | 1479/3640 [34:00<49:27,  1.37s/it]
 41%|████      | 1480/3640 [34:01<49:29,  1.37s/it]
 41%|████      | 1481/3640 [34:03<49:58,  1.39s/it]
 41%|████      | 1482/3640 [34:04<49:43,  1.38s/it]
 41%|████      | 1483/3640 [34:05<49:33,  1.38s/it]
 41%|████      | 1484/3640 [34:07<49:24,  1.38s/it]
 41%|████      | 1485/3640 [34:08<49:20,  1.37s/it]
 41%|████      | 1486/3640 [34:09<49:17,  1.37s/it]
 41%|████      | 1487/3640 [34:11<49:03,  1.37s/it]
 41%|████      | 1488/3640 [34:12<49:05,  1.37s/it]
 41%|████      | 1489/3640 [34:13<49:08,  1.37s/it]
 41%|████      | 1490/3640 [34:15<49:08,  1.37s/it]
 41%|████      | 1491/3640 [34:16<49:02,  1.37s/it]
 41%|████      | 1492/3640 [34:18<49:01,  1.37s/it]
 41%|████      | 1493/3640 [34:19<49:03,  1.37s/it]
 41%|████      | 1494/3640 [34:20<48:59,  1.37s/it]
 41%|████      | 1495/3640 [34:22<48:58,  1.37s/it]
 41%|████      | 1496/3640 [34:23<48:57,  1.37s/it]
 41%|████      | 1497/3640 [34:24<48:54,  1.37s/it]
 41%|████      | 1498/3640 [34:26<48:54,  1.37s/it]
 41%|████      | 1499/3640 [34:27<48:54,  1.37s/it]
 41%|████      | 1500/3640 [34:29<49:29,  1.39s/it]
                                                   

 41%|████      | 1500/3640 [34:29<49:29,  1.39s/it]
 41%|████      | 1501/3640 [34:30<49:47,  1.40s/it]
 41%|████▏     | 1502/3640 [34:31<49:29,  1.39s/it]
 41%|████▏     | 1503/3640 [34:33<49:11,  1.38s/it]
 41%|████▏     | 1504/3640 [34:34<49:35,  1.39s/it]
 41%|████▏     | 1505/3640 [34:36<49:28,  1.39s/it]
 41%|████▏     | 1506/3640 [34:37<51:04,  1.44s/it]
 41%|████▏     | 1507/3640 [34:38<50:18,  1.41s/it]
 41%|████▏     | 1508/3640 [34:40<49:47,  1.40s/it]
 41%|████▏     | 1509/3640 [34:41<49:21,  1.39s/it]
 41%|████▏     | 1510/3640 [34:43<49:08,  1.38s/it]
 42%|████▏     | 1511/3640 [34:44<48:52,  1.38s/it]
 42%|████▏     | 1512/3640 [34:45<48:47,  1.38s/it]
 42%|████▏     | 1513/3640 [34:47<50:56,  1.44s/it]
 42%|████▏     | 1514/3640 [34:48<50:14,  1.42s/it]
 42%|████▏     | 1515/3640 [34:50<49:42,  1.40s/it]
 42%|████▏     | 1516/3640 [34:51<49:22,  1.39s/it]
 42%|████▏     | 1517/3640 [34:52<49:06,  1.39s/it]
 42%|████▏     | 1518/3640 [34:54<48:55,  1.38s/it]
 42%|████▏     | 1519/3640 [34:55<48:46,  1.38s/it]
 42%|████▏     | 1520/3640 [34:57<48:43,  1.38s/it]
 42%|████▏     | 1521/3640 [34:58<48:32,  1.37s/it]
 42%|████▏     | 1522/3640 [34:59<48:30,  1.37s/it]
 42%|████▏     | 1523/3640 [35:01<48:19,  1.37s/it]
 42%|████▏     | 1524/3640 [35:02<48:18,  1.37s/it]
 42%|████▏     | 1525/3640 [35:03<48:10,  1.37s/it]
                                                   

 42%|████▏     | 1525/3640 [35:03<48:10,  1.37s/it]
 42%|████▏     | 1526/3640 [35:05<48:47,  1.38s/it]
 42%|████▏     | 1527/3640 [35:06<48:36,  1.38s/it]
 42%|████▏     | 1528/3640 [35:08<48:51,  1.39s/it]
 42%|████▏     | 1529/3640 [35:09<48:33,  1.38s/it]
 42%|████▏     | 1530/3640 [35:10<48:27,  1.38s/it]
 42%|████▏     | 1531/3640 [35:12<48:19,  1.37s/it]
 42%|████▏     | 1532/3640 [35:13<48:14,  1.37s/it]
 42%|████▏     | 1533/3640 [35:14<48:10,  1.37s/it]
 42%|████▏     | 1534/3640 [35:16<48:09,  1.37s/it]
 42%|████▏     | 1535/3640 [35:17<48:08,  1.37s/it]
 42%|████▏     | 1536/3640 [35:18<48:01,  1.37s/it]
 42%|████▏     | 1537/3640 [35:20<48:01,  1.37s/it]
 42%|████▏     | 1538/3640 [35:21<47:59,  1.37s/it]
 42%|████▏     | 1539/3640 [35:23<47:56,  1.37s/it]
 42%|████▏     | 1540/3640 [35:24<47:49,  1.37s/it]
 42%|████▏     | 1541/3640 [35:25<47:50,  1.37s/it]
 42%|████▏     | 1542/3640 [35:27<47:42,  1.36s/it]
 42%|████▏     | 1543/3640 [35:28<47:48,  1.37s/it]
 42%|████▏     | 1544/3640 [35:29<47:51,  1.37s/it]
 42%|████▏     | 1545/3640 [35:31<47:51,  1.37s/it]
 42%|████▏     | 1546/3640 [35:32<47:51,  1.37s/it]
 42%|████▎     | 1547/3640 [35:34<47:52,  1.37s/it]
 43%|████▎     | 1548/3640 [35:35<47:52,  1.37s/it]
 43%|████▎     | 1549/3640 [35:36<47:52,  1.37s/it]
 43%|████▎     | 1550/3640 [35:38<47:43,  1.37s/it]
                                                   

 43%|████▎     | 1550/3640 [35:38<47:43,  1.37s/it]
 43%|████▎     | 1551/3640 [35:39<48:17,  1.39s/it]
 43%|████▎     | 1552/3640 [35:41<48:42,  1.40s/it]
 43%|████▎     | 1553/3640 [35:42<48:26,  1.39s/it]
 43%|████▎     | 1554/3640 [35:43<48:12,  1.39s/it]
 43%|████▎     | 1555/3640 [35:45<48:00,  1.38s/it]
 43%|████▎     | 1556/3640 [35:46<47:56,  1.38s/it]
 43%|████▎     | 1557/3640 [35:47<47:47,  1.38s/it]
 43%|████▎     | 1558/3640 [35:49<47:37,  1.37s/it]
 43%|████▎     | 1559/3640 [35:50<47:35,  1.37s/it]
 43%|████▎     | 1560/3640 [35:52<47:36,  1.37s/it]
 43%|████▎     | 1561/3640 [35:53<47:37,  1.37s/it]
 43%|████▎     | 1562/3640 [35:54<47:27,  1.37s/it]
 43%|████▎     | 1563/3640 [35:56<47:29,  1.37s/it]
 43%|████▎     | 1564/3640 [35:57<47:23,  1.37s/it]
 43%|████▎     | 1565/3640 [35:58<47:20,  1.37s/it]
 43%|████▎     | 1566/3640 [36:00<47:21,  1.37s/it]
 43%|████▎     | 1567/3640 [36:01<47:20,  1.37s/it]
 43%|████▎     | 1568/3640 [36:02<47:19,  1.37s/it]
 43%|████▎     | 1569/3640 [36:04<47:16,  1.37s/it]
 43%|████▎     | 1570/3640 [36:05<47:17,  1.37s/it]
 43%|████▎     | 1571/3640 [36:07<47:19,  1.37s/it]
 43%|████▎     | 1572/3640 [36:08<47:12,  1.37s/it]
 43%|████▎     | 1573/3640 [36:09<47:06,  1.37s/it]
 43%|████▎     | 1574/3640 [36:11<47:09,  1.37s/it]
 43%|████▎     | 1575/3640 [36:12<47:08,  1.37s/it]
                                                   

 43%|████▎     | 1575/3640 [36:12<47:08,  1.37s/it]
 43%|████▎     | 1576/3640 [36:14<47:57,  1.39s/it]
 43%|████▎     | 1577/3640 [36:15<47:46,  1.39s/it]
 43%|████▎     | 1578/3640 [36:16<47:30,  1.38s/it]
 43%|████▎     | 1579/3640 [36:18<47:22,  1.38s/it]
 43%|████▎     | 1580/3640 [36:19<47:19,  1.38s/it]
 43%|████▎     | 1581/3640 [36:20<47:13,  1.38s/it]
 43%|████▎     | 1582/3640 [36:22<46:49,  1.36s/it]
 43%|████▎     | 1583/3640 [36:23<46:50,  1.37s/it]
 44%|████▎     | 1584/3640 [36:24<46:48,  1.37s/it]
 44%|████▎     | 1585/3640 [36:26<46:48,  1.37s/it]
 44%|████▎     | 1586/3640 [36:27<46:54,  1.37s/it]
 44%|████▎     | 1587/3640 [36:29<46:53,  1.37s/it]
 44%|████▎     | 1588/3640 [36:30<46:56,  1.37s/it]
 44%|████▎     | 1589/3640 [36:31<46:49,  1.37s/it]
 44%|████▎     | 1590/3640 [36:33<48:59,  1.43s/it]
 44%|████▎     | 1591/3640 [36:34<48:13,  1.41s/it]
 44%|████▎     | 1592/3640 [36:36<47:52,  1.40s/it]
 44%|████▍     | 1593/3640 [36:37<47:35,  1.39s/it]
 44%|████▍     | 1594/3640 [36:38<47:14,  1.39s/it]
 44%|████▍     | 1595/3640 [36:40<47:06,  1.38s/it]
 44%|████▍     | 1596/3640 [36:41<46:58,  1.38s/it]
 44%|████▍     | 1597/3640 [36:42<46:51,  1.38s/it]
 44%|████▍     | 1598/3640 [36:44<46:45,  1.37s/it]
 44%|████▍     | 1599/3640 [36:45<47:11,  1.39s/it]
 44%|████▍     | 1600/3640 [36:47<47:01,  1.38s/it]
                                                   

 44%|████▍     | 1600/3640 [36:47<47:01,  1.38s/it]
 44%|████▍     | 1601/3640 [36:48<47:14,  1.39s/it]
 44%|████▍     | 1602/3640 [36:49<47:02,  1.38s/it]
 44%|████▍     | 1603/3640 [36:51<46:50,  1.38s/it]
 44%|████▍     | 1604/3640 [36:52<46:44,  1.38s/it]
 44%|████▍     | 1605/3640 [36:54<46:39,  1.38s/it]
 44%|████▍     | 1606/3640 [36:55<46:35,  1.37s/it]
 44%|████▍     | 1607/3640 [36:56<46:33,  1.37s/it]
 44%|████▍     | 1608/3640 [36:58<46:30,  1.37s/it]
 44%|████▍     | 1609/3640 [36:59<46:23,  1.37s/it]
 44%|████▍     | 1610/3640 [37:00<46:25,  1.37s/it]
 44%|████▍     | 1611/3640 [37:02<46:22,  1.37s/it]
 44%|████▍     | 1612/3640 [37:03<46:20,  1.37s/it]
 44%|████▍     | 1613/3640 [37:05<46:21,  1.37s/it]
 44%|████▍     | 1614/3640 [37:06<46:23,  1.37s/it]
 44%|████▍     | 1615/3640 [37:07<46:21,  1.37s/it]
 44%|████▍     | 1616/3640 [37:09<46:16,  1.37s/it]
 44%|████▍     | 1617/3640 [37:10<46:15,  1.37s/it]
 44%|████▍     | 1618/3640 [37:11<46:11,  1.37s/it]
 44%|████▍     | 1619/3640 [37:13<46:13,  1.37s/it]
 45%|████▍     | 1620/3640 [37:14<46:05,  1.37s/it]
 45%|████▍     | 1621/3640 [37:15<45:59,  1.37s/it]
 45%|████▍     | 1622/3640 [37:17<46:01,  1.37s/it]
 45%|████▍     | 1623/3640 [37:18<46:45,  1.39s/it]
 45%|████▍     | 1624/3640 [37:20<46:31,  1.38s/it]
 45%|████▍     | 1625/3640 [37:21<46:20,  1.38s/it]
                                                   

 45%|████▍     | 1625/3640 [37:21<46:20,  1.38s/it]
 45%|████▍     | 1626/3640 [37:22<46:35,  1.39s/it]
 45%|████▍     | 1627/3640 [37:24<46:25,  1.38s/it]
 45%|████▍     | 1628/3640 [37:25<46:13,  1.38s/it]
 45%|████▍     | 1629/3640 [37:27<46:05,  1.38s/it]
 45%|████▍     | 1630/3640 [37:28<46:04,  1.38s/it]
 45%|████▍     | 1631/3640 [37:29<46:01,  1.37s/it]
 45%|████▍     | 1632/3640 [37:31<45:57,  1.37s/it]
 45%|████▍     | 1633/3640 [37:32<46:00,  1.38s/it]
 45%|████▍     | 1634/3640 [37:33<45:53,  1.37s/it]
 45%|████▍     | 1635/3640 [37:35<47:05,  1.41s/it]
 45%|████▍     | 1636/3640 [37:36<46:43,  1.40s/it]
 45%|████▍     | 1637/3640 [37:38<46:22,  1.39s/it]
 45%|████▌     | 1638/3640 [37:39<46:10,  1.38s/it]
 45%|████▌     | 1639/3640 [37:40<46:00,  1.38s/it]
 45%|████▌     | 1640/3640 [37:42<45:56,  1.38s/it]
 45%|████▌     | 1641/3640 [37:43<45:53,  1.38s/it]
 45%|████▌     | 1642/3640 [37:44<45:48,  1.38s/it]
 45%|████▌     | 1643/3640 [37:46<45:46,  1.38s/it]
 45%|████▌     | 1644/3640 [37:47<45:43,  1.37s/it]
 45%|████▌     | 1645/3640 [37:49<45:41,  1.37s/it]
 45%|████▌     | 1646/3640 [37:50<45:43,  1.38s/it]
 45%|████▌     | 1647/3640 [37:51<46:09,  1.39s/it]
 45%|████▌     | 1648/3640 [37:53<45:55,  1.38s/it]
 45%|████▌     | 1649/3640 [37:54<45:46,  1.38s/it]
 45%|████▌     | 1650/3640 [37:56<45:37,  1.38s/it]
                                                   

 45%|████▌     | 1650/3640 [37:56<45:37,  1.38s/it]
 45%|████▌     | 1651/3640 [37:57<45:57,  1.39s/it]
 45%|████▌     | 1652/3640 [37:58<45:46,  1.38s/it]
 45%|████▌     | 1653/3640 [38:00<45:37,  1.38s/it]
 45%|████▌     | 1654/3640 [38:01<45:33,  1.38s/it]
 45%|████▌     | 1655/3640 [38:02<45:24,  1.37s/it]
 45%|████▌     | 1656/3640 [38:04<45:18,  1.37s/it]
 46%|████▌     | 1657/3640 [38:05<45:18,  1.37s/it]
 46%|████▌     | 1658/3640 [38:07<45:18,  1.37s/it]
 46%|████▌     | 1659/3640 [38:08<45:19,  1.37s/it]
 46%|████▌     | 1660/3640 [38:09<45:14,  1.37s/it]
 46%|████▌     | 1661/3640 [38:11<45:14,  1.37s/it]
 46%|████▌     | 1662/3640 [38:12<45:10,  1.37s/it]
 46%|████▌     | 1663/3640 [38:13<45:09,  1.37s/it]
 46%|████▌     | 1664/3640 [38:15<45:01,  1.37s/it]
 46%|████▌     | 1665/3640 [38:16<45:04,  1.37s/it]
 46%|████▌     | 1666/3640 [38:17<45:07,  1.37s/it]
 46%|████▌     | 1667/3640 [38:19<45:06,  1.37s/it]
 46%|████▌     | 1668/3640 [38:20<45:08,  1.37s/it]
 46%|████▌     | 1669/3640 [38:22<45:05,  1.37s/it]
 46%|████▌     | 1670/3640 [38:23<45:06,  1.37s/it]
 46%|████▌     | 1671/3640 [38:24<45:42,  1.39s/it]
 46%|████▌     | 1672/3640 [38:26<45:25,  1.38s/it]
 46%|████▌     | 1673/3640 [38:27<45:17,  1.38s/it]
 46%|████▌     | 1674/3640 [38:29<45:12,  1.38s/it]
 46%|████▌     | 1675/3640 [38:30<45:08,  1.38s/it]
                                                   

 46%|████▌     | 1675/3640 [38:30<45:08,  1.38s/it]
 46%|████▌     | 1676/3640 [38:31<45:23,  1.39s/it]
 46%|████▌     | 1677/3640 [38:33<45:14,  1.38s/it]
 46%|████▌     | 1678/3640 [38:34<45:07,  1.38s/it]
 46%|████▌     | 1679/3640 [38:35<45:05,  1.38s/it]
 46%|████▌     | 1680/3640 [38:37<44:59,  1.38s/it]
 46%|████▌     | 1681/3640 [38:38<44:53,  1.38s/it]
 46%|████▌     | 1682/3640 [38:40<44:44,  1.37s/it]
 46%|████▌     | 1683/3640 [38:41<44:43,  1.37s/it]
 46%|████▋     | 1684/3640 [38:42<44:43,  1.37s/it]
 46%|████▋     | 1685/3640 [38:44<44:41,  1.37s/it]
 46%|████▋     | 1686/3640 [38:45<44:39,  1.37s/it]
 46%|████▋     | 1687/3640 [38:46<44:33,  1.37s/it]
 46%|████▋     | 1688/3640 [38:48<44:32,  1.37s/it]
 46%|████▋     | 1689/3640 [38:49<44:34,  1.37s/it]
 46%|████▋     | 1690/3640 [38:51<44:29,  1.37s/it]
 46%|████▋     | 1691/3640 [38:52<44:22,  1.37s/it]
 46%|████▋     | 1692/3640 [38:53<44:24,  1.37s/it]
 47%|████▋     | 1693/3640 [38:55<44:27,  1.37s/it]
 47%|████▋     | 1694/3640 [38:56<44:31,  1.37s/it]
 47%|████▋     | 1695/3640 [38:57<45:01,  1.39s/it]
 47%|████▋     | 1696/3640 [38:59<44:47,  1.38s/it]
 47%|████▋     | 1697/3640 [39:00<46:35,  1.44s/it]
 47%|████▋     | 1698/3640 [39:02<45:47,  1.41s/it]
 47%|████▋     | 1699/3640 [39:03<45:19,  1.40s/it]
 47%|████▋     | 1700/3640 [39:04<45:03,  1.39s/it]
                                                   

 47%|████▋     | 1700/3640 [39:05<45:03,  1.39s/it]
 47%|████▋     | 1701/3640 [39:06<45:11,  1.40s/it]
 47%|████▋     | 1702/3640 [39:07<44:55,  1.39s/it]
 47%|████▋     | 1703/3640 [39:09<44:44,  1.39s/it]
 47%|████▋     | 1704/3640 [39:10<44:36,  1.38s/it]
 47%|████▋     | 1705/3640 [39:11<44:29,  1.38s/it]
 47%|████▋     | 1706/3640 [39:13<44:24,  1.38s/it]
 47%|████▋     | 1707/3640 [39:14<44:08,  1.37s/it]
 47%|████▋     | 1708/3640 [39:15<44:08,  1.37s/it]
 47%|████▋     | 1709/3640 [39:17<44:09,  1.37s/it]
 47%|████▋     | 1710/3640 [39:18<44:05,  1.37s/it]
 47%|████▋     | 1711/3640 [39:20<44:12,  1.37s/it]
 47%|████▋     | 1712/3640 [39:21<44:07,  1.37s/it]
 47%|████▋     | 1713/3640 [39:22<44:04,  1.37s/it]
 47%|████▋     | 1714/3640 [39:24<44:01,  1.37s/it]
 47%|████▋     | 1715/3640 [39:25<44:01,  1.37s/it]
 47%|████▋     | 1716/3640 [39:26<43:53,  1.37s/it]
 47%|████▋     | 1717/3640 [39:28<43:48,  1.37s/it]
 47%|████▋     | 1718/3640 [39:29<44:28,  1.39s/it]
 47%|████▋     | 1719/3640 [39:31<44:16,  1.38s/it]
 47%|████▋     | 1720/3640 [39:32<44:11,  1.38s/it]
 47%|████▋     | 1721/3640 [39:33<43:55,  1.37s/it]
 47%|████▋     | 1722/3640 [39:35<43:55,  1.37s/it]
 47%|████▋     | 1723/3640 [39:36<43:53,  1.37s/it]
 47%|████▋     | 1724/3640 [39:37<43:50,  1.37s/it]
 47%|████▋     | 1725/3640 [39:39<43:47,  1.37s/it]
                                                   

 47%|████▋     | 1725/3640 [39:39<43:47,  1.37s/it]
 47%|████▋     | 1726/3640 [39:40<44:19,  1.39s/it]
 47%|████▋     | 1727/3640 [39:42<44:10,  1.39s/it]
 47%|████▋     | 1728/3640 [39:43<43:59,  1.38s/it]
 48%|████▊     | 1729/3640 [39:44<43:51,  1.38s/it]
 48%|████▊     | 1730/3640 [39:46<43:48,  1.38s/it]
 48%|████▊     | 1731/3640 [39:47<43:43,  1.37s/it]
 48%|████▊     | 1732/3640 [39:48<43:40,  1.37s/it]
 48%|████▊     | 1733/3640 [39:50<43:39,  1.37s/it]
 48%|████▊     | 1734/3640 [39:51<43:38,  1.37s/it]
 48%|████▊     | 1735/3640 [39:53<43:27,  1.37s/it]
 48%|████▊     | 1736/3640 [39:54<43:28,  1.37s/it]
 48%|████▊     | 1737/3640 [39:55<43:31,  1.37s/it]
 48%|████▊     | 1738/3640 [39:57<43:28,  1.37s/it]
 48%|████▊     | 1739/3640 [39:58<43:27,  1.37s/it]
 48%|████▊     | 1740/3640 [39:59<43:27,  1.37s/it]
 48%|████▊     | 1741/3640 [40:01<43:24,  1.37s/it]
 48%|████▊     | 1742/3640 [40:02<44:02,  1.39s/it]
 48%|████▊     | 1743/3640 [40:04<43:45,  1.38s/it]
 48%|████▊     | 1744/3640 [40:05<43:37,  1.38s/it]
 48%|████▊     | 1745/3640 [40:06<43:31,  1.38s/it]
 48%|████▊     | 1746/3640 [40:08<43:26,  1.38s/it]
 48%|████▊     | 1747/3640 [40:09<43:23,  1.38s/it]
 48%|████▊     | 1748/3640 [40:10<43:19,  1.37s/it]
 48%|████▊     | 1749/3640 [40:12<43:12,  1.37s/it]
 48%|████▊     | 1750/3640 [40:13<43:11,  1.37s/it]
                                                   

 48%|████▊     | 1750/3640 [40:13<43:11,  1.37s/it]
 48%|████▊     | 1751/3640 [40:15<43:46,  1.39s/it]
 48%|████▊     | 1752/3640 [40:16<43:33,  1.38s/it]
 48%|████▊     | 1753/3640 [40:17<43:22,  1.38s/it]
 48%|████▊     | 1754/3640 [40:19<43:07,  1.37s/it]
 48%|████▊     | 1755/3640 [40:20<43:06,  1.37s/it]
 48%|████▊     | 1756/3640 [40:22<43:05,  1.37s/it]
 48%|████▊     | 1757/3640 [40:23<42:59,  1.37s/it]
 48%|████▊     | 1758/3640 [40:24<42:57,  1.37s/it]
 48%|████▊     | 1759/3640 [40:26<42:56,  1.37s/it]
 48%|████▊     | 1760/3640 [40:27<42:58,  1.37s/it]
 48%|████▊     | 1761/3640 [40:28<42:53,  1.37s/it]
 48%|████▊     | 1762/3640 [40:30<42:54,  1.37s/it]
 48%|████▊     | 1763/3640 [40:31<42:53,  1.37s/it]
 48%|████▊     | 1764/3640 [40:32<42:54,  1.37s/it]
 48%|████▊     | 1765/3640 [40:34<42:55,  1.37s/it]
 49%|████▊     | 1766/3640 [40:35<43:35,  1.40s/it]
 49%|████▊     | 1767/3640 [40:37<43:22,  1.39s/it]
 49%|████▊     | 1768/3640 [40:38<43:14,  1.39s/it]
 49%|████▊     | 1769/3640 [40:39<43:06,  1.38s/it]
 49%|████▊     | 1770/3640 [40:41<42:58,  1.38s/it]
 49%|████▊     | 1771/3640 [40:42<42:53,  1.38s/it]
 49%|████▊     | 1772/3640 [40:44<42:48,  1.38s/it]
 49%|████▊     | 1773/3640 [40:45<42:45,  1.37s/it]
 49%|████▊     | 1774/3640 [40:46<42:39,  1.37s/it]
 49%|████▉     | 1775/3640 [40:48<42:34,  1.37s/it]
                                                   

 49%|████▉     | 1775/3640 [40:48<42:34,  1.37s/it]
 49%|████▉     | 1776/3640 [40:49<42:54,  1.38s/it]
 49%|████▉     | 1777/3640 [40:50<42:46,  1.38s/it]
 49%|████▉     | 1778/3640 [40:52<42:41,  1.38s/it]
 49%|████▉     | 1779/3640 [40:53<42:41,  1.38s/it]
 49%|████▉     | 1780/3640 [40:55<42:40,  1.38s/it]
 49%|████▉     | 1781/3640 [40:56<42:37,  1.38s/it]
 49%|████▉     | 1782/3640 [40:57<44:32,  1.44s/it]
 49%|████▉     | 1783/3640 [40:59<43:51,  1.42s/it]
 49%|████▉     | 1784/3640 [41:00<43:21,  1.40s/it]
 49%|████▉     | 1785/3640 [41:02<43:03,  1.39s/it]
 49%|████▉     | 1786/3640 [41:03<42:49,  1.39s/it]
 49%|████▉     | 1787/3640 [41:04<42:34,  1.38s/it]
 49%|████▉     | 1788/3640 [41:06<42:29,  1.38s/it]
 49%|████▉     | 1789/3640 [41:07<42:32,  1.38s/it]
 49%|████▉     | 1790/3640 [41:09<42:54,  1.39s/it]
 49%|████▉     | 1791/3640 [41:10<42:43,  1.39s/it]
 49%|████▉     | 1792/3640 [41:11<42:35,  1.38s/it]
 49%|████▉     | 1793/3640 [41:13<42:27,  1.38s/it]
 49%|████▉     | 1794/3640 [41:14<42:21,  1.38s/it]
 49%|████▉     | 1795/3640 [41:15<42:16,  1.37s/it]
 49%|████▉     | 1796/3640 [41:17<42:14,  1.37s/it]
 49%|████▉     | 1797/3640 [41:18<42:13,  1.37s/it]
 49%|████▉     | 1798/3640 [41:19<42:09,  1.37s/it]
 49%|████▉     | 1799/3640 [41:21<42:08,  1.37s/it]
 49%|████▉     | 1800/3640 [41:22<42:07,  1.37s/it]
                                                   

 49%|████▉     | 1800/3640 [41:22<42:07,  1.37s/it]
 49%|████▉     | 1801/3640 [41:24<42:31,  1.39s/it]
 50%|████▉     | 1802/3640 [41:25<42:19,  1.38s/it]
 50%|████▉     | 1803/3640 [41:27<44:02,  1.44s/it]
 50%|████▉     | 1804/3640 [41:28<43:19,  1.42s/it]
 50%|████▉     | 1805/3640 [41:29<42:52,  1.40s/it]
 50%|████▉     | 1806/3640 [41:31<42:34,  1.39s/it]
 50%|████▉     | 1807/3640 [41:32<42:19,  1.39s/it]
 50%|████▉     | 1808/3640 [41:33<42:06,  1.38s/it]
 50%|████▉     | 1809/3640 [41:35<41:57,  1.37s/it]
 50%|████▉     | 1810/3640 [41:36<41:56,  1.37s/it]
 50%|████▉     | 1811/3640 [41:38<41:58,  1.38s/it]
 50%|████▉     | 1812/3640 [41:39<41:56,  1.38s/it]
 50%|████▉     | 1813/3640 [41:40<42:35,  1.40s/it]
 50%|████▉     | 1814/3640 [41:42<42:12,  1.39s/it]
 50%|████▉     | 1815/3640 [41:43<42:02,  1.38s/it]
 50%|████▉     | 1816/3640 [41:44<41:58,  1.38s/it]
 50%|████▉     | 1817/3640 [41:46<41:52,  1.38s/it]
 50%|████▉     | 1818/3640 [41:47<41:47,  1.38s/it]
 50%|████▉     | 1819/3640 [41:49<41:44,  1.38s/it]
 50%|█████     | 1820/3640 [41:50<41:43,  1.38s/it]
 50%|█████     | 1821/3640 [41:51<41:30,  1.37s/it]
 50%|█████     | 1822/3640 [41:53<41:25,  1.37s/it]
 50%|█████     | 1823/3640 [41:54<41:27,  1.37s/it]
 50%|█████     | 1824/3640 [41:55<41:27,  1.37s/it]
 50%|█████     | 1825/3640 [41:57<41:24,  1.37s/it]
                                                   

 50%|█████     | 1825/3640 [41:57<41:24,  1.37s/it]
 50%|█████     | 1826/3640 [41:58<41:49,  1.38s/it]
 50%|█████     | 1827/3640 [42:00<41:43,  1.38s/it]
 50%|█████     | 1828/3640 [42:01<41:35,  1.38s/it]
 50%|█████     | 1829/3640 [42:02<41:30,  1.38s/it]
 50%|█████     | 1830/3640 [42:04<41:21,  1.37s/it]
 50%|█████     | 1831/3640 [42:05<41:23,  1.37s/it]
 50%|█████     | 1832/3640 [42:06<41:17,  1.37s/it]
 50%|█████     | 1833/3640 [42:08<41:18,  1.37s/it]
 50%|█████     | 1834/3640 [42:09<41:18,  1.37s/it]
 50%|█████     | 1835/3640 [42:11<41:17,  1.37s/it]
 50%|█████     | 1836/3640 [42:12<41:18,  1.37s/it]
 50%|█████     | 1837/3640 [42:13<42:01,  1.40s/it]
 50%|█████     | 1838/3640 [42:15<41:49,  1.39s/it]
 51%|█████     | 1839/3640 [42:16<41:36,  1.39s/it]
 51%|█████     | 1840/3640 [42:18<41:28,  1.38s/it]
 51%|█████     | 1841/3640 [42:19<41:20,  1.38s/it]
 51%|█████     | 1842/3640 [42:20<41:11,  1.37s/it]
 51%|█████     | 1843/3640 [42:22<41:07,  1.37s/it]
 51%|█████     | 1844/3640 [42:23<41:07,  1.37s/it]
 51%|█████     | 1845/3640 [42:24<41:04,  1.37s/it]
 51%|█████     | 1846/3640 [42:26<41:02,  1.37s/it]
 51%|█████     | 1847/3640 [42:27<40:56,  1.37s/it]
 51%|█████     | 1848/3640 [42:28<40:56,  1.37s/it]
 51%|█████     | 1849/3640 [42:30<40:45,  1.37s/it]
 51%|█████     | 1850/3640 [42:31<40:43,  1.36s/it]
                                                   

 51%|█████     | 1850/3640 [42:31<40:43,  1.36s/it]
 51%|█████     | 1851/3640 [42:33<41:13,  1.38s/it]
 51%|█████     | 1852/3640 [42:34<41:05,  1.38s/it]
 51%|█████     | 1853/3640 [42:35<40:59,  1.38s/it]
 51%|█████     | 1854/3640 [42:37<40:47,  1.37s/it]
 51%|█████     | 1855/3640 [42:38<40:44,  1.37s/it]
 51%|█████     | 1856/3640 [42:39<40:46,  1.37s/it]
 51%|█████     | 1857/3640 [42:41<40:41,  1.37s/it]
 51%|█████     | 1858/3640 [42:42<40:41,  1.37s/it]
 51%|█████     | 1859/3640 [42:44<40:40,  1.37s/it]
 51%|█████     | 1860/3640 [42:45<40:41,  1.37s/it]
 51%|█████     | 1861/3640 [42:46<41:11,  1.39s/it]
 51%|█████     | 1862/3640 [42:48<40:59,  1.38s/it]
 51%|█████     | 1863/3640 [42:49<40:48,  1.38s/it]
 51%|█████     | 1864/3640 [42:50<40:41,  1.37s/it]
 51%|█████     | 1865/3640 [42:52<40:34,  1.37s/it]
 51%|█████▏    | 1866/3640 [42:53<40:32,  1.37s/it]
 51%|█████▏    | 1867/3640 [42:55<40:30,  1.37s/it]
 51%|█████▏    | 1868/3640 [42:56<40:28,  1.37s/it]
 51%|█████▏    | 1869/3640 [42:57<40:26,  1.37s/it]
 51%|█████▏    | 1870/3640 [42:59<40:26,  1.37s/it]
 51%|█████▏    | 1871/3640 [43:00<40:22,  1.37s/it]
 51%|█████▏    | 1872/3640 [43:01<40:22,  1.37s/it]
 51%|█████▏    | 1873/3640 [43:03<40:22,  1.37s/it]
 51%|█████▏    | 1874/3640 [43:04<40:18,  1.37s/it]
 52%|█████▏    | 1875/3640 [43:06<40:18,  1.37s/it]
                                                   

 52%|█████▏    | 1875/3640 [43:06<40:18,  1.37s/it]
 52%|█████▏    | 1876/3640 [43:07<40:52,  1.39s/it]
 52%|█████▏    | 1877/3640 [43:09<42:21,  1.44s/it]
 52%|█████▏    | 1878/3640 [43:10<41:44,  1.42s/it]
 52%|█████▏    | 1879/3640 [43:11<41:17,  1.41s/it]
 52%|█████▏    | 1880/3640 [43:13<40:56,  1.40s/it]
 52%|█████▏    | 1881/3640 [43:14<40:43,  1.39s/it]
 52%|█████▏    | 1882/3640 [43:15<40:26,  1.38s/it]
 52%|█████▏    | 1883/3640 [43:17<40:20,  1.38s/it]
 52%|█████▏    | 1884/3640 [43:18<40:21,  1.38s/it]
 52%|█████▏    | 1885/3640 [43:20<40:43,  1.39s/it]
 52%|█████▏    | 1886/3640 [43:21<40:29,  1.39s/it]
 52%|█████▏    | 1887/3640 [43:22<40:20,  1.38s/it]
 52%|█████▏    | 1888/3640 [43:24<40:15,  1.38s/it]
 52%|█████▏    | 1889/3640 [43:25<41:58,  1.44s/it]
 52%|█████▏    | 1890/3640 [43:27<41:17,  1.42s/it]
 52%|█████▏    | 1891/3640 [43:28<40:54,  1.40s/it]
 52%|█████▏    | 1892/3640 [43:29<40:35,  1.39s/it]
 52%|█████▏    | 1893/3640 [43:31<40:17,  1.38s/it]
 52%|█████▏    | 1894/3640 [43:32<40:07,  1.38s/it]
 52%|█████▏    | 1895/3640 [43:33<40:04,  1.38s/it]
 52%|█████▏    | 1896/3640 [43:35<39:59,  1.38s/it]
 52%|█████▏    | 1897/3640 [43:36<39:55,  1.37s/it]
 52%|█████▏    | 1898/3640 [43:38<39:48,  1.37s/it]
 52%|█████▏    | 1899/3640 [43:39<39:45,  1.37s/it]
 52%|█████▏    | 1900/3640 [43:40<39:47,  1.37s/it]
                                                   

 52%|█████▏    | 1900/3640 [43:40<39:47,  1.37s/it]
 52%|█████▏    | 1901/3640 [43:42<40:05,  1.38s/it]
 52%|█████▏    | 1902/3640 [43:43<39:57,  1.38s/it]
 52%|█████▏    | 1903/3640 [43:44<39:52,  1.38s/it]
 52%|█████▏    | 1904/3640 [43:46<39:44,  1.37s/it]
 52%|█████▏    | 1905/3640 [43:47<39:40,  1.37s/it]
 52%|█████▏    | 1906/3640 [43:49<39:38,  1.37s/it]
 52%|█████▏    | 1907/3640 [43:50<39:37,  1.37s/it]
 52%|█████▏    | 1908/3640 [43:51<40:06,  1.39s/it]
 52%|█████▏    | 1909/3640 [43:53<39:59,  1.39s/it]
 52%|█████▏    | 1910/3640 [43:54<39:48,  1.38s/it]
 52%|█████▎    | 1911/3640 [43:55<39:43,  1.38s/it]
 53%|█████▎    | 1912/3640 [43:57<39:39,  1.38s/it]
 53%|█████▎    | 1913/3640 [43:58<39:34,  1.38s/it]
 53%|█████▎    | 1914/3640 [44:00<39:28,  1.37s/it]
 53%|█████▎    | 1915/3640 [44:01<39:25,  1.37s/it]
 53%|█████▎    | 1916/3640 [44:02<39:23,  1.37s/it]
 53%|█████▎    | 1917/3640 [44:04<39:22,  1.37s/it]
 53%|█████▎    | 1918/3640 [44:05<39:20,  1.37s/it]
 53%|█████▎    | 1919/3640 [44:06<39:12,  1.37s/it]
 53%|█████▎    | 1920/3640 [44:08<39:11,  1.37s/it]
 53%|█████▎    | 1921/3640 [44:09<39:14,  1.37s/it]
 53%|█████▎    | 1922/3640 [44:11<39:06,  1.37s/it]
 53%|█████▎    | 1923/3640 [44:12<39:01,  1.36s/it]
 53%|█████▎    | 1924/3640 [44:13<39:03,  1.37s/it]
 53%|█████▎    | 1925/3640 [44:15<39:05,  1.37s/it]
                                                   

 53%|█████▎    | 1925/3640 [44:15<39:05,  1.37s/it]
 53%|█████▎    | 1926/3640 [44:16<39:23,  1.38s/it]
 53%|█████▎    | 1927/3640 [44:17<39:16,  1.38s/it]
 53%|█████▎    | 1928/3640 [44:19<39:11,  1.37s/it]
 53%|█████▎    | 1929/3640 [44:20<39:11,  1.37s/it]
 53%|█████▎    | 1930/3640 [44:22<39:03,  1.37s/it]
 53%|█████▎    | 1931/3640 [44:23<38:57,  1.37s/it]
 53%|█████▎    | 1932/3640 [44:24<39:31,  1.39s/it]
 53%|█████▎    | 1933/3640 [44:26<39:23,  1.38s/it]
 53%|█████▎    | 1934/3640 [44:27<39:16,  1.38s/it]
 53%|█████▎    | 1935/3640 [44:28<39:12,  1.38s/it]
 53%|█████▎    | 1936/3640 [44:30<39:00,  1.37s/it]
 53%|█████▎    | 1937/3640 [44:31<38:53,  1.37s/it]
 53%|█████▎    | 1938/3640 [44:33<38:50,  1.37s/it]
 53%|█████▎    | 1939/3640 [44:34<38:56,  1.37s/it]
 53%|█████▎    | 1940/3640 [44:35<38:54,  1.37s/it]
 53%|█████▎    | 1941/3640 [44:37<38:50,  1.37s/it]
 53%|█████▎    | 1942/3640 [44:38<38:46,  1.37s/it]
 53%|█████▎    | 1943/3640 [44:39<38:44,  1.37s/it]
 53%|█████▎    | 1944/3640 [44:41<38:45,  1.37s/it]
 53%|█████▎    | 1945/3640 [44:42<38:44,  1.37s/it]
 53%|█████▎    | 1946/3640 [44:44<38:42,  1.37s/it]
 53%|█████▎    | 1947/3640 [44:45<38:41,  1.37s/it]
 54%|█████▎    | 1948/3640 [44:46<38:37,  1.37s/it]
 54%|█████▎    | 1949/3640 [44:48<38:39,  1.37s/it]
 54%|█████▎    | 1950/3640 [44:49<38:35,  1.37s/it]
                                                   

 54%|█████▎    | 1950/3640 [44:49<38:35,  1.37s/it]
 54%|█████▎    | 1951/3640 [44:50<39:00,  1.39s/it]
 54%|█████▎    | 1952/3640 [44:52<38:50,  1.38s/it]
 54%|█████▎    | 1953/3640 [44:53<38:44,  1.38s/it]
 54%|█████▎    | 1954/3640 [44:55<38:36,  1.37s/it]
 54%|█████▎    | 1955/3640 [44:56<38:33,  1.37s/it]
 54%|█████▎    | 1956/3640 [44:57<39:07,  1.39s/it]
 54%|█████▍    | 1957/3640 [44:59<38:56,  1.39s/it]
 54%|█████▍    | 1958/3640 [45:00<38:48,  1.38s/it]
 54%|█████▍    | 1959/3640 [45:01<38:41,  1.38s/it]
 54%|█████▍    | 1960/3640 [45:03<38:35,  1.38s/it]
 54%|█████▍    | 1961/3640 [45:04<38:26,  1.37s/it]
 54%|█████▍    | 1962/3640 [45:06<38:25,  1.37s/it]
 54%|█████▍    | 1963/3640 [45:07<40:08,  1.44s/it]
 54%|█████▍    | 1964/3640 [45:09<39:32,  1.42s/it]
 54%|█████▍    | 1965/3640 [45:10<39:11,  1.40s/it]
 54%|█████▍    | 1966/3640 [45:11<38:53,  1.39s/it]
 54%|█████▍    | 1967/3640 [45:13<38:36,  1.38s/it]
 54%|█████▍    | 1968/3640 [45:14<38:23,  1.38s/it]
 54%|█████▍    | 1969/3640 [45:15<38:19,  1.38s/it]
 54%|█████▍    | 1970/3640 [45:17<38:16,  1.38s/it]
 54%|█████▍    | 1971/3640 [45:18<38:08,  1.37s/it]
 54%|█████▍    | 1972/3640 [45:19<38:07,  1.37s/it]
 54%|█████▍    | 1973/3640 [45:21<38:08,  1.37s/it]
 54%|█████▍    | 1974/3640 [45:22<38:06,  1.37s/it]
 54%|█████▍    | 1975/3640 [45:24<38:01,  1.37s/it]
                                                   

 54%|█████▍    | 1975/3640 [45:24<38:01,  1.37s/it]
 54%|█████▍    | 1976/3640 [45:25<38:22,  1.38s/it]
 54%|█████▍    | 1977/3640 [45:26<38:08,  1.38s/it]
 54%|█████▍    | 1978/3640 [45:28<37:53,  1.37s/it]
 54%|█████▍    | 1979/3640 [45:29<37:51,  1.37s/it]
 54%|█████▍    | 1980/3640 [45:31<38:26,  1.39s/it]
 54%|█████▍    | 1981/3640 [45:32<38:17,  1.38s/it]
 54%|█████▍    | 1982/3640 [45:33<38:09,  1.38s/it]
 54%|█████▍    | 1983/3640 [45:35<38:04,  1.38s/it]
 55%|█████▍    | 1984/3640 [45:36<37:57,  1.38s/it]
 55%|█████▍    | 1985/3640 [45:37<37:55,  1.37s/it]
 55%|█████▍    | 1986/3640 [45:39<37:52,  1.37s/it]
 55%|█████▍    | 1987/3640 [45:40<37:49,  1.37s/it]
 55%|█████▍    | 1988/3640 [45:41<37:42,  1.37s/it]
 55%|█████▍    | 1989/3640 [45:43<37:39,  1.37s/it]
 55%|█████▍    | 1990/3640 [45:44<37:41,  1.37s/it]
 55%|█████▍    | 1991/3640 [45:46<37:42,  1.37s/it]
 55%|█████▍    | 1992/3640 [45:47<37:43,  1.37s/it]
 55%|█████▍    | 1993/3640 [45:48<37:37,  1.37s/it]
 55%|█████▍    | 1994/3640 [45:50<37:36,  1.37s/it]
 55%|█████▍    | 1995/3640 [45:51<37:35,  1.37s/it]
 55%|█████▍    | 1996/3640 [45:52<37:32,  1.37s/it]
 55%|█████▍    | 1997/3640 [45:54<37:31,  1.37s/it]
 55%|█████▍    | 1998/3640 [45:55<37:33,  1.37s/it]
 55%|█████▍    | 1999/3640 [45:57<37:30,  1.37s/it]
 55%|█████▍    | 2000/3640 [45:58<37:29,  1.37s/it]
                                                   

 55%|█████▍    | 2000/3640 [45:58<37:29,  1.37s/it]
 55%|█████▍    | 2001/3640 [45:59<37:53,  1.39s/it]
 55%|█████▌    | 2002/3640 [46:01<37:46,  1.38s/it]
 55%|█████▌    | 2003/3640 [46:02<37:55,  1.39s/it]
 55%|█████▌    | 2004/3640 [46:04<38:00,  1.39s/it]
 55%|█████▌    | 2005/3640 [46:05<37:50,  1.39s/it]
 55%|█████▌    | 2006/3640 [46:06<37:41,  1.38s/it]
 55%|█████▌    | 2007/3640 [46:08<37:32,  1.38s/it]
 55%|█████▌    | 2008/3640 [46:09<37:23,  1.37s/it]
 55%|█████▌    | 2009/3640 [46:10<37:22,  1.37s/it]
 55%|█████▌    | 2010/3640 [46:12<37:18,  1.37s/it]
 55%|█████▌    | 2011/3640 [46:13<37:11,  1.37s/it]
 55%|█████▌    | 2012/3640 [46:15<37:11,  1.37s/it]
 55%|█████▌    | 2013/3640 [46:16<37:11,  1.37s/it]
 55%|█████▌    | 2014/3640 [46:17<37:10,  1.37s/it]
 55%|█████▌    | 2015/3640 [46:19<37:09,  1.37s/it]
 55%|█████▌    | 2016/3640 [46:20<37:00,  1.37s/it]
 55%|█████▌    | 2017/3640 [46:21<37:00,  1.37s/it]
 55%|█████▌    | 2018/3640 [46:23<36:56,  1.37s/it]
 55%|█████▌    | 2019/3640 [46:24<36:58,  1.37s/it]
 55%|█████▌    | 2020/3640 [46:25<36:58,  1.37s/it]
 56%|█████▌    | 2021/3640 [46:27<36:58,  1.37s/it]
 56%|█████▌    | 2022/3640 [46:28<36:57,  1.37s/it]
 56%|█████▌    | 2023/3640 [46:30<36:53,  1.37s/it]
 56%|█████▌    | 2024/3640 [46:31<36:54,  1.37s/it]
 56%|█████▌    | 2025/3640 [46:32<36:52,  1.37s/it]
                                                   

 56%|█████▌    | 2025/3640 [46:32<36:52,  1.37s/it]
 56%|█████▌    | 2026/3640 [46:34<37:11,  1.38s/it]
 56%|█████▌    | 2027/3640 [46:35<37:34,  1.40s/it]
 56%|█████▌    | 2028/3640 [46:37<37:26,  1.39s/it]
 56%|█████▌    | 2029/3640 [46:38<37:16,  1.39s/it]
 56%|█████▌    | 2030/3640 [46:39<37:03,  1.38s/it]
 56%|█████▌    | 2031/3640 [46:41<36:57,  1.38s/it]
 56%|█████▌    | 2032/3640 [46:42<36:52,  1.38s/it]
 56%|█████▌    | 2033/3640 [46:43<36:43,  1.37s/it]
 56%|█████▌    | 2034/3640 [46:45<36:44,  1.37s/it]
 56%|█████▌    | 2035/3640 [46:46<36:42,  1.37s/it]
 56%|█████▌    | 2036/3640 [46:47<36:39,  1.37s/it]
 56%|█████▌    | 2037/3640 [46:49<36:34,  1.37s/it]
 56%|█████▌    | 2038/3640 [46:50<36:34,  1.37s/it]
 56%|█████▌    | 2039/3640 [46:52<36:33,  1.37s/it]
 56%|█████▌    | 2040/3640 [46:53<36:30,  1.37s/it]
 56%|█████▌    | 2041/3640 [46:54<36:26,  1.37s/it]
 56%|█████▌    | 2042/3640 [46:56<36:24,  1.37s/it]
 56%|█████▌    | 2043/3640 [46:57<36:23,  1.37s/it]
 56%|█████▌    | 2044/3640 [46:58<36:23,  1.37s/it]
 56%|█████▌    | 2045/3640 [47:00<36:17,  1.37s/it]
 56%|█████▌    | 2046/3640 [47:01<36:15,  1.37s/it]
 56%|█████▌    | 2047/3640 [47:03<36:16,  1.37s/it]
 56%|█████▋    | 2048/3640 [47:04<36:15,  1.37s/it]
 56%|█████▋    | 2049/3640 [47:05<36:17,  1.37s/it]
 56%|█████▋    | 2050/3640 [47:07<36:18,  1.37s/it]
                                                   

 56%|█████▋    | 2050/3640 [47:07<36:18,  1.37s/it]
 56%|█████▋    | 2051/3640 [47:08<37:05,  1.40s/it]
 56%|█████▋    | 2052/3640 [47:09<36:51,  1.39s/it]
 56%|█████▋    | 2053/3640 [47:11<36:42,  1.39s/it]
 56%|█████▋    | 2054/3640 [47:12<36:32,  1.38s/it]
 56%|█████▋    | 2055/3640 [47:14<36:25,  1.38s/it]
 56%|█████▋    | 2056/3640 [47:15<36:21,  1.38s/it]
 57%|█████▋    | 2057/3640 [47:16<36:15,  1.37s/it]
 57%|█████▋    | 2058/3640 [47:18<36:09,  1.37s/it]
 57%|█████▋    | 2059/3640 [47:19<36:09,  1.37s/it]
 57%|█████▋    | 2060/3640 [47:20<36:10,  1.37s/it]
 57%|█████▋    | 2061/3640 [47:22<36:08,  1.37s/it]
 57%|█████▋    | 2062/3640 [47:23<36:02,  1.37s/it]
 57%|█████▋    | 2063/3640 [47:25<36:02,  1.37s/it]
 57%|█████▋    | 2064/3640 [47:26<36:03,  1.37s/it]
 57%|█████▋    | 2065/3640 [47:27<35:59,  1.37s/it]
 57%|█████▋    | 2066/3640 [47:29<35:50,  1.37s/it]
 57%|█████▋    | 2067/3640 [47:30<35:48,  1.37s/it]
 57%|█████▋    | 2068/3640 [47:32<37:23,  1.43s/it]
 57%|█████▋    | 2069/3640 [47:33<36:55,  1.41s/it]
 57%|█████▋    | 2070/3640 [47:34<36:32,  1.40s/it]
 57%|█████▋    | 2071/3640 [47:36<36:20,  1.39s/it]
 57%|█████▋    | 2072/3640 [47:37<36:07,  1.38s/it]
 57%|█████▋    | 2073/3640 [47:38<36:02,  1.38s/it]
 57%|█████▋    | 2074/3640 [47:40<35:53,  1.38s/it]
 57%|█████▋    | 2075/3640 [47:41<36:21,  1.39s/it]
                                                   

 57%|█████▋    | 2075/3640 [47:41<36:21,  1.39s/it]
 57%|█████▋    | 2076/3640 [47:43<36:26,  1.40s/it]
 57%|█████▋    | 2077/3640 [47:44<36:12,  1.39s/it]
 57%|█████▋    | 2078/3640 [47:45<36:03,  1.39s/it]
 57%|█████▋    | 2079/3640 [47:47<35:55,  1.38s/it]
 57%|█████▋    | 2080/3640 [47:48<35:49,  1.38s/it]
 57%|█████▋    | 2081/3640 [47:50<35:46,  1.38s/it]
 57%|█████▋    | 2082/3640 [47:51<35:38,  1.37s/it]
 57%|█████▋    | 2083/3640 [47:52<35:35,  1.37s/it]
 57%|█████▋    | 2084/3640 [47:54<35:34,  1.37s/it]
 57%|█████▋    | 2085/3640 [47:55<35:32,  1.37s/it]
 57%|█████▋    | 2086/3640 [47:56<35:30,  1.37s/it]
 57%|█████▋    | 2087/3640 [47:58<35:26,  1.37s/it]
 57%|█████▋    | 2088/3640 [47:59<35:23,  1.37s/it]
 57%|█████▋    | 2089/3640 [48:00<35:20,  1.37s/it]
 57%|█████▋    | 2090/3640 [48:02<35:18,  1.37s/it]
 57%|█████▋    | 2091/3640 [48:03<35:19,  1.37s/it]
 57%|█████▋    | 2092/3640 [48:05<35:17,  1.37s/it]
 57%|█████▊    | 2093/3640 [48:06<35:16,  1.37s/it]
 58%|█████▊    | 2094/3640 [48:07<35:15,  1.37s/it]
 58%|█████▊    | 2095/3640 [48:09<35:11,  1.37s/it]
 58%|█████▊    | 2096/3640 [48:10<35:14,  1.37s/it]
 58%|█████▊    | 2097/3640 [48:11<35:13,  1.37s/it]
 58%|█████▊    | 2098/3640 [48:13<35:13,  1.37s/it]
 58%|█████▊    | 2099/3640 [48:14<35:43,  1.39s/it]
 58%|█████▊    | 2100/3640 [48:16<35:30,  1.38s/it]
                                                   

 58%|█████▊    | 2100/3640 [48:16<35:30,  1.38s/it]
 58%|█████▊    | 2101/3640 [48:17<35:44,  1.39s/it]
 58%|█████▊    | 2102/3640 [48:18<35:34,  1.39s/it]
 58%|█████▊    | 2103/3640 [48:20<35:25,  1.38s/it]
 58%|█████▊    | 2104/3640 [48:21<35:18,  1.38s/it]
 58%|█████▊    | 2105/3640 [48:22<35:09,  1.37s/it]
 58%|█████▊    | 2106/3640 [48:24<35:06,  1.37s/it]
 58%|█████▊    | 2107/3640 [48:25<35:07,  1.37s/it]
 58%|█████▊    | 2108/3640 [48:27<35:02,  1.37s/it]
 58%|█████▊    | 2109/3640 [48:28<34:58,  1.37s/it]
 58%|█████▊    | 2110/3640 [48:29<34:55,  1.37s/it]
 58%|█████▊    | 2111/3640 [48:31<34:54,  1.37s/it]
 58%|█████▊    | 2112/3640 [48:32<34:56,  1.37s/it]
 58%|█████▊    | 2113/3640 [48:33<34:55,  1.37s/it]
 58%|█████▊    | 2114/3640 [48:35<34:51,  1.37s/it]
 58%|█████▊    | 2115/3640 [48:36<34:47,  1.37s/it]
 58%|█████▊    | 2116/3640 [48:38<34:47,  1.37s/it]
 58%|█████▊    | 2117/3640 [48:39<34:46,  1.37s/it]
 58%|█████▊    | 2118/3640 [48:40<34:48,  1.37s/it]
 58%|█████▊    | 2119/3640 [48:42<34:45,  1.37s/it]
 58%|█████▊    | 2120/3640 [48:43<34:47,  1.37s/it]
 58%|█████▊    | 2121/3640 [48:44<34:45,  1.37s/it]
 58%|█████▊    | 2122/3640 [48:46<34:43,  1.37s/it]
 58%|█████▊    | 2123/3640 [48:47<35:04,  1.39s/it]
 58%|█████▊    | 2124/3640 [48:49<34:56,  1.38s/it]
 58%|█████▊    | 2125/3640 [48:50<34:48,  1.38s/it]
                                                   

 58%|█████▊    | 2125/3640 [48:50<34:48,  1.38s/it]
 58%|█████▊    | 2126/3640 [48:51<35:08,  1.39s/it]
 58%|█████▊    | 2127/3640 [48:53<34:53,  1.38s/it]
 58%|█████▊    | 2128/3640 [48:54<34:44,  1.38s/it]
 58%|█████▊    | 2129/3640 [48:55<34:34,  1.37s/it]
 59%|█████▊    | 2130/3640 [48:57<34:30,  1.37s/it]
 59%|█████▊    | 2131/3640 [48:58<34:28,  1.37s/it]
 59%|█████▊    | 2132/3640 [49:00<34:32,  1.37s/it]
 59%|█████▊    | 2133/3640 [49:01<34:29,  1.37s/it]
 59%|█████▊    | 2134/3640 [49:02<34:25,  1.37s/it]
 59%|█████▊    | 2135/3640 [49:04<34:22,  1.37s/it]
 59%|█████▊    | 2136/3640 [49:05<34:17,  1.37s/it]
 59%|█████▊    | 2137/3640 [49:06<34:19,  1.37s/it]
 59%|█████▊    | 2138/3640 [49:08<34:20,  1.37s/it]
 59%|█████▉    | 2139/3640 [49:09<34:17,  1.37s/it]
 59%|█████▉    | 2140/3640 [49:11<34:16,  1.37s/it]
 59%|█████▉    | 2141/3640 [49:12<34:10,  1.37s/it]
 59%|█████▉    | 2142/3640 [49:13<34:11,  1.37s/it]
 59%|█████▉    | 2143/3640 [49:15<34:08,  1.37s/it]
 59%|█████▉    | 2144/3640 [49:16<34:07,  1.37s/it]
 59%|█████▉    | 2145/3640 [49:17<33:59,  1.36s/it]
 59%|█████▉    | 2146/3640 [49:19<34:17,  1.38s/it]
 59%|█████▉    | 2147/3640 [49:20<34:20,  1.38s/it]
 59%|█████▉    | 2148/3640 [49:22<34:16,  1.38s/it]
 59%|█████▉    | 2149/3640 [49:23<34:09,  1.37s/it]
 59%|█████▉    | 2150/3640 [49:24<34:07,  1.37s/it]
                                                   

 59%|█████▉    | 2150/3640 [49:24<34:07,  1.37s/it]
 59%|█████▉    | 2151/3640 [49:26<34:22,  1.39s/it]
 59%|█████▉    | 2152/3640 [49:27<34:15,  1.38s/it]
 59%|█████▉    | 2153/3640 [49:28<34:08,  1.38s/it]
 59%|█████▉    | 2154/3640 [49:30<34:05,  1.38s/it]
 59%|█████▉    | 2155/3640 [49:31<33:57,  1.37s/it]
 59%|█████▉    | 2156/3640 [49:33<35:31,  1.44s/it]
 59%|█████▉    | 2157/3640 [49:34<35:00,  1.42s/it]
 59%|█████▉    | 2158/3640 [49:35<34:37,  1.40s/it]
 59%|█████▉    | 2159/3640 [49:37<34:22,  1.39s/it]
 59%|█████▉    | 2160/3640 [49:38<34:07,  1.38s/it]
 59%|█████▉    | 2161/3640 [49:40<34:01,  1.38s/it]
 59%|█████▉    | 2162/3640 [49:41<33:56,  1.38s/it]
 59%|█████▉    | 2163/3640 [49:42<33:51,  1.38s/it]
 59%|█████▉    | 2164/3640 [49:44<33:49,  1.37s/it]
 59%|█████▉    | 2165/3640 [49:45<33:39,  1.37s/it]
 60%|█████▉    | 2166/3640 [49:46<33:37,  1.37s/it]
 60%|█████▉    | 2167/3640 [49:48<33:34,  1.37s/it]
 60%|█████▉    | 2168/3640 [49:49<33:32,  1.37s/it]
 60%|█████▉    | 2169/3640 [49:51<33:29,  1.37s/it]
 60%|█████▉    | 2170/3640 [49:52<34:00,  1.39s/it]
 60%|█████▉    | 2171/3640 [49:53<33:53,  1.38s/it]
 60%|█████▉    | 2172/3640 [49:55<33:46,  1.38s/it]
 60%|█████▉    | 2173/3640 [49:56<33:40,  1.38s/it]
 60%|█████▉    | 2174/3640 [49:58<34:57,  1.43s/it]
 60%|█████▉    | 2175/3640 [49:59<34:28,  1.41s/it]
                                                   

 60%|█████▉    | 2175/3640 [49:59<34:28,  1.41s/it]
 60%|█████▉    | 2176/3640 [50:00<34:35,  1.42s/it]
 60%|█████▉    | 2177/3640 [50:02<34:14,  1.40s/it]
 60%|█████▉    | 2178/3640 [50:03<33:56,  1.39s/it]
 60%|█████▉    | 2179/3640 [50:05<33:47,  1.39s/it]
 60%|█████▉    | 2180/3640 [50:06<33:38,  1.38s/it]
 60%|█████▉    | 2181/3640 [50:07<33:30,  1.38s/it]
 60%|█████▉    | 2182/3640 [50:09<33:27,  1.38s/it]
 60%|█████▉    | 2183/3640 [50:10<33:25,  1.38s/it]
 60%|██████    | 2184/3640 [50:11<33:20,  1.37s/it]
 60%|██████    | 2185/3640 [50:13<33:16,  1.37s/it]
 60%|██████    | 2186/3640 [50:14<33:11,  1.37s/it]
 60%|██████    | 2187/3640 [50:16<33:10,  1.37s/it]
 60%|██████    | 2188/3640 [50:17<33:10,  1.37s/it]
 60%|██████    | 2189/3640 [50:18<33:06,  1.37s/it]
 60%|██████    | 2190/3640 [50:20<33:05,  1.37s/it]
 60%|██████    | 2191/3640 [50:21<33:00,  1.37s/it]
 60%|██████    | 2192/3640 [50:22<33:00,  1.37s/it]
 60%|██████    | 2193/3640 [50:24<32:55,  1.37s/it]
 60%|██████    | 2194/3640 [50:25<33:30,  1.39s/it]
 60%|██████    | 2195/3640 [50:27<33:21,  1.38s/it]
 60%|██████    | 2196/3640 [50:28<33:13,  1.38s/it]
 60%|██████    | 2197/3640 [50:29<33:11,  1.38s/it]
 60%|██████    | 2198/3640 [50:31<33:05,  1.38s/it]
 60%|██████    | 2199/3640 [50:32<32:57,  1.37s/it]
 60%|██████    | 2200/3640 [50:33<32:53,  1.37s/it]
                                                   

 60%|██████    | 2200/3640 [50:33<32:53,  1.37s/it]
 60%|██████    | 2201/3640 [50:35<33:08,  1.38s/it]
 60%|██████    | 2202/3640 [50:36<33:02,  1.38s/it]
 61%|██████    | 2203/3640 [50:38<32:54,  1.37s/it]
 61%|██████    | 2204/3640 [50:39<32:52,  1.37s/it]
 61%|██████    | 2205/3640 [50:40<32:50,  1.37s/it]
 61%|██████    | 2206/3640 [50:42<32:48,  1.37s/it]
 61%|██████    | 2207/3640 [50:43<32:42,  1.37s/it]
 61%|██████    | 2208/3640 [50:44<32:39,  1.37s/it]
 61%|██████    | 2209/3640 [50:46<32:37,  1.37s/it]
 61%|██████    | 2210/3640 [50:47<32:36,  1.37s/it]
 61%|██████    | 2211/3640 [50:48<32:36,  1.37s/it]
 61%|██████    | 2212/3640 [50:50<32:35,  1.37s/it]
 61%|██████    | 2213/3640 [50:51<32:33,  1.37s/it]
 61%|██████    | 2214/3640 [50:53<32:30,  1.37s/it]
 61%|██████    | 2215/3640 [50:54<32:30,  1.37s/it]
 61%|██████    | 2216/3640 [50:55<32:30,  1.37s/it]
 61%|██████    | 2217/3640 [50:57<32:26,  1.37s/it]
 61%|██████    | 2218/3640 [50:58<32:55,  1.39s/it]
 61%|██████    | 2219/3640 [51:00<32:45,  1.38s/it]
 61%|██████    | 2220/3640 [51:01<32:41,  1.38s/it]
 61%|██████    | 2221/3640 [51:02<32:35,  1.38s/it]
 61%|██████    | 2222/3640 [51:04<32:30,  1.38s/it]
 61%|██████    | 2223/3640 [51:05<32:24,  1.37s/it]
 61%|██████    | 2224/3640 [51:06<32:23,  1.37s/it]
 61%|██████    | 2225/3640 [51:08<32:21,  1.37s/it]
                                                   

 61%|██████    | 2225/3640 [51:08<32:21,  1.37s/it]
 61%|██████    | 2226/3640 [51:09<32:27,  1.38s/it]
 61%|██████    | 2227/3640 [51:10<32:25,  1.38s/it]
 61%|██████    | 2228/3640 [51:12<32:23,  1.38s/it]
 61%|██████    | 2229/3640 [51:13<32:19,  1.37s/it]
 61%|██████▏   | 2230/3640 [51:15<32:16,  1.37s/it]
 61%|██████▏   | 2231/3640 [51:16<32:13,  1.37s/it]
 61%|██████▏   | 2232/3640 [51:17<32:01,  1.37s/it]
 61%|██████▏   | 2233/3640 [51:19<32:01,  1.37s/it]
 61%|██████▏   | 2234/3640 [51:20<32:02,  1.37s/it]
 61%|██████▏   | 2235/3640 [51:21<31:59,  1.37s/it]
 61%|██████▏   | 2236/3640 [51:23<31:59,  1.37s/it]
 61%|██████▏   | 2237/3640 [51:24<32:00,  1.37s/it]
 61%|██████▏   | 2238/3640 [51:26<32:01,  1.37s/it]
 62%|██████▏   | 2239/3640 [51:27<32:03,  1.37s/it]
 62%|██████▏   | 2240/3640 [51:28<32:00,  1.37s/it]
 62%|██████▏   | 2241/3640 [51:30<32:06,  1.38s/it]
 62%|██████▏   | 2242/3640 [51:31<32:18,  1.39s/it]
 62%|██████▏   | 2243/3640 [51:32<32:08,  1.38s/it]
 62%|██████▏   | 2244/3640 [51:34<32:04,  1.38s/it]
 62%|██████▏   | 2245/3640 [51:35<32:02,  1.38s/it]
 62%|██████▏   | 2246/3640 [51:37<31:57,  1.38s/it]
 62%|██████▏   | 2247/3640 [51:38<31:52,  1.37s/it]
 62%|██████▏   | 2248/3640 [51:39<33:04,  1.43s/it]
 62%|██████▏   | 2249/3640 [51:41<32:41,  1.41s/it]
 62%|██████▏   | 2250/3640 [51:42<32:19,  1.40s/it]
                                                   

 62%|██████▏   | 2250/3640 [51:42<32:19,  1.40s/it]
 62%|██████▏   | 2251/3640 [51:44<32:23,  1.40s/it]
 62%|██████▏   | 2252/3640 [51:45<32:11,  1.39s/it]
 62%|██████▏   | 2253/3640 [51:46<32:00,  1.38s/it]
 62%|██████▏   | 2254/3640 [51:48<31:51,  1.38s/it]
 62%|██████▏   | 2255/3640 [51:49<31:43,  1.37s/it]
 62%|██████▏   | 2256/3640 [51:50<31:39,  1.37s/it]
 62%|██████▏   | 2257/3640 [51:52<31:38,  1.37s/it]
 62%|██████▏   | 2258/3640 [51:53<31:36,  1.37s/it]
 62%|██████▏   | 2259/3640 [51:55<31:34,  1.37s/it]
 62%|██████▏   | 2260/3640 [51:56<31:31,  1.37s/it]
 62%|██████▏   | 2261/3640 [51:57<31:31,  1.37s/it]
 62%|██████▏   | 2262/3640 [51:59<32:57,  1.44s/it]
 62%|██████▏   | 2263/3640 [52:00<32:31,  1.42s/it]
 62%|██████▏   | 2264/3640 [52:02<32:08,  1.40s/it]
 62%|██████▏   | 2265/3640 [52:03<32:19,  1.41s/it]
 62%|██████▏   | 2266/3640 [52:04<32:00,  1.40s/it]
 62%|██████▏   | 2267/3640 [52:06<31:48,  1.39s/it]
 62%|██████▏   | 2268/3640 [52:07<31:37,  1.38s/it]
 62%|██████▏   | 2269/3640 [52:09<31:27,  1.38s/it]
 62%|██████▏   | 2270/3640 [52:10<31:22,  1.37s/it]
 62%|██████▏   | 2271/3640 [52:11<31:20,  1.37s/it]
 62%|██████▏   | 2272/3640 [52:13<31:15,  1.37s/it]
 62%|██████▏   | 2273/3640 [52:14<31:07,  1.37s/it]
 62%|██████▏   | 2274/3640 [52:15<31:07,  1.37s/it]
 62%|██████▎   | 2275/3640 [52:17<31:08,  1.37s/it]
                                                   

 62%|██████▎   | 2275/3640 [52:17<31:08,  1.37s/it]
 63%|██████▎   | 2276/3640 [52:18<31:12,  1.37s/it]
 63%|██████▎   | 2277/3640 [52:20<31:13,  1.37s/it]
 63%|██████▎   | 2278/3640 [52:21<31:10,  1.37s/it]
 63%|██████▎   | 2279/3640 [52:22<31:07,  1.37s/it]
 63%|██████▎   | 2280/3640 [52:24<31:04,  1.37s/it]
 63%|██████▎   | 2281/3640 [52:25<31:02,  1.37s/it]
 63%|██████▎   | 2282/3640 [52:26<31:00,  1.37s/it]
 63%|██████▎   | 2283/3640 [52:28<31:00,  1.37s/it]
 63%|██████▎   | 2284/3640 [52:29<30:52,  1.37s/it]
 63%|██████▎   | 2285/3640 [52:30<30:53,  1.37s/it]
 63%|██████▎   | 2286/3640 [52:32<30:54,  1.37s/it]
 63%|██████▎   | 2287/3640 [52:33<30:48,  1.37s/it]
 63%|██████▎   | 2288/3640 [52:35<30:49,  1.37s/it]
 63%|██████▎   | 2289/3640 [52:36<31:19,  1.39s/it]
 63%|██████▎   | 2290/3640 [52:37<31:07,  1.38s/it]
 63%|██████▎   | 2291/3640 [52:39<30:58,  1.38s/it]
 63%|██████▎   | 2292/3640 [52:40<30:54,  1.38s/it]
 63%|██████▎   | 2293/3640 [52:41<30:51,  1.37s/it]
 63%|██████▎   | 2294/3640 [52:43<30:48,  1.37s/it]
 63%|██████▎   | 2295/3640 [52:44<30:45,  1.37s/it]
 63%|██████▎   | 2296/3640 [52:46<30:43,  1.37s/it]
 63%|██████▎   | 2297/3640 [52:47<30:42,  1.37s/it]
 63%|██████▎   | 2298/3640 [52:48<30:38,  1.37s/it]
 63%|██████▎   | 2299/3640 [52:50<30:37,  1.37s/it]
 63%|██████▎   | 2300/3640 [52:51<30:35,  1.37s/it]
                                                   

 63%|██████▎   | 2300/3640 [52:51<30:35,  1.37s/it]
 63%|██████▎   | 2301/3640 [52:52<30:54,  1.38s/it]
 63%|██████▎   | 2302/3640 [52:54<30:44,  1.38s/it]
 63%|██████▎   | 2303/3640 [52:55<30:39,  1.38s/it]
 63%|██████▎   | 2304/3640 [52:57<30:30,  1.37s/it]
 63%|██████▎   | 2305/3640 [52:58<30:26,  1.37s/it]
 63%|██████▎   | 2306/3640 [52:59<30:23,  1.37s/it]
 63%|██████▎   | 2307/3640 [53:01<30:23,  1.37s/it]
 63%|██████▎   | 2308/3640 [53:02<30:24,  1.37s/it]
 63%|██████▎   | 2309/3640 [53:03<30:24,  1.37s/it]
 63%|██████▎   | 2310/3640 [53:05<30:25,  1.37s/it]
 63%|██████▎   | 2311/3640 [53:06<30:24,  1.37s/it]
 64%|██████▎   | 2312/3640 [53:08<30:18,  1.37s/it]
 64%|██████▎   | 2313/3640 [53:09<30:43,  1.39s/it]
 64%|██████▎   | 2314/3640 [53:10<30:34,  1.38s/it]
 64%|██████▎   | 2315/3640 [53:12<30:27,  1.38s/it]
 64%|██████▎   | 2316/3640 [53:13<30:22,  1.38s/it]
 64%|██████▎   | 2317/3640 [53:14<30:19,  1.38s/it]
 64%|██████▎   | 2318/3640 [53:16<30:17,  1.38s/it]
 64%|██████▎   | 2319/3640 [53:17<30:12,  1.37s/it]
 64%|██████▎   | 2320/3640 [53:19<30:10,  1.37s/it]
 64%|██████▍   | 2321/3640 [53:20<30:10,  1.37s/it]
 64%|██████▍   | 2322/3640 [53:21<30:12,  1.37s/it]
 64%|██████▍   | 2323/3640 [53:23<30:06,  1.37s/it]
 64%|██████▍   | 2324/3640 [53:24<30:08,  1.37s/it]
 64%|██████▍   | 2325/3640 [53:25<30:02,  1.37s/it]
                                                   

 64%|██████▍   | 2325/3640 [53:25<30:02,  1.37s/it]
 64%|██████▍   | 2326/3640 [53:27<30:17,  1.38s/it]
 64%|██████▍   | 2327/3640 [53:28<30:11,  1.38s/it]
 64%|██████▍   | 2328/3640 [53:30<30:07,  1.38s/it]
 64%|██████▍   | 2329/3640 [53:31<30:04,  1.38s/it]
 64%|██████▍   | 2330/3640 [53:32<29:58,  1.37s/it]
 64%|██████▍   | 2331/3640 [53:34<29:56,  1.37s/it]
 64%|██████▍   | 2332/3640 [53:35<29:59,  1.38s/it]
 64%|██████▍   | 2333/3640 [53:36<29:59,  1.38s/it]
 64%|██████▍   | 2334/3640 [53:38<29:58,  1.38s/it]
 64%|██████▍   | 2335/3640 [53:39<31:18,  1.44s/it]
 64%|██████▍   | 2336/3640 [53:41<31:00,  1.43s/it]
 64%|██████▍   | 2337/3640 [53:42<30:54,  1.42s/it]
 64%|██████▍   | 2338/3640 [53:44<30:30,  1.41s/it]
 64%|██████▍   | 2339/3640 [53:45<30:14,  1.39s/it]
 64%|██████▍   | 2340/3640 [53:46<30:03,  1.39s/it]
 64%|██████▍   | 2341/3640 [53:48<29:57,  1.38s/it]
 64%|██████▍   | 2342/3640 [53:49<29:48,  1.38s/it]
 64%|██████▍   | 2343/3640 [53:50<29:43,  1.38s/it]
 64%|██████▍   | 2344/3640 [53:52<29:43,  1.38s/it]
 64%|██████▍   | 2345/3640 [53:53<29:37,  1.37s/it]
 64%|██████▍   | 2346/3640 [53:55<29:32,  1.37s/it]
 64%|██████▍   | 2347/3640 [53:56<29:30,  1.37s/it]
 65%|██████▍   | 2348/3640 [53:57<29:28,  1.37s/it]
 65%|██████▍   | 2349/3640 [53:59<29:25,  1.37s/it]
 65%|██████▍   | 2350/3640 [54:00<29:28,  1.37s/it]
                                                   

 65%|██████▍   | 2350/3640 [54:00<29:28,  1.37s/it]
 65%|██████▍   | 2351/3640 [54:01<29:48,  1.39s/it]
 65%|██████▍   | 2352/3640 [54:03<29:40,  1.38s/it]
 65%|██████▍   | 2353/3640 [54:04<29:34,  1.38s/it]
 65%|██████▍   | 2354/3640 [54:06<29:29,  1.38s/it]
 65%|██████▍   | 2355/3640 [54:07<29:23,  1.37s/it]
 65%|██████▍   | 2356/3640 [54:08<29:20,  1.37s/it]
 65%|██████▍   | 2357/3640 [54:10<29:16,  1.37s/it]
 65%|██████▍   | 2358/3640 [54:11<29:15,  1.37s/it]
 65%|██████▍   | 2359/3640 [54:12<29:14,  1.37s/it]
 65%|██████▍   | 2360/3640 [54:14<29:32,  1.38s/it]
 65%|██████▍   | 2361/3640 [54:15<29:32,  1.39s/it]
 65%|██████▍   | 2362/3640 [54:17<29:25,  1.38s/it]
 65%|██████▍   | 2363/3640 [54:18<29:20,  1.38s/it]
 65%|██████▍   | 2364/3640 [54:19<29:13,  1.37s/it]
 65%|██████▍   | 2365/3640 [54:21<29:09,  1.37s/it]
 65%|██████▌   | 2366/3640 [54:22<29:05,  1.37s/it]
 65%|██████▌   | 2367/3640 [54:23<29:05,  1.37s/it]
 65%|██████▌   | 2368/3640 [54:25<29:01,  1.37s/it]
 65%|██████▌   | 2369/3640 [54:26<29:00,  1.37s/it]
 65%|██████▌   | 2370/3640 [54:28<29:00,  1.37s/it]
 65%|██████▌   | 2371/3640 [54:29<28:59,  1.37s/it]
 65%|██████▌   | 2372/3640 [54:30<28:59,  1.37s/it]
 65%|██████▌   | 2373/3640 [54:32<28:55,  1.37s/it]
 65%|██████▌   | 2374/3640 [54:33<28:55,  1.37s/it]
 65%|██████▌   | 2375/3640 [54:34<28:51,  1.37s/it]
                                                   

 65%|██████▌   | 2375/3640 [54:34<28:51,  1.37s/it]
 65%|██████▌   | 2376/3640 [54:36<29:05,  1.38s/it]
 65%|██████▌   | 2377/3640 [54:37<28:59,  1.38s/it]
 65%|██████▌   | 2378/3640 [54:39<28:56,  1.38s/it]
 65%|██████▌   | 2379/3640 [54:40<28:51,  1.37s/it]
 65%|██████▌   | 2380/3640 [54:41<28:46,  1.37s/it]
 65%|██████▌   | 2381/3640 [54:43<28:43,  1.37s/it]
 65%|██████▌   | 2382/3640 [54:44<28:40,  1.37s/it]
 65%|██████▌   | 2383/3640 [54:45<28:40,  1.37s/it]
 65%|██████▌   | 2384/3640 [54:47<29:02,  1.39s/it]
 66%|██████▌   | 2385/3640 [54:48<28:55,  1.38s/it]
 66%|██████▌   | 2386/3640 [54:50<28:49,  1.38s/it]
 66%|██████▌   | 2387/3640 [54:51<28:42,  1.37s/it]
 66%|██████▌   | 2388/3640 [54:52<28:37,  1.37s/it]
 66%|██████▌   | 2389/3640 [54:54<28:35,  1.37s/it]
 66%|██████▌   | 2390/3640 [54:55<28:32,  1.37s/it]
 66%|██████▌   | 2391/3640 [54:56<28:24,  1.36s/it]
 66%|██████▌   | 2392/3640 [54:58<28:24,  1.37s/it]
 66%|██████▌   | 2393/3640 [54:59<28:24,  1.37s/it]
 66%|██████▌   | 2394/3640 [55:00<28:27,  1.37s/it]
 66%|██████▌   | 2395/3640 [55:02<28:26,  1.37s/it]
 66%|██████▌   | 2396/3640 [55:03<28:24,  1.37s/it]
 66%|██████▌   | 2397/3640 [55:05<28:27,  1.37s/it]
 66%|██████▌   | 2398/3640 [55:06<28:25,  1.37s/it]
 66%|██████▌   | 2399/3640 [55:07<28:22,  1.37s/it]
 66%|██████▌   | 2400/3640 [55:09<28:21,  1.37s/it]
                                                   

 66%|██████▌   | 2400/3640 [55:09<28:21,  1.37s/it]
 66%|██████▌   | 2401/3640 [55:10<28:40,  1.39s/it]
 66%|██████▌   | 2402/3640 [55:12<28:32,  1.38s/it]
 66%|██████▌   | 2403/3640 [55:13<28:28,  1.38s/it]
 66%|██████▌   | 2404/3640 [55:14<28:24,  1.38s/it]
 66%|██████▌   | 2405/3640 [55:16<28:17,  1.37s/it]
 66%|██████▌   | 2406/3640 [55:17<28:11,  1.37s/it]
 66%|██████▌   | 2407/3640 [55:18<28:10,  1.37s/it]
 66%|██████▌   | 2408/3640 [55:20<28:36,  1.39s/it]
 66%|██████▌   | 2409/3640 [55:21<28:26,  1.39s/it]
 66%|██████▌   | 2410/3640 [55:23<28:20,  1.38s/it]
 66%|██████▌   | 2411/3640 [55:24<28:15,  1.38s/it]
 66%|██████▋   | 2412/3640 [55:25<28:10,  1.38s/it]
 66%|██████▋   | 2413/3640 [55:27<28:03,  1.37s/it]
 66%|██████▋   | 2414/3640 [55:28<28:00,  1.37s/it]
 66%|██████▋   | 2415/3640 [55:29<27:55,  1.37s/it]
 66%|██████▋   | 2416/3640 [55:31<27:58,  1.37s/it]
 66%|██████▋   | 2417/3640 [55:32<27:55,  1.37s/it]
 66%|██████▋   | 2418/3640 [55:33<27:55,  1.37s/it]
 66%|██████▋   | 2419/3640 [55:35<27:55,  1.37s/it]
 66%|██████▋   | 2420/3640 [55:36<27:50,  1.37s/it]
 67%|██████▋   | 2421/3640 [55:38<27:49,  1.37s/it]
 67%|██████▋   | 2422/3640 [55:39<27:47,  1.37s/it]
 67%|██████▋   | 2423/3640 [55:40<27:44,  1.37s/it]
 67%|██████▋   | 2424/3640 [55:42<27:38,  1.36s/it]
 67%|██████▋   | 2425/3640 [55:43<27:36,  1.36s/it]
                                                   

 67%|██████▋   | 2425/3640 [55:43<27:36,  1.36s/it]
 67%|██████▋   | 2426/3640 [55:44<27:56,  1.38s/it]
 67%|██████▋   | 2427/3640 [55:46<27:51,  1.38s/it]
 67%|██████▋   | 2428/3640 [55:47<27:47,  1.38s/it]
 67%|██████▋   | 2429/3640 [55:49<27:44,  1.37s/it]
 67%|██████▋   | 2430/3640 [55:50<27:43,  1.37s/it]
 67%|██████▋   | 2431/3640 [55:51<27:37,  1.37s/it]
 67%|██████▋   | 2432/3640 [55:53<27:56,  1.39s/it]
 67%|██████▋   | 2433/3640 [55:54<27:51,  1.38s/it]
 67%|██████▋   | 2434/3640 [55:56<27:49,  1.38s/it]
 67%|██████▋   | 2435/3640 [55:57<27:42,  1.38s/it]
 67%|██████▋   | 2436/3640 [55:58<27:37,  1.38s/it]
 67%|██████▋   | 2437/3640 [56:00<27:27,  1.37s/it]
 67%|██████▋   | 2438/3640 [56:01<27:27,  1.37s/it]
 67%|██████▋   | 2439/3640 [56:02<27:27,  1.37s/it]
 67%|██████▋   | 2440/3640 [56:04<27:23,  1.37s/it]
 67%|██████▋   | 2441/3640 [56:05<27:21,  1.37s/it]
 67%|██████▋   | 2442/3640 [56:06<27:18,  1.37s/it]
 67%|██████▋   | 2443/3640 [56:08<27:16,  1.37s/it]
 67%|██████▋   | 2444/3640 [56:09<27:16,  1.37s/it]
 67%|██████▋   | 2445/3640 [56:11<27:18,  1.37s/it]
 67%|██████▋   | 2446/3640 [56:12<27:17,  1.37s/it]
 67%|██████▋   | 2447/3640 [56:13<27:16,  1.37s/it]
 67%|██████▋   | 2448/3640 [56:15<27:14,  1.37s/it]
 67%|██████▋   | 2449/3640 [56:16<27:15,  1.37s/it]
 67%|██████▋   | 2450/3640 [56:17<27:12,  1.37s/it]
                                                   

 67%|██████▋   | 2450/3640 [56:17<27:12,  1.37s/it]
 67%|██████▋   | 2451/3640 [56:19<27:24,  1.38s/it]
 67%|██████▋   | 2452/3640 [56:20<27:18,  1.38s/it]
 67%|██████▋   | 2453/3640 [56:22<27:07,  1.37s/it]
 67%|██████▋   | 2454/3640 [56:23<27:07,  1.37s/it]
 67%|██████▋   | 2455/3640 [56:24<27:00,  1.37s/it]
 67%|██████▋   | 2456/3640 [56:26<28:26,  1.44s/it]
 68%|██████▊   | 2457/3640 [56:27<27:56,  1.42s/it]
 68%|██████▊   | 2458/3640 [56:29<27:36,  1.40s/it]
 68%|██████▊   | 2459/3640 [56:30<27:22,  1.39s/it]
 68%|██████▊   | 2460/3640 [56:31<27:13,  1.38s/it]
 68%|██████▊   | 2461/3640 [56:33<27:06,  1.38s/it]
 68%|██████▊   | 2462/3640 [56:34<27:00,  1.38s/it]
 68%|██████▊   | 2463/3640 [56:35<26:58,  1.38s/it]
 68%|██████▊   | 2464/3640 [56:37<26:53,  1.37s/it]
 68%|██████▊   | 2465/3640 [56:38<26:50,  1.37s/it]
 68%|██████▊   | 2466/3640 [56:40<27:05,  1.38s/it]
 68%|██████▊   | 2467/3640 [56:41<26:52,  1.38s/it]
 68%|██████▊   | 2468/3640 [56:42<26:47,  1.37s/it]
 68%|██████▊   | 2469/3640 [56:44<26:43,  1.37s/it]
 68%|██████▊   | 2470/3640 [56:45<26:46,  1.37s/it]
 68%|██████▊   | 2471/3640 [56:46<26:43,  1.37s/it]
 68%|██████▊   | 2472/3640 [56:48<27:34,  1.42s/it]
 68%|██████▊   | 2473/3640 [56:49<27:14,  1.40s/it]
 68%|██████▊   | 2474/3640 [56:51<27:02,  1.39s/it]
 68%|██████▊   | 2475/3640 [56:52<26:53,  1.39s/it]
                                                   

 68%|██████▊   | 2475/3640 [56:52<26:53,  1.39s/it]
 68%|██████▊   | 2476/3640 [56:53<26:48,  1.38s/it]
 68%|██████▊   | 2477/3640 [56:55<26:43,  1.38s/it]
 68%|██████▊   | 2478/3640 [56:56<26:39,  1.38s/it]
 68%|██████▊   | 2479/3640 [56:58<26:50,  1.39s/it]
 68%|██████▊   | 2480/3640 [56:59<26:51,  1.39s/it]
 68%|██████▊   | 2481/3640 [57:00<26:46,  1.39s/it]
 68%|██████▊   | 2482/3640 [57:02<26:40,  1.38s/it]
 68%|██████▊   | 2483/3640 [57:03<26:31,  1.38s/it]
 68%|██████▊   | 2484/3640 [57:04<26:28,  1.37s/it]
 68%|██████▊   | 2485/3640 [57:06<26:25,  1.37s/it]
 68%|██████▊   | 2486/3640 [57:07<26:23,  1.37s/it]
 68%|██████▊   | 2487/3640 [57:09<26:21,  1.37s/it]
 68%|██████▊   | 2488/3640 [57:10<26:43,  1.39s/it]
 68%|██████▊   | 2489/3640 [57:11<26:33,  1.38s/it]
 68%|██████▊   | 2490/3640 [57:13<26:27,  1.38s/it]
 68%|██████▊   | 2491/3640 [57:14<26:23,  1.38s/it]
 68%|██████▊   | 2492/3640 [57:16<26:22,  1.38s/it]
 68%|██████▊   | 2493/3640 [57:17<26:17,  1.38s/it]
 69%|██████▊   | 2494/3640 [57:18<26:14,  1.37s/it]
 69%|██████▊   | 2495/3640 [57:20<26:10,  1.37s/it]
 69%|██████▊   | 2496/3640 [57:21<26:11,  1.37s/it]
 69%|██████▊   | 2497/3640 [57:22<26:08,  1.37s/it]
 69%|██████▊   | 2498/3640 [57:24<26:06,  1.37s/it]
 69%|██████▊   | 2499/3640 [57:25<26:01,  1.37s/it]
 69%|██████▊   | 2500/3640 [57:26<26:01,  1.37s/it]
                                                   

 69%|██████▊   | 2500/3640 [57:26<26:01,  1.37s/it]
 69%|██████▊   | 2501/3640 [57:28<25:58,  1.37s/it]
 69%|██████▊   | 2502/3640 [57:29<25:55,  1.37s/it]
 69%|██████▉   | 2503/3640 [57:31<26:17,  1.39s/it]
 69%|██████▉   | 2504/3640 [57:32<26:09,  1.38s/it]
 69%|██████▉   | 2505/3640 [57:33<26:04,  1.38s/it]
 69%|██████▉   | 2506/3640 [57:35<26:00,  1.38s/it]
 69%|██████▉   | 2507/3640 [57:36<25:56,  1.37s/it]
 69%|██████▉   | 2508/3640 [57:38<25:56,  1.38s/it]
 69%|██████▉   | 2509/3640 [57:39<25:55,  1.37s/it]
 69%|██████▉   | 2510/3640 [57:40<25:54,  1.38s/it]
 69%|██████▉   | 2511/3640 [57:42<25:50,  1.37s/it]
 69%|██████▉   | 2512/3640 [57:43<25:48,  1.37s/it]
 69%|██████▉   | 2513/3640 [57:44<25:46,  1.37s/it]
 69%|██████▉   | 2514/3640 [57:46<25:47,  1.37s/it]
 69%|██████▉   | 2515/3640 [57:47<25:46,  1.37s/it]
 69%|██████▉   | 2516/3640 [57:48<25:45,  1.38s/it]
 69%|██████▉   | 2517/3640 [57:50<25:43,  1.37s/it]
 69%|██████▉   | 2518/3640 [57:51<25:42,  1.37s/it]
 69%|██████▉   | 2519/3640 [57:53<25:40,  1.37s/it]
 69%|██████▉   | 2520/3640 [57:54<25:37,  1.37s/it]
 69%|██████▉   | 2521/3640 [57:55<25:37,  1.37s/it]
 69%|██████▉   | 2522/3640 [57:57<25:34,  1.37s/it]
 69%|██████▉   | 2523/3640 [57:58<25:27,  1.37s/it]
 69%|██████▉   | 2524/3640 [57:59<25:25,  1.37s/it]
 69%|██████▉   | 2525/3640 [58:01<25:23,  1.37s/it]
                                                   

 69%|██████▉   | 2525/3640 [58:01<25:23,  1.37s/it]
 69%|██████▉   | 2526/3640 [58:02<25:23,  1.37s/it]
 69%|██████▉   | 2527/3640 [58:04<25:47,  1.39s/it]
 69%|██████▉   | 2528/3640 [58:05<26:50,  1.45s/it]
 69%|██████▉   | 2529/3640 [58:07<26:24,  1.43s/it]
 70%|██████▉   | 2530/3640 [58:08<26:04,  1.41s/it]
 70%|██████▉   | 2531/3640 [58:09<25:47,  1.40s/it]
 70%|██████▉   | 2532/3640 [58:11<25:38,  1.39s/it]
 70%|██████▉   | 2533/3640 [58:12<25:28,  1.38s/it]
 70%|██████▉   | 2534/3640 [58:13<25:22,  1.38s/it]
 70%|██████▉   | 2535/3640 [58:15<25:19,  1.38s/it]
 70%|██████▉   | 2536/3640 [58:16<25:16,  1.37s/it]
 70%|██████▉   | 2537/3640 [58:18<25:15,  1.37s/it]
 70%|██████▉   | 2538/3640 [58:19<25:14,  1.37s/it]
 70%|██████▉   | 2539/3640 [58:20<25:15,  1.38s/it]
 70%|██████▉   | 2540/3640 [58:22<25:11,  1.37s/it]
 70%|██████▉   | 2541/3640 [58:23<25:08,  1.37s/it]
 70%|██████▉   | 2542/3640 [58:24<25:02,  1.37s/it]
 70%|██████▉   | 2543/3640 [58:26<25:01,  1.37s/it]
 70%|██████▉   | 2544/3640 [58:27<25:00,  1.37s/it]
 70%|██████▉   | 2545/3640 [58:29<24:57,  1.37s/it]
 70%|██████▉   | 2546/3640 [58:30<24:59,  1.37s/it]
 70%|██████▉   | 2547/3640 [58:31<24:57,  1.37s/it]
 70%|███████   | 2548/3640 [58:33<24:55,  1.37s/it]
 70%|███████   | 2549/3640 [58:34<24:52,  1.37s/it]
 70%|███████   | 2550/3640 [58:35<24:54,  1.37s/it]
                                                   

 70%|███████   | 2550/3640 [58:35<24:54,  1.37s/it]
 70%|███████   | 2551/3640 [58:37<25:16,  1.39s/it]
 70%|███████   | 2552/3640 [58:38<25:10,  1.39s/it]
 70%|███████   | 2553/3640 [58:40<25:00,  1.38s/it]
 70%|███████   | 2554/3640 [58:41<24:56,  1.38s/it]
 70%|███████   | 2555/3640 [58:42<24:51,  1.37s/it]
 70%|███████   | 2556/3640 [58:44<24:46,  1.37s/it]
 70%|███████   | 2557/3640 [58:45<24:42,  1.37s/it]
 70%|███████   | 2558/3640 [58:46<24:40,  1.37s/it]
 70%|███████   | 2559/3640 [58:48<24:40,  1.37s/it]
 70%|███████   | 2560/3640 [58:49<24:39,  1.37s/it]
 70%|███████   | 2561/3640 [58:50<24:34,  1.37s/it]
 70%|███████   | 2562/3640 [58:52<24:35,  1.37s/it]
 70%|███████   | 2563/3640 [58:53<24:33,  1.37s/it]
 70%|███████   | 2564/3640 [58:55<24:30,  1.37s/it]
 70%|███████   | 2565/3640 [58:56<24:31,  1.37s/it]
 70%|███████   | 2566/3640 [58:57<24:28,  1.37s/it]
 71%|███████   | 2567/3640 [58:59<24:29,  1.37s/it]
 71%|███████   | 2568/3640 [59:00<25:34,  1.43s/it]
 71%|███████   | 2569/3640 [59:02<25:13,  1.41s/it]
 71%|███████   | 2570/3640 [59:03<24:59,  1.40s/it]
 71%|███████   | 2571/3640 [59:04<24:46,  1.39s/it]
 71%|███████   | 2572/3640 [59:06<24:39,  1.39s/it]
 71%|███████   | 2573/3640 [59:07<24:30,  1.38s/it]
 71%|███████   | 2574/3640 [59:09<24:33,  1.38s/it]
 71%|███████   | 2575/3640 [59:10<24:38,  1.39s/it]
                                                   

 71%|███████   | 2575/3640 [59:10<24:38,  1.39s/it]
 71%|███████   | 2576/3640 [59:11<24:33,  1.39s/it]
 71%|███████   | 2577/3640 [59:13<24:25,  1.38s/it]
 71%|███████   | 2578/3640 [59:14<24:19,  1.37s/it]
 71%|███████   | 2579/3640 [59:15<24:21,  1.38s/it]
 71%|███████   | 2580/3640 [59:17<24:17,  1.37s/it]
 71%|███████   | 2581/3640 [59:18<24:15,  1.37s/it]
 71%|███████   | 2582/3640 [59:20<24:14,  1.37s/it]
 71%|███████   | 2583/3640 [59:21<24:07,  1.37s/it]
 71%|███████   | 2584/3640 [59:22<24:05,  1.37s/it]
 71%|███████   | 2585/3640 [59:24<24:04,  1.37s/it]
 71%|███████   | 2586/3640 [59:25<24:05,  1.37s/it]
 71%|███████   | 2587/3640 [59:26<24:04,  1.37s/it]
 71%|███████   | 2588/3640 [59:28<24:02,  1.37s/it]
 71%|███████   | 2589/3640 [59:29<24:03,  1.37s/it]
 71%|███████   | 2590/3640 [59:30<23:58,  1.37s/it]
 71%|███████   | 2591/3640 [59:32<23:58,  1.37s/it]
 71%|███████   | 2592/3640 [59:33<23:56,  1.37s/it]
 71%|███████   | 2593/3640 [59:35<23:54,  1.37s/it]
 71%|███████▏  | 2594/3640 [59:36<23:57,  1.37s/it]
 71%|███████▏  | 2595/3640 [59:37<23:54,  1.37s/it]
 71%|███████▏  | 2596/3640 [59:39<23:51,  1.37s/it]
 71%|███████▏  | 2597/3640 [59:40<23:53,  1.37s/it]
 71%|███████▏  | 2598/3640 [59:41<24:04,  1.39s/it]
 71%|███████▏  | 2599/3640 [59:43<24:03,  1.39s/it]
 71%|███████▏  | 2600/3640 [59:44<23:57,  1.38s/it]
                                                   

 71%|███████▏  | 2600/3640 [59:44<23:57,  1.38s/it]
 71%|███████▏  | 2601/3640 [59:46<23:52,  1.38s/it]
 71%|███████▏  | 2602/3640 [59:47<23:45,  1.37s/it]
 72%|███████▏  | 2603/3640 [59:48<23:40,  1.37s/it]
 72%|███████▏  | 2604/3640 [59:50<23:39,  1.37s/it]
 72%|███████▏  | 2605/3640 [59:51<23:39,  1.37s/it]
 72%|███████▏  | 2606/3640 [59:52<23:38,  1.37s/it]
 72%|███████▏  | 2607/3640 [59:54<23:36,  1.37s/it]
 72%|███████▏  | 2608/3640 [59:55<23:37,  1.37s/it]
 72%|███████▏  | 2609/3640 [59:57<23:36,  1.37s/it]
 72%|███████▏  | 2610/3640 [59:58<23:33,  1.37s/it]
 72%|███████▏  | 2611/3640 [59:59<23:23,  1.36s/it]
 72%|███████▏  | 2612/3640 [1:00:01<23:27,  1.37s/it]
 72%|███████▏  | 2613/3640 [1:00:02<23:28,  1.37s/it]
 72%|███████▏  | 2614/3640 [1:00:03<23:26,  1.37s/it]
 72%|███████▏  | 2615/3640 [1:00:05<23:26,  1.37s/it]
 72%|███████▏  | 2616/3640 [1:00:06<23:25,  1.37s/it]
 72%|███████▏  | 2617/3640 [1:00:08<23:41,  1.39s/it]
 72%|███████▏  | 2618/3640 [1:00:09<23:26,  1.38s/it]
 72%|███████▏  | 2619/3640 [1:00:11<24:46,  1.46s/it]
 72%|███████▏  | 2620/3640 [1:00:12<24:37,  1.45s/it]
 72%|███████▏  | 2621/3640 [1:00:13<24:08,  1.42s/it]
 72%|███████▏  | 2622/3640 [1:00:15<24:13,  1.43s/it]
 72%|███████▏  | 2623/3640 [1:00:16<23:50,  1.41s/it]
 72%|███████▏  | 2624/3640 [1:00:18<23:36,  1.39s/it]
 72%|███████▏  | 2625/3640 [1:00:19<23:17,  1.38s/it]
                                                     

 72%|███████▏  | 2625/3640 [1:00:19<23:17,  1.38s/it]
 72%|███████▏  | 2626/3640 [1:00:20<23:10,  1.37s/it]
 72%|███████▏  | 2627/3640 [1:00:22<23:06,  1.37s/it]
 72%|███████▏  | 2628/3640 [1:00:23<23:03,  1.37s/it]
 72%|███████▏  | 2629/3640 [1:00:24<23:02,  1.37s/it]
 72%|███████▏  | 2630/3640 [1:00:26<22:59,  1.37s/it]
 72%|███████▏  | 2631/3640 [1:00:27<22:58,  1.37s/it]
 72%|███████▏  | 2632/3640 [1:00:28<22:58,  1.37s/it]
 72%|███████▏  | 2633/3640 [1:00:30<22:57,  1.37s/it]
 72%|███████▏  | 2634/3640 [1:00:31<23:57,  1.43s/it]
 72%|███████▏  | 2635/3640 [1:00:33<23:40,  1.41s/it]
 72%|███████▏  | 2636/3640 [1:00:34<23:23,  1.40s/it]
 72%|███████▏  | 2637/3640 [1:00:36<23:19,  1.40s/it]
 72%|███████▏  | 2638/3640 [1:00:37<23:10,  1.39s/it]
 72%|███████▎  | 2639/3640 [1:00:38<23:05,  1.38s/it]
 73%|███████▎  | 2640/3640 [1:00:40<23:02,  1.38s/it]
 73%|███████▎  | 2641/3640 [1:00:41<22:58,  1.38s/it]
 73%|███████▎  | 2642/3640 [1:00:42<22:55,  1.38s/it]
 73%|███████▎  | 2643/3640 [1:00:44<22:48,  1.37s/it]
 73%|███████▎  | 2644/3640 [1:00:45<22:46,  1.37s/it]
 73%|███████▎  | 2645/3640 [1:00:47<23:43,  1.43s/it]
 73%|███████▎  | 2646/3640 [1:00:48<23:41,  1.43s/it]
 73%|███████▎  | 2647/3640 [1:00:49<23:21,  1.41s/it]
 73%|███████▎  | 2648/3640 [1:00:51<23:08,  1.40s/it]
 73%|███████▎  | 2649/3640 [1:00:52<22:56,  1.39s/it]
 73%|███████▎  | 2650/3640 [1:00:54<22:49,  1.38s/it]
                                                     

 73%|███████▎  | 2650/3640 [1:00:54<22:49,  1.38s/it]
 73%|███████▎  | 2651/3640 [1:00:55<23:23,  1.42s/it]
 73%|███████▎  | 2652/3640 [1:00:56<23:02,  1.40s/it]
 73%|███████▎  | 2653/3640 [1:00:58<22:52,  1.39s/it]
 73%|███████▎  | 2654/3640 [1:00:59<22:45,  1.38s/it]
 73%|███████▎  | 2655/3640 [1:01:01<22:41,  1.38s/it]
 73%|███████▎  | 2656/3640 [1:01:02<22:36,  1.38s/it]
 73%|███████▎  | 2657/3640 [1:01:03<22:31,  1.37s/it]
 73%|███████▎  | 2658/3640 [1:01:05<22:27,  1.37s/it]
 73%|███████▎  | 2659/3640 [1:01:06<22:27,  1.37s/it]
 73%|███████▎  | 2660/3640 [1:01:07<22:23,  1.37s/it]
 73%|███████▎  | 2661/3640 [1:01:09<22:22,  1.37s/it]
 73%|███████▎  | 2662/3640 [1:01:10<22:15,  1.37s/it]
 73%|███████▎  | 2663/3640 [1:01:11<22:16,  1.37s/it]
 73%|███████▎  | 2664/3640 [1:01:13<22:13,  1.37s/it]
 73%|███████▎  | 2665/3640 [1:01:14<22:13,  1.37s/it]
 73%|███████▎  | 2666/3640 [1:01:16<22:11,  1.37s/it]
 73%|███████▎  | 2667/3640 [1:01:17<22:09,  1.37s/it]
 73%|███████▎  | 2668/3640 [1:01:18<22:09,  1.37s/it]
 73%|███████▎  | 2669/3640 [1:01:20<22:21,  1.38s/it]
 73%|███████▎  | 2670/3640 [1:01:21<22:25,  1.39s/it]
 73%|███████▎  | 2671/3640 [1:01:23<22:18,  1.38s/it]
 73%|███████▎  | 2672/3640 [1:01:24<22:14,  1.38s/it]
 73%|███████▎  | 2673/3640 [1:01:25<22:10,  1.38s/it]
 73%|███████▎  | 2674/3640 [1:01:27<22:05,  1.37s/it]
 73%|███████▎  | 2675/3640 [1:01:28<22:00,  1.37s/it]
                                                     

 73%|███████▎  | 2675/3640 [1:01:28<22:00,  1.37s/it]
 74%|███████▎  | 2676/3640 [1:01:29<21:58,  1.37s/it]
 74%|███████▎  | 2677/3640 [1:01:31<22:00,  1.37s/it]
 74%|███████▎  | 2678/3640 [1:01:32<21:58,  1.37s/it]
 74%|███████▎  | 2679/3640 [1:01:33<21:55,  1.37s/it]
 74%|███████▎  | 2680/3640 [1:01:35<21:49,  1.36s/it]
 74%|███████▎  | 2681/3640 [1:01:36<21:48,  1.36s/it]
 74%|███████▎  | 2682/3640 [1:01:38<21:50,  1.37s/it]
 74%|███████▎  | 2683/3640 [1:01:39<21:48,  1.37s/it]
 74%|███████▎  | 2684/3640 [1:01:40<21:49,  1.37s/it]
 74%|███████▍  | 2685/3640 [1:01:42<21:57,  1.38s/it]
 74%|███████▍  | 2686/3640 [1:01:43<21:53,  1.38s/it]
 74%|███████▍  | 2687/3640 [1:01:44<21:47,  1.37s/it]
 74%|███████▍  | 2688/3640 [1:01:46<21:43,  1.37s/it]
 74%|███████▍  | 2689/3640 [1:01:47<21:42,  1.37s/it]
 74%|███████▍  | 2690/3640 [1:01:49<21:39,  1.37s/it]
 74%|███████▍  | 2691/3640 [1:01:50<21:40,  1.37s/it]
 74%|███████▍  | 2692/3640 [1:01:51<21:40,  1.37s/it]
 74%|███████▍  | 2693/3640 [1:01:53<21:56,  1.39s/it]
 74%|███████▍  | 2694/3640 [1:01:54<21:47,  1.38s/it]
 74%|███████▍  | 2695/3640 [1:01:55<21:42,  1.38s/it]
 74%|███████▍  | 2696/3640 [1:01:57<21:39,  1.38s/it]
 74%|███████▍  | 2697/3640 [1:01:58<21:36,  1.37s/it]
 74%|███████▍  | 2698/3640 [1:02:00<21:33,  1.37s/it]
 74%|███████▍  | 2699/3640 [1:02:01<21:29,  1.37s/it]
 74%|███████▍  | 2700/3640 [1:02:02<21:27,  1.37s/it]
                                                     

 74%|███████▍  | 2700/3640 [1:02:02<21:27,  1.37s/it]
 74%|███████▍  | 2701/3640 [1:02:04<21:24,  1.37s/it]
 74%|███████▍  | 2702/3640 [1:02:05<21:30,  1.38s/it]
 74%|███████▍  | 2703/3640 [1:02:06<21:25,  1.37s/it]
 74%|███████▍  | 2704/3640 [1:02:08<21:23,  1.37s/it]
 74%|███████▍  | 2705/3640 [1:02:09<21:22,  1.37s/it]
 74%|███████▍  | 2706/3640 [1:02:11<21:17,  1.37s/it]
 74%|███████▍  | 2707/3640 [1:02:12<21:18,  1.37s/it]
 74%|███████▍  | 2708/3640 [1:02:13<22:15,  1.43s/it]
 74%|███████▍  | 2709/3640 [1:02:15<21:57,  1.42s/it]
 74%|███████▍  | 2710/3640 [1:02:16<21:43,  1.40s/it]
 74%|███████▍  | 2711/3640 [1:02:18<21:32,  1.39s/it]
 75%|███████▍  | 2712/3640 [1:02:19<21:23,  1.38s/it]
 75%|███████▍  | 2713/3640 [1:02:20<21:16,  1.38s/it]
 75%|███████▍  | 2714/3640 [1:02:22<21:10,  1.37s/it]
 75%|███████▍  | 2715/3640 [1:02:23<21:08,  1.37s/it]
 75%|███████▍  | 2716/3640 [1:02:24<21:04,  1.37s/it]
 75%|███████▍  | 2717/3640 [1:02:26<21:26,  1.39s/it]
 75%|███████▍  | 2718/3640 [1:02:27<21:16,  1.38s/it]
 75%|███████▍  | 2719/3640 [1:02:29<21:04,  1.37s/it]
 75%|███████▍  | 2720/3640 [1:02:30<21:06,  1.38s/it]
 75%|███████▍  | 2721/3640 [1:02:31<21:03,  1.37s/it]
 75%|███████▍  | 2722/3640 [1:02:33<20:58,  1.37s/it]
 75%|███████▍  | 2723/3640 [1:02:34<20:58,  1.37s/it]
 75%|███████▍  | 2724/3640 [1:02:35<20:56,  1.37s/it]
 75%|███████▍  | 2725/3640 [1:02:37<20:54,  1.37s/it]
                                                     

 75%|███████▍  | 2725/3640 [1:02:37<20:54,  1.37s/it]
 75%|███████▍  | 2726/3640 [1:02:38<20:52,  1.37s/it]
 75%|███████▍  | 2727/3640 [1:02:40<20:49,  1.37s/it]
 75%|███████▍  | 2728/3640 [1:02:41<20:47,  1.37s/it]
 75%|███████▍  | 2729/3640 [1:02:42<20:41,  1.36s/it]
 75%|███████▌  | 2730/3640 [1:02:44<20:41,  1.36s/it]
 75%|███████▌  | 2731/3640 [1:02:45<20:42,  1.37s/it]
 75%|███████▌  | 2732/3640 [1:02:46<20:33,  1.36s/it]
 75%|███████▌  | 2733/3640 [1:02:48<20:34,  1.36s/it]
 75%|███████▌  | 2734/3640 [1:02:49<20:34,  1.36s/it]
 75%|███████▌  | 2735/3640 [1:02:50<20:33,  1.36s/it]
 75%|███████▌  | 2736/3640 [1:02:52<20:34,  1.37s/it]
 75%|███████▌  | 2737/3640 [1:02:53<20:34,  1.37s/it]
 75%|███████▌  | 2738/3640 [1:02:55<20:30,  1.36s/it]
 75%|███████▌  | 2739/3640 [1:02:56<20:31,  1.37s/it]
 75%|███████▌  | 2740/3640 [1:02:57<20:30,  1.37s/it]
 75%|███████▌  | 2741/3640 [1:02:59<20:36,  1.38s/it]
 75%|███████▌  | 2742/3640 [1:03:00<20:32,  1.37s/it]
 75%|███████▌  | 2743/3640 [1:03:01<20:27,  1.37s/it]
 75%|███████▌  | 2744/3640 [1:03:03<20:25,  1.37s/it]
 75%|███████▌  | 2745/3640 [1:03:04<20:24,  1.37s/it]
 75%|███████▌  | 2746/3640 [1:03:05<20:25,  1.37s/it]
 75%|███████▌  | 2747/3640 [1:03:07<20:23,  1.37s/it]
 75%|███████▌  | 2748/3640 [1:03:08<20:22,  1.37s/it]
 76%|███████▌  | 2749/3640 [1:03:10<20:21,  1.37s/it]
 76%|███████▌  | 2750/3640 [1:03:11<20:20,  1.37s/it]
                                                     

 76%|███████▌  | 2750/3640 [1:03:11<20:20,  1.37s/it]
 76%|███████▌  | 2751/3640 [1:03:12<20:17,  1.37s/it]
 76%|███████▌  | 2752/3640 [1:03:14<20:16,  1.37s/it]
 76%|███████▌  | 2753/3640 [1:03:15<20:18,  1.37s/it]
 76%|███████▌  | 2754/3640 [1:03:16<20:15,  1.37s/it]
 76%|███████▌  | 2755/3640 [1:03:18<20:13,  1.37s/it]
 76%|███████▌  | 2756/3640 [1:03:19<20:11,  1.37s/it]
 76%|███████▌  | 2757/3640 [1:03:21<20:11,  1.37s/it]
 76%|███████▌  | 2758/3640 [1:03:22<20:07,  1.37s/it]
 76%|███████▌  | 2759/3640 [1:03:23<20:05,  1.37s/it]
 76%|███████▌  | 2760/3640 [1:03:25<20:05,  1.37s/it]
 76%|███████▌  | 2761/3640 [1:03:26<20:02,  1.37s/it]
 76%|███████▌  | 2762/3640 [1:03:27<20:02,  1.37s/it]
 76%|███████▌  | 2763/3640 [1:03:29<19:59,  1.37s/it]
 76%|███████▌  | 2764/3640 [1:03:30<19:59,  1.37s/it]
 76%|███████▌  | 2765/3640 [1:03:32<20:05,  1.38s/it]
 76%|███████▌  | 2766/3640 [1:03:33<19:56,  1.37s/it]
 76%|███████▌  | 2767/3640 [1:03:34<19:50,  1.36s/it]
 76%|███████▌  | 2768/3640 [1:03:36<19:53,  1.37s/it]
 76%|███████▌  | 2769/3640 [1:03:37<19:54,  1.37s/it]
 76%|███████▌  | 2770/3640 [1:03:38<19:51,  1.37s/it]
 76%|███████▌  | 2771/3640 [1:03:40<19:50,  1.37s/it]
 76%|███████▌  | 2772/3640 [1:03:41<19:50,  1.37s/it]
 76%|███████▌  | 2773/3640 [1:03:42<19:48,  1.37s/it]
 76%|███████▌  | 2774/3640 [1:03:44<19:48,  1.37s/it]
 76%|███████▌  | 2775/3640 [1:03:45<19:46,  1.37s/it]
                                                     

 76%|███████▌  | 2775/3640 [1:03:45<19:46,  1.37s/it]
 76%|███████▋  | 2776/3640 [1:03:47<19:42,  1.37s/it]
 76%|███████▋  | 2777/3640 [1:03:48<19:41,  1.37s/it]
 76%|███████▋  | 2778/3640 [1:03:49<19:36,  1.36s/it]
 76%|███████▋  | 2779/3640 [1:03:51<19:33,  1.36s/it]
 76%|███████▋  | 2780/3640 [1:03:52<19:25,  1.36s/it]
 76%|███████▋  | 2781/3640 [1:03:53<19:28,  1.36s/it]
 76%|███████▋  | 2782/3640 [1:03:55<19:29,  1.36s/it]
 76%|███████▋  | 2783/3640 [1:03:56<19:28,  1.36s/it]
 76%|███████▋  | 2784/3640 [1:03:57<19:28,  1.37s/it]
 77%|███████▋  | 2785/3640 [1:03:59<19:27,  1.37s/it]
 77%|███████▋  | 2786/3640 [1:04:00<19:28,  1.37s/it]
 77%|███████▋  | 2787/3640 [1:04:02<19:25,  1.37s/it]
 77%|███████▋  | 2788/3640 [1:04:03<19:23,  1.37s/it]
 77%|███████▋  | 2789/3640 [1:04:04<19:29,  1.37s/it]
 77%|███████▋  | 2790/3640 [1:04:06<19:28,  1.37s/it]
 77%|███████▋  | 2791/3640 [1:04:07<19:25,  1.37s/it]
 77%|███████▋  | 2792/3640 [1:04:08<19:21,  1.37s/it]
 77%|███████▋  | 2793/3640 [1:04:10<19:20,  1.37s/it]
 77%|███████▋  | 2794/3640 [1:04:11<19:18,  1.37s/it]
 77%|███████▋  | 2795/3640 [1:04:13<19:17,  1.37s/it]
 77%|███████▋  | 2796/3640 [1:04:14<19:16,  1.37s/it]
 77%|███████▋  | 2797/3640 [1:04:15<19:17,  1.37s/it]
 77%|███████▋  | 2798/3640 [1:04:17<19:14,  1.37s/it]
 77%|███████▋  | 2799/3640 [1:04:18<19:12,  1.37s/it]
 77%|███████▋  | 2800/3640 [1:04:19<19:11,  1.37s/it]
                                                     

 77%|███████▋  | 2800/3640 [1:04:19<19:11,  1.37s/it]
 77%|███████▋  | 2801/3640 [1:04:21<19:10,  1.37s/it]
 77%|███████▋  | 2802/3640 [1:04:22<19:08,  1.37s/it]
 77%|███████▋  | 2803/3640 [1:04:24<19:07,  1.37s/it]
 77%|███████▋  | 2804/3640 [1:04:25<19:04,  1.37s/it]
 77%|███████▋  | 2805/3640 [1:04:26<19:03,  1.37s/it]
 77%|███████▋  | 2806/3640 [1:04:28<18:59,  1.37s/it]
 77%|███████▋  | 2807/3640 [1:04:29<18:58,  1.37s/it]
 77%|███████▋  | 2808/3640 [1:04:30<19:01,  1.37s/it]
 77%|███████▋  | 2809/3640 [1:04:32<19:06,  1.38s/it]
 77%|███████▋  | 2810/3640 [1:04:33<18:59,  1.37s/it]
 77%|███████▋  | 2811/3640 [1:04:34<18:56,  1.37s/it]
 77%|███████▋  | 2812/3640 [1:04:36<18:55,  1.37s/it]
 77%|███████▋  | 2813/3640 [1:04:37<18:59,  1.38s/it]
 77%|███████▋  | 2814/3640 [1:04:39<19:08,  1.39s/it]
 77%|███████▋  | 2815/3640 [1:04:40<19:37,  1.43s/it]
 77%|███████▋  | 2816/3640 [1:04:42<19:18,  1.41s/it]
 77%|███████▋  | 2817/3640 [1:04:43<19:08,  1.40s/it]
 77%|███████▋  | 2818/3640 [1:04:44<18:58,  1.38s/it]
 77%|███████▋  | 2819/3640 [1:04:46<18:54,  1.38s/it]
 77%|███████▋  | 2820/3640 [1:04:47<18:48,  1.38s/it]
 78%|███████▊  | 2821/3640 [1:04:48<18:45,  1.37s/it]
 78%|███████▊  | 2822/3640 [1:04:50<18:40,  1.37s/it]
 78%|███████▊  | 2823/3640 [1:04:51<19:10,  1.41s/it]
 78%|███████▊  | 2824/3640 [1:04:53<19:02,  1.40s/it]
 78%|███████▊  | 2825/3640 [1:04:54<18:59,  1.40s/it]
                                                     

 78%|███████▊  | 2825/3640 [1:04:54<18:59,  1.40s/it]
 78%|███████▊  | 2826/3640 [1:04:55<18:58,  1.40s/it]
 78%|███████▊  | 2827/3640 [1:04:57<18:50,  1.39s/it]
 78%|███████▊  | 2828/3640 [1:04:58<18:51,  1.39s/it]
 78%|███████▊  | 2829/3640 [1:05:00<18:47,  1.39s/it]
 78%|███████▊  | 2830/3640 [1:05:01<18:46,  1.39s/it]
 78%|███████▊  | 2831/3640 [1:05:02<18:43,  1.39s/it]
 78%|███████▊  | 2832/3640 [1:05:04<18:43,  1.39s/it]
 78%|███████▊  | 2833/3640 [1:05:05<18:41,  1.39s/it]
 78%|███████▊  | 2834/3640 [1:05:07<18:37,  1.39s/it]
 78%|███████▊  | 2835/3640 [1:05:08<18:37,  1.39s/it]
 78%|███████▊  | 2836/3640 [1:05:09<18:42,  1.40s/it]
 78%|███████▊  | 2837/3640 [1:05:11<19:35,  1.46s/it]
 78%|███████▊  | 2838/3640 [1:05:12<19:15,  1.44s/it]
 78%|███████▊  | 2839/3640 [1:05:14<19:00,  1.42s/it]
 78%|███████▊  | 2840/3640 [1:05:15<18:50,  1.41s/it]
 78%|███████▊  | 2841/3640 [1:05:16<18:41,  1.40s/it]
 78%|███████▊  | 2842/3640 [1:05:18<18:37,  1.40s/it]
 78%|███████▊  | 2843/3640 [1:05:19<18:29,  1.39s/it]
 78%|███████▊  | 2844/3640 [1:05:21<18:26,  1.39s/it]
 78%|███████▊  | 2845/3640 [1:05:22<18:23,  1.39s/it]
 78%|███████▊  | 2846/3640 [1:05:23<18:19,  1.39s/it]
 78%|███████▊  | 2847/3640 [1:05:25<18:18,  1.39s/it]
 78%|███████▊  | 2848/3640 [1:05:26<18:16,  1.38s/it]
 78%|███████▊  | 2849/3640 [1:05:28<18:12,  1.38s/it]
 78%|███████▊  | 2850/3640 [1:05:29<18:07,  1.38s/it]
                                                     

 78%|███████▊  | 2850/3640 [1:05:29<18:07,  1.38s/it]
 78%|███████▊  | 2851/3640 [1:05:30<18:05,  1.38s/it]
 78%|███████▊  | 2852/3640 [1:05:32<18:00,  1.37s/it]
 78%|███████▊  | 2853/3640 [1:05:33<18:00,  1.37s/it]
 78%|███████▊  | 2854/3640 [1:05:34<17:58,  1.37s/it]
 78%|███████▊  | 2855/3640 [1:05:36<18:12,  1.39s/it]
 78%|███████▊  | 2856/3640 [1:05:37<18:03,  1.38s/it]
 78%|███████▊  | 2857/3640 [1:05:39<17:59,  1.38s/it]
 79%|███████▊  | 2858/3640 [1:05:40<17:55,  1.38s/it]
 79%|███████▊  | 2859/3640 [1:05:41<17:51,  1.37s/it]
 79%|███████▊  | 2860/3640 [1:05:43<17:56,  1.38s/it]
 79%|███████▊  | 2861/3640 [1:05:44<17:52,  1.38s/it]
 79%|███████▊  | 2862/3640 [1:05:45<17:50,  1.38s/it]
 79%|███████▊  | 2863/3640 [1:05:47<17:48,  1.38s/it]
 79%|███████▊  | 2864/3640 [1:05:48<17:45,  1.37s/it]
 79%|███████▊  | 2865/3640 [1:05:50<17:42,  1.37s/it]
 79%|███████▊  | 2866/3640 [1:05:51<17:42,  1.37s/it]
 79%|███████▉  | 2867/3640 [1:05:52<17:35,  1.37s/it]
 79%|███████▉  | 2868/3640 [1:05:54<17:35,  1.37s/it]
 79%|███████▉  | 2869/3640 [1:05:55<17:34,  1.37s/it]
 79%|███████▉  | 2870/3640 [1:05:56<17:33,  1.37s/it]
 79%|███████▉  | 2871/3640 [1:05:58<17:32,  1.37s/it]
 79%|███████▉  | 2872/3640 [1:05:59<17:30,  1.37s/it]
 79%|███████▉  | 2873/3640 [1:06:00<17:32,  1.37s/it]
 79%|███████▉  | 2874/3640 [1:06:02<17:30,  1.37s/it]
 79%|███████▉  | 2875/3640 [1:06:03<17:27,  1.37s/it]
                                                     

 79%|███████▉  | 2875/3640 [1:06:03<17:27,  1.37s/it]
 79%|███████▉  | 2876/3640 [1:06:05<17:27,  1.37s/it]
 79%|███████▉  | 2877/3640 [1:06:06<17:27,  1.37s/it]
 79%|███████▉  | 2878/3640 [1:06:07<17:25,  1.37s/it]
 79%|███████▉  | 2879/3640 [1:06:09<17:23,  1.37s/it]
 79%|███████▉  | 2880/3640 [1:06:10<17:23,  1.37s/it]
 79%|███████▉  | 2881/3640 [1:06:11<17:20,  1.37s/it]
 79%|███████▉  | 2882/3640 [1:06:13<17:16,  1.37s/it]
 79%|███████▉  | 2883/3640 [1:06:14<17:14,  1.37s/it]
 79%|███████▉  | 2884/3640 [1:06:16<17:18,  1.37s/it]
 79%|███████▉  | 2885/3640 [1:06:17<17:15,  1.37s/it]
 79%|███████▉  | 2886/3640 [1:06:18<17:11,  1.37s/it]
 79%|███████▉  | 2887/3640 [1:06:20<17:11,  1.37s/it]
 79%|███████▉  | 2888/3640 [1:06:21<17:08,  1.37s/it]
 79%|███████▉  | 2889/3640 [1:06:22<17:04,  1.36s/it]
 79%|███████▉  | 2890/3640 [1:06:24<17:04,  1.37s/it]
 79%|███████▉  | 2891/3640 [1:06:25<17:04,  1.37s/it]
 79%|███████▉  | 2892/3640 [1:06:27<17:03,  1.37s/it]
 79%|███████▉  | 2893/3640 [1:06:28<17:02,  1.37s/it]
 80%|███████▉  | 2894/3640 [1:06:29<17:01,  1.37s/it]
 80%|███████▉  | 2895/3640 [1:06:31<17:01,  1.37s/it]
 80%|███████▉  | 2896/3640 [1:06:32<16:59,  1.37s/it]
 80%|███████▉  | 2897/3640 [1:06:33<16:57,  1.37s/it]
 80%|███████▉  | 2898/3640 [1:06:35<16:57,  1.37s/it]
 80%|███████▉  | 2899/3640 [1:06:36<16:54,  1.37s/it]
 80%|███████▉  | 2900/3640 [1:06:37<16:53,  1.37s/it]
                                                     

 80%|███████▉  | 2900/3640 [1:06:37<16:53,  1.37s/it]
 80%|███████▉  | 2901/3640 [1:06:39<16:53,  1.37s/it]
 80%|███████▉  | 2902/3640 [1:06:40<16:52,  1.37s/it]
 80%|███████▉  | 2903/3640 [1:06:42<16:50,  1.37s/it]
 80%|███████▉  | 2904/3640 [1:06:43<16:49,  1.37s/it]
 80%|███████▉  | 2905/3640 [1:06:44<16:47,  1.37s/it]
 80%|███████▉  | 2906/3640 [1:06:46<16:44,  1.37s/it]
 80%|███████▉  | 2907/3640 [1:06:47<16:44,  1.37s/it]
 80%|███████▉  | 2908/3640 [1:06:48<16:46,  1.37s/it]
 80%|███████▉  | 2909/3640 [1:06:50<16:43,  1.37s/it]
 80%|███████▉  | 2910/3640 [1:06:51<16:41,  1.37s/it]
 80%|███████▉  | 2911/3640 [1:06:53<16:39,  1.37s/it]
 80%|████████  | 2912/3640 [1:06:54<16:38,  1.37s/it]
 80%|████████  | 2913/3640 [1:06:55<16:36,  1.37s/it]
 80%|████████  | 2914/3640 [1:06:57<16:33,  1.37s/it]
 80%|████████  | 2915/3640 [1:06:58<16:32,  1.37s/it]
 80%|████████  | 2916/3640 [1:07:00<17:17,  1.43s/it]
 80%|████████  | 2917/3640 [1:07:01<17:01,  1.41s/it]
 80%|████████  | 2918/3640 [1:07:02<16:48,  1.40s/it]
 80%|████████  | 2919/3640 [1:07:04<16:42,  1.39s/it]
 80%|████████  | 2920/3640 [1:07:05<16:36,  1.38s/it]
 80%|████████  | 2921/3640 [1:07:06<16:32,  1.38s/it]
 80%|████████  | 2922/3640 [1:07:08<16:28,  1.38s/it]
 80%|████████  | 2923/3640 [1:07:09<16:25,  1.37s/it]
 80%|████████  | 2924/3640 [1:07:11<16:20,  1.37s/it]
 80%|████████  | 2925/3640 [1:07:12<16:18,  1.37s/it]
                                                     

 80%|████████  | 2925/3640 [1:07:12<16:18,  1.37s/it]
 80%|████████  | 2926/3640 [1:07:13<16:16,  1.37s/it]
 80%|████████  | 2927/3640 [1:07:15<16:15,  1.37s/it]
 80%|████████  | 2928/3640 [1:07:16<16:11,  1.36s/it]
 80%|████████  | 2929/3640 [1:07:17<16:10,  1.37s/it]
 80%|████████  | 2930/3640 [1:07:19<16:11,  1.37s/it]
 81%|████████  | 2931/3640 [1:07:20<16:12,  1.37s/it]
 81%|████████  | 2932/3640 [1:07:21<16:09,  1.37s/it]
 81%|████████  | 2933/3640 [1:07:23<16:08,  1.37s/it]
 81%|████████  | 2934/3640 [1:07:24<16:05,  1.37s/it]
 81%|████████  | 2935/3640 [1:07:26<16:04,  1.37s/it]
 81%|████████  | 2936/3640 [1:07:27<16:02,  1.37s/it]
 81%|████████  | 2937/3640 [1:07:28<16:01,  1.37s/it]
 81%|████████  | 2938/3640 [1:07:30<15:59,  1.37s/it]
 81%|████████  | 2939/3640 [1:07:31<15:57,  1.37s/it]
 81%|████████  | 2940/3640 [1:07:32<15:55,  1.36s/it]
 81%|████████  | 2941/3640 [1:07:34<15:55,  1.37s/it]
 81%|████████  | 2942/3640 [1:07:35<15:54,  1.37s/it]
 81%|████████  | 2943/3640 [1:07:37<16:30,  1.42s/it]
 81%|████████  | 2944/3640 [1:07:38<16:17,  1.41s/it]
 81%|████████  | 2945/3640 [1:07:39<16:09,  1.39s/it]
 81%|████████  | 2946/3640 [1:07:41<16:01,  1.38s/it]
 81%|████████  | 2947/3640 [1:07:42<15:55,  1.38s/it]
 81%|████████  | 2948/3640 [1:07:44<15:51,  1.38s/it]
 81%|████████  | 2949/3640 [1:07:45<15:47,  1.37s/it]
 81%|████████  | 2950/3640 [1:07:46<15:46,  1.37s/it]
                                                     

 81%|████████  | 2950/3640 [1:07:46<15:46,  1.37s/it]
 81%|████████  | 2951/3640 [1:07:48<15:44,  1.37s/it]
 81%|████████  | 2952/3640 [1:07:49<15:42,  1.37s/it]
 81%|████████  | 2953/3640 [1:07:50<15:38,  1.37s/it]
 81%|████████  | 2954/3640 [1:07:52<16:09,  1.41s/it]
 81%|████████  | 2955/3640 [1:07:53<16:03,  1.41s/it]
 81%|████████  | 2956/3640 [1:07:55<15:53,  1.39s/it]
 81%|████████  | 2957/3640 [1:07:56<15:46,  1.39s/it]
 81%|████████▏ | 2958/3640 [1:07:57<15:41,  1.38s/it]
 81%|████████▏ | 2959/3640 [1:08:00<19:29,  1.72s/it]
 81%|████████▏ | 2960/3640 [1:08:01<18:17,  1.61s/it]
 81%|████████▏ | 2961/3640 [1:08:03<17:23,  1.54s/it]
 81%|████████▏ | 2962/3640 [1:08:04<17:29,  1.55s/it]
 81%|████████▏ | 2963/3640 [1:08:06<16:50,  1.49s/it]
 81%|████████▏ | 2964/3640 [1:08:07<16:24,  1.46s/it]
 81%|████████▏ | 2965/3640 [1:08:08<16:04,  1.43s/it]
 81%|████████▏ | 2966/3640 [1:08:10<15:50,  1.41s/it]
 82%|████████▏ | 2967/3640 [1:08:11<15:39,  1.40s/it]
 82%|████████▏ | 2968/3640 [1:08:12<15:32,  1.39s/it]
 82%|████████▏ | 2969/3640 [1:08:14<15:27,  1.38s/it]
 82%|████████▏ | 2970/3640 [1:08:15<15:24,  1.38s/it]
 82%|████████▏ | 2971/3640 [1:08:16<15:20,  1.38s/it]
 82%|████████▏ | 2972/3640 [1:08:18<15:15,  1.37s/it]
 82%|████████▏ | 2973/3640 [1:08:19<15:13,  1.37s/it]
 82%|████████▏ | 2974/3640 [1:08:21<15:11,  1.37s/it]
 82%|████████▏ | 2975/3640 [1:08:22<15:11,  1.37s/it]
                                                     

 82%|████████▏ | 2975/3640 [1:08:22<15:11,  1.37s/it]
 82%|████████▏ | 2976/3640 [1:08:23<15:08,  1.37s/it]
 82%|████████▏ | 2977/3640 [1:08:25<15:06,  1.37s/it]
 82%|████████▏ | 2978/3640 [1:08:26<15:10,  1.38s/it]
 82%|████████▏ | 2979/3640 [1:08:27<15:06,  1.37s/it]
 82%|████████▏ | 2980/3640 [1:08:29<15:04,  1.37s/it]
 82%|████████▏ | 2981/3640 [1:08:30<15:03,  1.37s/it]
 82%|████████▏ | 2982/3640 [1:08:32<15:01,  1.37s/it]
 82%|████████▏ | 2983/3640 [1:08:33<14:57,  1.37s/it]
 82%|████████▏ | 2984/3640 [1:08:34<14:56,  1.37s/it]
 82%|████████▏ | 2985/3640 [1:08:36<14:54,  1.37s/it]
 82%|████████▏ | 2986/3640 [1:08:37<14:52,  1.36s/it]
 82%|████████▏ | 2987/3640 [1:08:38<14:51,  1.37s/it]
 82%|████████▏ | 2988/3640 [1:08:40<14:51,  1.37s/it]
 82%|████████▏ | 2989/3640 [1:08:41<14:50,  1.37s/it]
 82%|████████▏ | 2990/3640 [1:08:42<14:49,  1.37s/it]
 82%|████████▏ | 2991/3640 [1:08:44<14:46,  1.37s/it]
 82%|████████▏ | 2992/3640 [1:08:45<14:46,  1.37s/it]
 82%|████████▏ | 2993/3640 [1:08:47<14:45,  1.37s/it]
 82%|████████▏ | 2994/3640 [1:08:48<14:43,  1.37s/it]
 82%|████████▏ | 2995/3640 [1:08:49<14:42,  1.37s/it]
 82%|████████▏ | 2996/3640 [1:08:51<14:41,  1.37s/it]
 82%|████████▏ | 2997/3640 [1:08:52<14:38,  1.37s/it]
 82%|████████▏ | 2998/3640 [1:08:53<14:38,  1.37s/it]
 82%|████████▏ | 2999/3640 [1:08:55<14:34,  1.37s/it]
 82%|████████▏ | 3000/3640 [1:08:56<14:34,  1.37s/it]
                                                     

 82%|████████▏ | 3000/3640 [1:08:56<14:34,  1.37s/it]
 82%|████████▏ | 3001/3640 [1:08:58<14:33,  1.37s/it]
 82%|████████▏ | 3002/3640 [1:08:59<14:38,  1.38s/it]
 82%|████████▎ | 3003/3640 [1:09:00<14:34,  1.37s/it]
 83%|████████▎ | 3004/3640 [1:09:02<14:30,  1.37s/it]
 83%|████████▎ | 3005/3640 [1:09:03<14:27,  1.37s/it]
 83%|████████▎ | 3006/3640 [1:09:04<14:26,  1.37s/it]
 83%|████████▎ | 3007/3640 [1:09:06<14:25,  1.37s/it]
 83%|████████▎ | 3008/3640 [1:09:07<14:22,  1.36s/it]
 83%|████████▎ | 3009/3640 [1:09:08<14:22,  1.37s/it]
 83%|████████▎ | 3010/3640 [1:09:10<14:22,  1.37s/it]
 83%|████████▎ | 3011/3640 [1:09:11<14:20,  1.37s/it]
 83%|████████▎ | 3012/3640 [1:09:13<14:19,  1.37s/it]
 83%|████████▎ | 3013/3640 [1:09:14<14:18,  1.37s/it]
 83%|████████▎ | 3014/3640 [1:09:15<14:17,  1.37s/it]
 83%|████████▎ | 3015/3640 [1:09:17<14:14,  1.37s/it]
 83%|████████▎ | 3016/3640 [1:09:18<14:13,  1.37s/it]
 83%|████████▎ | 3017/3640 [1:09:19<14:13,  1.37s/it]
 83%|████████▎ | 3018/3640 [1:09:21<14:50,  1.43s/it]
 83%|████████▎ | 3019/3640 [1:09:22<14:36,  1.41s/it]
 83%|████████▎ | 3020/3640 [1:09:24<14:27,  1.40s/it]
 83%|████████▎ | 3021/3640 [1:09:25<14:20,  1.39s/it]
 83%|████████▎ | 3022/3640 [1:09:26<14:14,  1.38s/it]
 83%|████████▎ | 3023/3640 [1:09:28<14:11,  1.38s/it]
 83%|████████▎ | 3024/3640 [1:09:29<14:07,  1.38s/it]
 83%|████████▎ | 3025/3640 [1:09:31<14:05,  1.37s/it]
                                                     

 83%|████████▎ | 3025/3640 [1:09:31<14:05,  1.37s/it]
 83%|████████▎ | 3026/3640 [1:09:32<14:07,  1.38s/it]
 83%|████████▎ | 3027/3640 [1:09:33<14:03,  1.38s/it]
 83%|████████▎ | 3028/3640 [1:09:35<14:38,  1.44s/it]
 83%|████████▎ | 3029/3640 [1:09:36<14:25,  1.42s/it]
 83%|████████▎ | 3030/3640 [1:09:38<14:15,  1.40s/it]
 83%|████████▎ | 3031/3640 [1:09:39<14:08,  1.39s/it]
 83%|████████▎ | 3032/3640 [1:09:40<14:00,  1.38s/it]
 83%|████████▎ | 3033/3640 [1:09:42<13:56,  1.38s/it]
 83%|████████▎ | 3034/3640 [1:09:43<13:52,  1.37s/it]
 83%|████████▎ | 3035/3640 [1:09:44<13:50,  1.37s/it]
 83%|████████▎ | 3036/3640 [1:09:46<13:47,  1.37s/it]
 83%|████████▎ | 3037/3640 [1:09:47<13:43,  1.37s/it]
 83%|████████▎ | 3038/3640 [1:09:49<13:43,  1.37s/it]
 83%|████████▎ | 3039/3640 [1:09:50<13:42,  1.37s/it]
 84%|████████▎ | 3040/3640 [1:09:51<13:41,  1.37s/it]
 84%|████████▎ | 3041/3640 [1:09:53<13:40,  1.37s/it]
 84%|████████▎ | 3042/3640 [1:09:54<13:37,  1.37s/it]
 84%|████████▎ | 3043/3640 [1:09:55<13:35,  1.37s/it]
 84%|████████▎ | 3044/3640 [1:09:57<13:34,  1.37s/it]
 84%|████████▎ | 3045/3640 [1:09:58<13:33,  1.37s/it]
 84%|████████▎ | 3046/3640 [1:10:00<13:27,  1.36s/it]
 84%|████████▎ | 3047/3640 [1:10:01<13:27,  1.36s/it]
 84%|████████▎ | 3048/3640 [1:10:02<13:27,  1.36s/it]
 84%|████████▍ | 3049/3640 [1:10:04<13:26,  1.36s/it]
 84%|████████▍ | 3050/3640 [1:10:05<13:29,  1.37s/it]
                                                     

 84%|████████▍ | 3050/3640 [1:10:05<13:29,  1.37s/it]
 84%|████████▍ | 3051/3640 [1:10:06<13:27,  1.37s/it]
 84%|████████▍ | 3052/3640 [1:10:08<13:26,  1.37s/it]
 84%|████████▍ | 3053/3640 [1:10:09<13:25,  1.37s/it]
 84%|████████▍ | 3054/3640 [1:10:10<13:23,  1.37s/it]
 84%|████████▍ | 3055/3640 [1:10:12<13:20,  1.37s/it]
 84%|████████▍ | 3056/3640 [1:10:13<13:18,  1.37s/it]
 84%|████████▍ | 3057/3640 [1:10:15<13:17,  1.37s/it]
 84%|████████▍ | 3058/3640 [1:10:16<13:17,  1.37s/it]
 84%|████████▍ | 3059/3640 [1:10:17<13:15,  1.37s/it]
 84%|████████▍ | 3060/3640 [1:10:19<13:13,  1.37s/it]
 84%|████████▍ | 3061/3640 [1:10:20<13:11,  1.37s/it]
 84%|████████▍ | 3062/3640 [1:10:21<13:09,  1.37s/it]
 84%|████████▍ | 3063/3640 [1:10:23<13:07,  1.36s/it]
 84%|████████▍ | 3064/3640 [1:10:24<13:05,  1.36s/it]
 84%|████████▍ | 3065/3640 [1:10:25<13:03,  1.36s/it]
 84%|████████▍ | 3066/3640 [1:10:27<13:01,  1.36s/it]
 84%|████████▍ | 3067/3640 [1:10:28<12:57,  1.36s/it]
 84%|████████▍ | 3068/3640 [1:10:30<12:58,  1.36s/it]
 84%|████████▍ | 3069/3640 [1:10:31<12:56,  1.36s/it]
 84%|████████▍ | 3070/3640 [1:10:32<12:56,  1.36s/it]
 84%|████████▍ | 3071/3640 [1:10:34<12:54,  1.36s/it]
 84%|████████▍ | 3072/3640 [1:10:35<12:54,  1.36s/it]
 84%|████████▍ | 3073/3640 [1:10:36<12:54,  1.37s/it]
 84%|████████▍ | 3074/3640 [1:10:38<12:57,  1.37s/it]
 84%|████████▍ | 3075/3640 [1:10:39<12:55,  1.37s/it]
                                                     

 84%|████████▍ | 3075/3640 [1:10:39<12:55,  1.37s/it]
 85%|████████▍ | 3076/3640 [1:10:41<12:52,  1.37s/it]
 85%|████████▍ | 3077/3640 [1:10:42<12:48,  1.37s/it]
 85%|████████▍ | 3078/3640 [1:10:43<12:47,  1.37s/it]
 85%|████████▍ | 3079/3640 [1:10:45<12:46,  1.37s/it]
 85%|████████▍ | 3080/3640 [1:10:46<12:44,  1.37s/it]
 85%|████████▍ | 3081/3640 [1:10:47<12:42,  1.36s/it]
 85%|████████▍ | 3082/3640 [1:10:49<12:40,  1.36s/it]
 85%|████████▍ | 3083/3640 [1:10:50<12:40,  1.36s/it]
 85%|████████▍ | 3084/3640 [1:10:51<12:39,  1.37s/it]
 85%|████████▍ | 3085/3640 [1:10:53<12:36,  1.36s/it]
 85%|████████▍ | 3086/3640 [1:10:54<12:36,  1.37s/it]
 85%|████████▍ | 3087/3640 [1:10:56<12:35,  1.37s/it]
 85%|████████▍ | 3088/3640 [1:10:57<12:33,  1.37s/it]
 85%|████████▍ | 3089/3640 [1:10:58<12:32,  1.37s/it]
 85%|████████▍ | 3090/3640 [1:11:00<12:32,  1.37s/it]
 85%|████████▍ | 3091/3640 [1:11:01<12:29,  1.37s/it]
 85%|████████▍ | 3092/3640 [1:11:02<12:29,  1.37s/it]
 85%|████████▍ | 3093/3640 [1:11:04<12:28,  1.37s/it]
 85%|████████▌ | 3094/3640 [1:11:05<12:26,  1.37s/it]
 85%|████████▌ | 3095/3640 [1:11:06<12:25,  1.37s/it]
 85%|████████▌ | 3096/3640 [1:11:08<12:22,  1.37s/it]
 85%|████████▌ | 3097/3640 [1:11:09<12:22,  1.37s/it]
 85%|████████▌ | 3098/3640 [1:11:11<12:24,  1.37s/it]
 85%|████████▌ | 3099/3640 [1:11:12<12:20,  1.37s/it]
 85%|████████▌ | 3100/3640 [1:11:13<12:18,  1.37s/it]
                                                     

 85%|████████▌ | 3100/3640 [1:11:13<12:18,  1.37s/it]
 85%|████████▌ | 3101/3640 [1:11:15<12:17,  1.37s/it]
 85%|████████▌ | 3102/3640 [1:11:16<12:15,  1.37s/it]
 85%|████████▌ | 3103/3640 [1:11:17<12:12,  1.36s/it]
 85%|████████▌ | 3104/3640 [1:11:19<12:11,  1.36s/it]
 85%|████████▌ | 3105/3640 [1:11:20<12:44,  1.43s/it]
 85%|████████▌ | 3106/3640 [1:11:22<12:33,  1.41s/it]
 85%|████████▌ | 3107/3640 [1:11:23<12:24,  1.40s/it]
 85%|████████▌ | 3108/3640 [1:11:24<12:18,  1.39s/it]
 85%|████████▌ | 3109/3640 [1:11:26<12:13,  1.38s/it]
 85%|████████▌ | 3110/3640 [1:11:27<12:09,  1.38s/it]
 85%|████████▌ | 3111/3640 [1:11:29<12:07,  1.37s/it]
 85%|████████▌ | 3112/3640 [1:11:30<12:04,  1.37s/it]
 86%|████████▌ | 3113/3640 [1:11:31<12:01,  1.37s/it]
 86%|████████▌ | 3114/3640 [1:11:33<11:59,  1.37s/it]
 86%|████████▌ | 3115/3640 [1:11:34<11:58,  1.37s/it]
 86%|████████▌ | 3116/3640 [1:11:35<11:55,  1.37s/it]
 86%|████████▌ | 3117/3640 [1:11:37<11:53,  1.36s/it]
 86%|████████▌ | 3118/3640 [1:11:38<11:52,  1.37s/it]
 86%|████████▌ | 3119/3640 [1:11:39<11:50,  1.36s/it]
 86%|████████▌ | 3120/3640 [1:11:41<11:50,  1.37s/it]
 86%|████████▌ | 3121/3640 [1:11:42<11:52,  1.37s/it]
 86%|████████▌ | 3122/3640 [1:11:44<11:49,  1.37s/it]
 86%|████████▌ | 3123/3640 [1:11:45<11:44,  1.36s/it]
 86%|████████▌ | 3124/3640 [1:11:46<11:42,  1.36s/it]
 86%|████████▌ | 3125/3640 [1:11:48<11:41,  1.36s/it]
                                                     

 86%|████████▌ | 3125/3640 [1:11:48<11:41,  1.36s/it]
 86%|████████▌ | 3126/3640 [1:11:49<11:40,  1.36s/it]
 86%|████████▌ | 3127/3640 [1:11:50<11:40,  1.37s/it]
 86%|████████▌ | 3128/3640 [1:11:52<11:40,  1.37s/it]
 86%|████████▌ | 3129/3640 [1:11:53<11:37,  1.36s/it]
 86%|████████▌ | 3130/3640 [1:11:54<11:35,  1.36s/it]
 86%|████████▌ | 3131/3640 [1:11:56<11:33,  1.36s/it]
 86%|████████▌ | 3132/3640 [1:11:57<11:32,  1.36s/it]
 86%|████████▌ | 3133/3640 [1:11:59<11:32,  1.37s/it]
 86%|████████▌ | 3134/3640 [1:12:00<11:29,  1.36s/it]
 86%|████████▌ | 3135/3640 [1:12:01<11:28,  1.36s/it]
 86%|████████▌ | 3136/3640 [1:12:03<11:27,  1.36s/it]
 86%|████████▌ | 3137/3640 [1:12:04<11:26,  1.37s/it]
 86%|████████▌ | 3138/3640 [1:12:05<11:24,  1.36s/it]
 86%|████████▌ | 3139/3640 [1:12:07<11:24,  1.37s/it]
 86%|████████▋ | 3140/3640 [1:12:08<11:23,  1.37s/it]
 86%|████████▋ | 3141/3640 [1:12:09<11:20,  1.36s/it]
 86%|████████▋ | 3142/3640 [1:12:11<11:20,  1.37s/it]
 86%|████████▋ | 3143/3640 [1:12:12<11:18,  1.37s/it]
 86%|████████▋ | 3144/3640 [1:12:14<11:17,  1.37s/it]
 86%|████████▋ | 3145/3640 [1:12:15<11:17,  1.37s/it]
 86%|████████▋ | 3146/3640 [1:12:16<11:17,  1.37s/it]
 86%|████████▋ | 3147/3640 [1:12:18<11:16,  1.37s/it]
 86%|████████▋ | 3148/3640 [1:12:19<11:14,  1.37s/it]
 87%|████████▋ | 3149/3640 [1:12:20<11:11,  1.37s/it]
 87%|████████▋ | 3150/3640 [1:12:22<11:10,  1.37s/it]
                                                     

 87%|████████▋ | 3150/3640 [1:12:22<11:10,  1.37s/it]
 87%|████████▋ | 3151/3640 [1:12:23<11:09,  1.37s/it]
 87%|████████▋ | 3152/3640 [1:12:25<11:07,  1.37s/it]
 87%|████████▋ | 3153/3640 [1:12:26<11:07,  1.37s/it]
 87%|████████▋ | 3154/3640 [1:12:27<11:03,  1.37s/it]
 87%|████████▋ | 3155/3640 [1:12:29<11:02,  1.37s/it]
 87%|████████▋ | 3156/3640 [1:12:30<11:01,  1.37s/it]
 87%|████████▋ | 3157/3640 [1:12:31<10:59,  1.36s/it]
 87%|████████▋ | 3158/3640 [1:12:33<10:58,  1.37s/it]
 87%|████████▋ | 3159/3640 [1:12:34<10:57,  1.37s/it]
 87%|████████▋ | 3160/3640 [1:12:35<10:55,  1.37s/it]
 87%|████████▋ | 3161/3640 [1:12:37<10:54,  1.37s/it]
 87%|████████▋ | 3162/3640 [1:12:38<10:53,  1.37s/it]
 87%|████████▋ | 3163/3640 [1:12:40<10:52,  1.37s/it]
 87%|████████▋ | 3164/3640 [1:12:41<10:50,  1.37s/it]
 87%|████████▋ | 3165/3640 [1:12:42<10:48,  1.37s/it]
 87%|████████▋ | 3166/3640 [1:12:44<10:47,  1.37s/it]
 87%|████████▋ | 3167/3640 [1:12:45<10:46,  1.37s/it]
 87%|████████▋ | 3168/3640 [1:12:46<10:44,  1.37s/it]
 87%|████████▋ | 3169/3640 [1:12:48<10:46,  1.37s/it]
 87%|████████▋ | 3170/3640 [1:12:49<10:44,  1.37s/it]
 87%|████████▋ | 3171/3640 [1:12:51<10:40,  1.37s/it]
 87%|████████▋ | 3172/3640 [1:12:52<10:39,  1.37s/it]
 87%|████████▋ | 3173/3640 [1:12:53<10:38,  1.37s/it]
 87%|████████▋ | 3174/3640 [1:12:55<10:37,  1.37s/it]
 87%|████████▋ | 3175/3640 [1:12:56<10:36,  1.37s/it]
                                                     

 87%|████████▋ | 3175/3640 [1:12:56<10:36,  1.37s/it]
 87%|████████▋ | 3176/3640 [1:12:57<10:35,  1.37s/it]
 87%|████████▋ | 3177/3640 [1:12:59<10:33,  1.37s/it]
 87%|████████▋ | 3178/3640 [1:13:00<10:30,  1.37s/it]
 87%|████████▋ | 3179/3640 [1:13:01<10:30,  1.37s/it]
 87%|████████▋ | 3180/3640 [1:13:03<10:27,  1.36s/it]
 87%|████████▋ | 3181/3640 [1:13:04<10:26,  1.37s/it]
 87%|████████▋ | 3182/3640 [1:13:06<10:25,  1.37s/it]
 87%|████████▋ | 3183/3640 [1:13:07<10:24,  1.37s/it]
 87%|████████▋ | 3184/3640 [1:13:08<10:23,  1.37s/it]
 88%|████████▊ | 3185/3640 [1:13:10<10:22,  1.37s/it]
 88%|████████▊ | 3186/3640 [1:13:11<10:20,  1.37s/it]
 88%|████████▊ | 3187/3640 [1:13:12<10:18,  1.37s/it]
 88%|████████▊ | 3188/3640 [1:13:14<10:17,  1.37s/it]
 88%|████████▊ | 3189/3640 [1:13:15<10:16,  1.37s/it]
 88%|████████▊ | 3190/3640 [1:13:16<10:14,  1.37s/it]
 88%|████████▊ | 3191/3640 [1:13:18<10:12,  1.36s/it]
 88%|████████▊ | 3192/3640 [1:13:19<10:10,  1.36s/it]
 88%|████████▊ | 3193/3640 [1:13:21<10:13,  1.37s/it]
 88%|████████▊ | 3194/3640 [1:13:22<10:10,  1.37s/it]
 88%|████████▊ | 3195/3640 [1:13:23<10:07,  1.37s/it]
 88%|████████▊ | 3196/3640 [1:13:25<10:05,  1.36s/it]
 88%|████████▊ | 3197/3640 [1:13:26<10:02,  1.36s/it]
 88%|████████▊ | 3198/3640 [1:13:27<10:02,  1.36s/it]
 88%|████████▊ | 3199/3640 [1:13:29<10:00,  1.36s/it]
 88%|████████▊ | 3200/3640 [1:13:30<09:58,  1.36s/it]
                                                     

 88%|████████▊ | 3200/3640 [1:13:30<09:58,  1.36s/it]
 88%|████████▊ | 3201/3640 [1:13:31<09:58,  1.36s/it]
 88%|████████▊ | 3202/3640 [1:13:33<09:57,  1.36s/it]
 88%|████████▊ | 3203/3640 [1:13:34<09:56,  1.36s/it]
 88%|████████▊ | 3204/3640 [1:13:36<09:54,  1.36s/it]
 88%|████████▊ | 3205/3640 [1:13:37<09:53,  1.36s/it]
 88%|████████▊ | 3206/3640 [1:13:38<09:52,  1.37s/it]
 88%|████████▊ | 3207/3640 [1:13:40<09:51,  1.37s/it]
 88%|████████▊ | 3208/3640 [1:13:41<09:50,  1.37s/it]
 88%|████████▊ | 3209/3640 [1:13:43<10:15,  1.43s/it]
 88%|████████▊ | 3210/3640 [1:13:44<10:06,  1.41s/it]
 88%|████████▊ | 3211/3640 [1:13:45<09:58,  1.40s/it]
 88%|████████▊ | 3212/3640 [1:13:47<09:52,  1.38s/it]
 88%|████████▊ | 3213/3640 [1:13:48<09:48,  1.38s/it]
 88%|████████▊ | 3214/3640 [1:13:49<09:46,  1.38s/it]
 88%|████████▊ | 3215/3640 [1:13:51<09:44,  1.38s/it]
 88%|████████▊ | 3216/3640 [1:13:52<09:41,  1.37s/it]
 88%|████████▊ | 3217/3640 [1:13:54<09:42,  1.38s/it]
 88%|████████▊ | 3218/3640 [1:13:55<09:40,  1.37s/it]
 88%|████████▊ | 3219/3640 [1:13:56<09:37,  1.37s/it]
 88%|████████▊ | 3220/3640 [1:13:58<09:35,  1.37s/it]
 88%|████████▊ | 3221/3640 [1:13:59<09:33,  1.37s/it]
 89%|████████▊ | 3222/3640 [1:14:00<09:31,  1.37s/it]
 89%|████████▊ | 3223/3640 [1:14:02<09:30,  1.37s/it]
 89%|████████▊ | 3224/3640 [1:14:03<09:28,  1.37s/it]
 89%|████████▊ | 3225/3640 [1:14:04<09:26,  1.37s/it]
                                                     

 89%|████████▊ | 3225/3640 [1:14:04<09:26,  1.37s/it]
 89%|████████▊ | 3226/3640 [1:14:06<09:25,  1.37s/it]
 89%|████████▊ | 3227/3640 [1:14:07<09:24,  1.37s/it]
 89%|████████▊ | 3228/3640 [1:14:09<09:22,  1.36s/it]
 89%|████████▊ | 3229/3640 [1:14:10<09:21,  1.37s/it]
 89%|████████▊ | 3230/3640 [1:14:11<09:21,  1.37s/it]
 89%|████████▉ | 3231/3640 [1:14:13<09:19,  1.37s/it]
 89%|████████▉ | 3232/3640 [1:14:14<09:18,  1.37s/it]
 89%|████████▉ | 3233/3640 [1:14:15<09:16,  1.37s/it]
 89%|████████▉ | 3234/3640 [1:14:17<09:15,  1.37s/it]
 89%|████████▉ | 3235/3640 [1:14:18<09:13,  1.37s/it]
 89%|████████▉ | 3236/3640 [1:14:20<09:11,  1.36s/it]
 89%|████████▉ | 3237/3640 [1:14:21<09:10,  1.37s/it]
 89%|████████▉ | 3238/3640 [1:14:22<09:09,  1.37s/it]
 89%|████████▉ | 3239/3640 [1:14:24<09:07,  1.36s/it]
 89%|████████▉ | 3240/3640 [1:14:25<09:07,  1.37s/it]
 89%|████████▉ | 3241/3640 [1:14:26<09:09,  1.38s/it]
 89%|████████▉ | 3242/3640 [1:14:28<09:06,  1.37s/it]
 89%|████████▉ | 3243/3640 [1:14:29<09:04,  1.37s/it]
 89%|████████▉ | 3244/3640 [1:14:30<09:02,  1.37s/it]
 89%|████████▉ | 3245/3640 [1:14:32<09:00,  1.37s/it]
 89%|████████▉ | 3246/3640 [1:14:33<08:58,  1.37s/it]
 89%|████████▉ | 3247/3640 [1:14:35<08:56,  1.36s/it]
 89%|████████▉ | 3248/3640 [1:14:36<08:55,  1.37s/it]
 89%|████████▉ | 3249/3640 [1:14:37<08:53,  1.36s/it]
 89%|████████▉ | 3250/3640 [1:14:39<08:52,  1.36s/it]
                                                     

 89%|████████▉ | 3250/3640 [1:14:39<08:52,  1.36s/it]
 89%|████████▉ | 3251/3640 [1:14:40<08:51,  1.37s/it]
 89%|████████▉ | 3252/3640 [1:14:41<08:50,  1.37s/it]
 89%|████████▉ | 3253/3640 [1:14:43<08:49,  1.37s/it]
 89%|████████▉ | 3254/3640 [1:14:44<08:47,  1.37s/it]
 89%|████████▉ | 3255/3640 [1:14:46<08:47,  1.37s/it]
 89%|████████▉ | 3256/3640 [1:14:47<08:45,  1.37s/it]
 89%|████████▉ | 3257/3640 [1:14:48<08:44,  1.37s/it]
 90%|████████▉ | 3258/3640 [1:14:50<08:41,  1.37s/it]
 90%|████████▉ | 3259/3640 [1:14:51<08:40,  1.37s/it]
 90%|████████▉ | 3260/3640 [1:14:52<08:39,  1.37s/it]
 90%|████████▉ | 3261/3640 [1:14:54<08:38,  1.37s/it]
 90%|████████▉ | 3262/3640 [1:14:55<08:36,  1.37s/it]
 90%|████████▉ | 3263/3640 [1:14:56<08:35,  1.37s/it]
 90%|████████▉ | 3264/3640 [1:14:58<08:33,  1.37s/it]
 90%|████████▉ | 3265/3640 [1:14:59<08:34,  1.37s/it]
 90%|████████▉ | 3266/3640 [1:15:01<08:32,  1.37s/it]
 90%|████████▉ | 3267/3640 [1:15:02<08:30,  1.37s/it]
 90%|████████▉ | 3268/3640 [1:15:03<08:28,  1.37s/it]
 90%|████████▉ | 3269/3640 [1:15:05<08:27,  1.37s/it]
 90%|████████▉ | 3270/3640 [1:15:06<08:26,  1.37s/it]
 90%|████████▉ | 3271/3640 [1:15:07<08:24,  1.37s/it]
 90%|████████▉ | 3272/3640 [1:15:09<08:22,  1.37s/it]
 90%|████████▉ | 3273/3640 [1:15:10<08:21,  1.37s/it]
 90%|████████▉ | 3274/3640 [1:15:11<08:19,  1.37s/it]
 90%|████████▉ | 3275/3640 [1:15:13<08:18,  1.36s/it]
                                                     

 90%|████████▉ | 3275/3640 [1:15:13<08:18,  1.36s/it]
 90%|█████████ | 3276/3640 [1:15:14<08:17,  1.37s/it]
 90%|█████████ | 3277/3640 [1:15:16<08:15,  1.37s/it]
 90%|█████████ | 3278/3640 [1:15:17<08:13,  1.36s/it]
 90%|█████████ | 3279/3640 [1:15:18<08:11,  1.36s/it]
 90%|█████████ | 3280/3640 [1:15:20<08:11,  1.36s/it]
 90%|█████████ | 3281/3640 [1:15:21<08:09,  1.36s/it]
 90%|█████████ | 3282/3640 [1:15:22<08:08,  1.36s/it]
 90%|█████████ | 3283/3640 [1:15:24<08:07,  1.36s/it]
 90%|█████████ | 3284/3640 [1:15:25<08:05,  1.36s/it]
 90%|█████████ | 3285/3640 [1:15:27<08:04,  1.37s/it]
 90%|█████████ | 3286/3640 [1:15:28<08:03,  1.37s/it]
 90%|█████████ | 3287/3640 [1:15:29<08:00,  1.36s/it]
 90%|█████████ | 3288/3640 [1:15:31<07:59,  1.36s/it]
 90%|█████████ | 3289/3640 [1:15:32<08:01,  1.37s/it]
 90%|█████████ | 3290/3640 [1:15:33<07:59,  1.37s/it]
 90%|█████████ | 3291/3640 [1:15:35<07:56,  1.37s/it]
 90%|█████████ | 3292/3640 [1:15:36<07:55,  1.37s/it]
 90%|█████████ | 3293/3640 [1:15:37<07:53,  1.36s/it]
 90%|█████████ | 3294/3640 [1:15:39<07:51,  1.36s/it]
 91%|█████████ | 3295/3640 [1:15:40<07:49,  1.36s/it]
 91%|█████████ | 3296/3640 [1:15:42<07:49,  1.36s/it]
 91%|█████████ | 3297/3640 [1:15:43<08:09,  1.43s/it]
 91%|█████████ | 3298/3640 [1:15:44<08:02,  1.41s/it]
 91%|█████████ | 3299/3640 [1:15:46<07:57,  1.40s/it]
 91%|█████████ | 3300/3640 [1:15:47<07:52,  1.39s/it]
                                                     

 91%|█████████ | 3300/3640 [1:15:47<07:52,  1.39s/it]
 91%|█████████ | 3301/3640 [1:15:49<07:49,  1.39s/it]
 91%|█████████ | 3302/3640 [1:15:50<07:45,  1.38s/it]
 91%|█████████ | 3303/3640 [1:15:51<07:43,  1.37s/it]
 91%|█████████ | 3304/3640 [1:15:53<07:40,  1.37s/it]
 91%|█████████ | 3305/3640 [1:15:54<07:37,  1.37s/it]
 91%|█████████ | 3306/3640 [1:15:55<07:36,  1.37s/it]
 91%|█████████ | 3307/3640 [1:15:57<07:34,  1.36s/it]
 91%|█████████ | 3308/3640 [1:15:58<07:30,  1.36s/it]
 91%|█████████ | 3309/3640 [1:15:59<07:30,  1.36s/it]
 91%|█████████ | 3310/3640 [1:16:01<07:29,  1.36s/it]
 91%|█████████ | 3311/3640 [1:16:02<07:28,  1.36s/it]
 91%|█████████ | 3312/3640 [1:16:04<07:27,  1.37s/it]
 91%|█████████ | 3313/3640 [1:16:05<07:30,  1.38s/it]
 91%|█████████ | 3314/3640 [1:16:06<07:27,  1.37s/it]
 91%|█████████ | 3315/3640 [1:16:08<07:43,  1.43s/it]
 91%|█████████ | 3316/3640 [1:16:09<07:36,  1.41s/it]
 91%|█████████ | 3317/3640 [1:16:11<07:30,  1.40s/it]
 91%|█████████ | 3318/3640 [1:16:12<07:26,  1.39s/it]
 91%|█████████ | 3319/3640 [1:16:13<07:23,  1.38s/it]
 91%|█████████ | 3320/3640 [1:16:15<07:19,  1.37s/it]
 91%|█████████ | 3321/3640 [1:16:16<07:17,  1.37s/it]
 91%|█████████▏| 3322/3640 [1:16:17<07:16,  1.37s/it]
 91%|█████████▏| 3323/3640 [1:16:19<07:14,  1.37s/it]
 91%|█████████▏| 3324/3640 [1:16:20<07:11,  1.37s/it]
 91%|█████████▏| 3325/3640 [1:16:22<07:10,  1.37s/it]
                                                     

 91%|█████████▏| 3325/3640 [1:16:22<07:10,  1.37s/it]
 91%|█████████▏| 3326/3640 [1:16:23<07:09,  1.37s/it]
 91%|█████████▏| 3327/3640 [1:16:24<07:07,  1.37s/it]
 91%|█████████▏| 3328/3640 [1:16:26<07:06,  1.37s/it]
 91%|█████████▏| 3329/3640 [1:16:27<07:05,  1.37s/it]
 91%|█████████▏| 3330/3640 [1:16:28<07:04,  1.37s/it]
 92%|█████████▏| 3331/3640 [1:16:30<07:02,  1.37s/it]
 92%|█████████▏| 3332/3640 [1:16:31<07:01,  1.37s/it]
 92%|█████████▏| 3333/3640 [1:16:32<07:00,  1.37s/it]
 92%|█████████▏| 3334/3640 [1:16:34<06:58,  1.37s/it]
 92%|█████████▏| 3335/3640 [1:16:35<06:57,  1.37s/it]
 92%|█████████▏| 3336/3640 [1:16:37<06:56,  1.37s/it]
 92%|█████████▏| 3337/3640 [1:16:38<06:57,  1.38s/it]
 92%|█████████▏| 3338/3640 [1:16:39<06:55,  1.38s/it]
 92%|█████████▏| 3339/3640 [1:16:41<06:53,  1.37s/it]
 92%|█████████▏| 3340/3640 [1:16:42<06:51,  1.37s/it]
 92%|█████████▏| 3341/3640 [1:16:43<06:49,  1.37s/it]
 92%|█████████▏| 3342/3640 [1:16:45<06:53,  1.39s/it]
 92%|█████████▏| 3343/3640 [1:16:46<06:49,  1.38s/it]
 92%|█████████▏| 3344/3640 [1:16:48<06:48,  1.38s/it]
 92%|█████████▏| 3345/3640 [1:16:49<06:45,  1.38s/it]
 92%|█████████▏| 3346/3640 [1:16:50<06:43,  1.37s/it]
 92%|█████████▏| 3347/3640 [1:16:52<06:41,  1.37s/it]
 92%|█████████▏| 3348/3640 [1:16:53<06:40,  1.37s/it]
 92%|█████████▏| 3349/3640 [1:16:54<06:38,  1.37s/it]
 92%|█████████▏| 3350/3640 [1:16:56<06:37,  1.37s/it]
                                                     

 92%|█████████▏| 3350/3640 [1:16:56<06:37,  1.37s/it]
 92%|█████████▏| 3351/3640 [1:16:57<06:36,  1.37s/it]
 92%|█████████▏| 3352/3640 [1:16:59<06:34,  1.37s/it]
 92%|█████████▏| 3353/3640 [1:17:00<06:33,  1.37s/it]
 92%|█████████▏| 3354/3640 [1:17:01<06:31,  1.37s/it]
 92%|█████████▏| 3355/3640 [1:17:03<06:29,  1.37s/it]
 92%|█████████▏| 3356/3640 [1:17:04<06:28,  1.37s/it]
 92%|█████████▏| 3357/3640 [1:17:05<06:26,  1.37s/it]
 92%|█████████▏| 3358/3640 [1:17:07<06:25,  1.37s/it]
 92%|█████████▏| 3359/3640 [1:17:08<06:24,  1.37s/it]
 92%|█████████▏| 3360/3640 [1:17:10<06:23,  1.37s/it]
 92%|█████████▏| 3361/3640 [1:17:11<06:23,  1.38s/it]
 92%|█████████▏| 3362/3640 [1:17:12<06:21,  1.37s/it]
 92%|█████████▏| 3363/3640 [1:17:14<06:18,  1.37s/it]
 92%|█████████▏| 3364/3640 [1:17:15<06:17,  1.37s/it]
 92%|█████████▏| 3365/3640 [1:17:16<06:16,  1.37s/it]
 92%|█████████▏| 3366/3640 [1:17:18<06:14,  1.37s/it]
 92%|█████████▎| 3367/3640 [1:17:19<06:12,  1.37s/it]
 93%|█████████▎| 3368/3640 [1:17:20<06:10,  1.36s/it]
 93%|█████████▎| 3369/3640 [1:17:22<06:09,  1.36s/it]
 93%|█████████▎| 3370/3640 [1:17:23<06:08,  1.37s/it]
 93%|█████████▎| 3371/3640 [1:17:25<06:08,  1.37s/it]
 93%|█████████▎| 3372/3640 [1:17:26<06:06,  1.37s/it]
 93%|█████████▎| 3373/3640 [1:17:27<06:05,  1.37s/it]
 93%|█████████▎| 3374/3640 [1:17:29<06:04,  1.37s/it]
 93%|█████████▎| 3375/3640 [1:17:30<06:03,  1.37s/it]
                                                     

 93%|█████████▎| 3375/3640 [1:17:30<06:03,  1.37s/it]
 93%|█████████▎| 3376/3640 [1:17:31<06:01,  1.37s/it]
 93%|█████████▎| 3377/3640 [1:17:33<06:00,  1.37s/it]
 93%|█████████▎| 3378/3640 [1:17:34<05:57,  1.37s/it]
 93%|█████████▎| 3379/3640 [1:17:36<05:57,  1.37s/it]
 93%|█████████▎| 3380/3640 [1:17:37<05:54,  1.36s/it]
 93%|█████████▎| 3381/3640 [1:17:38<05:53,  1.37s/it]
 93%|█████████▎| 3382/3640 [1:17:40<05:52,  1.37s/it]
 93%|█████████▎| 3383/3640 [1:17:41<05:51,  1.37s/it]
 93%|█████████▎| 3384/3640 [1:17:42<05:49,  1.36s/it]
 93%|█████████▎| 3385/3640 [1:17:44<05:49,  1.37s/it]
 93%|█████████▎| 3386/3640 [1:17:45<05:47,  1.37s/it]
 93%|█████████▎| 3387/3640 [1:17:46<05:45,  1.37s/it]
 93%|█████████▎| 3388/3640 [1:17:48<05:44,  1.37s/it]
 93%|█████████▎| 3389/3640 [1:17:49<05:43,  1.37s/it]
 93%|█████████▎| 3390/3640 [1:17:51<05:54,  1.42s/it]
 93%|█████████▎| 3391/3640 [1:17:52<05:49,  1.40s/it]
 93%|█████████▎| 3392/3640 [1:17:53<05:45,  1.39s/it]
 93%|█████████▎| 3393/3640 [1:17:55<05:42,  1.39s/it]
 93%|█████████▎| 3394/3640 [1:17:56<05:39,  1.38s/it]
 93%|█████████▎| 3395/3640 [1:17:58<05:37,  1.38s/it]
 93%|█████████▎| 3396/3640 [1:17:59<05:33,  1.37s/it]
 93%|█████████▎| 3397/3640 [1:18:00<05:31,  1.36s/it]
 93%|█████████▎| 3398/3640 [1:18:02<05:30,  1.36s/it]
 93%|█████████▎| 3399/3640 [1:18:03<05:28,  1.36s/it]
 93%|█████████▎| 3400/3640 [1:18:04<05:27,  1.36s/it]
                                                     

 93%|█████████▎| 3400/3640 [1:18:04<05:27,  1.36s/it]
 93%|█████████▎| 3401/3640 [1:18:06<05:27,  1.37s/it]
 93%|█████████▎| 3402/3640 [1:18:07<05:26,  1.37s/it]
 93%|█████████▎| 3403/3640 [1:18:09<05:40,  1.43s/it]
 94%|█████████▎| 3404/3640 [1:18:10<05:34,  1.42s/it]
 94%|█████████▎| 3405/3640 [1:18:11<05:29,  1.40s/it]
 94%|█████████▎| 3406/3640 [1:18:13<05:25,  1.39s/it]
 94%|█████████▎| 3407/3640 [1:18:14<05:22,  1.38s/it]
 94%|█████████▎| 3408/3640 [1:18:16<05:21,  1.39s/it]
 94%|█████████▎| 3409/3640 [1:18:17<05:18,  1.38s/it]
 94%|█████████▎| 3410/3640 [1:18:18<05:16,  1.38s/it]
 94%|█████████▎| 3411/3640 [1:18:20<05:13,  1.37s/it]
 94%|█████████▎| 3412/3640 [1:18:21<05:12,  1.37s/it]
 94%|█████████▍| 3413/3640 [1:18:22<05:11,  1.37s/it]
 94%|█████████▍| 3414/3640 [1:18:24<05:09,  1.37s/it]
 94%|█████████▍| 3415/3640 [1:18:25<05:08,  1.37s/it]
 94%|█████████▍| 3416/3640 [1:18:26<05:06,  1.37s/it]
 94%|█████████▍| 3417/3640 [1:18:28<05:05,  1.37s/it]
 94%|█████████▍| 3418/3640 [1:18:29<05:03,  1.37s/it]
 94%|█████████▍| 3419/3640 [1:18:31<05:01,  1.37s/it]
 94%|█████████▍| 3420/3640 [1:18:32<05:00,  1.37s/it]
 94%|█████████▍| 3421/3640 [1:18:33<04:59,  1.37s/it]
 94%|█████████▍| 3422/3640 [1:18:35<04:58,  1.37s/it]
 94%|█████████▍| 3423/3640 [1:18:36<04:57,  1.37s/it]
 94%|█████████▍| 3424/3640 [1:18:37<04:56,  1.37s/it]
 94%|█████████▍| 3425/3640 [1:18:39<04:54,  1.37s/it]
                                                     

 94%|█████████▍| 3425/3640 [1:18:39<04:54,  1.37s/it]
 94%|█████████▍| 3426/3640 [1:18:40<04:54,  1.38s/it]
 94%|█████████▍| 3427/3640 [1:18:42<04:52,  1.37s/it]
 94%|█████████▍| 3428/3640 [1:18:43<04:50,  1.37s/it]
 94%|█████████▍| 3429/3640 [1:18:44<04:48,  1.37s/it]
 94%|█████████▍| 3430/3640 [1:18:46<04:47,  1.37s/it]
 94%|█████████▍| 3431/3640 [1:18:47<04:45,  1.37s/it]
 94%|█████████▍| 3432/3640 [1:18:48<04:46,  1.38s/it]
 94%|█████████▍| 3433/3640 [1:18:50<04:44,  1.37s/it]
 94%|█████████▍| 3434/3640 [1:18:51<04:42,  1.37s/it]
 94%|█████████▍| 3435/3640 [1:18:53<04:41,  1.37s/it]
 94%|█████████▍| 3436/3640 [1:18:54<04:39,  1.37s/it]
 94%|█████████▍| 3437/3640 [1:18:55<04:37,  1.37s/it]
 94%|█████████▍| 3438/3640 [1:18:57<04:36,  1.37s/it]
 94%|█████████▍| 3439/3640 [1:18:58<04:34,  1.37s/it]
 95%|█████████▍| 3440/3640 [1:18:59<04:33,  1.37s/it]
 95%|█████████▍| 3441/3640 [1:19:01<04:31,  1.37s/it]
 95%|█████████▍| 3442/3640 [1:19:02<04:30,  1.37s/it]
 95%|█████████▍| 3443/3640 [1:19:03<04:29,  1.37s/it]
 95%|█████████▍| 3444/3640 [1:19:05<04:27,  1.37s/it]
 95%|█████████▍| 3445/3640 [1:19:06<04:26,  1.37s/it]
 95%|█████████▍| 3446/3640 [1:19:08<04:25,  1.37s/it]
 95%|█████████▍| 3447/3640 [1:19:09<04:23,  1.37s/it]
 95%|█████████▍| 3448/3640 [1:19:10<04:22,  1.37s/it]
 95%|█████████▍| 3449/3640 [1:19:12<04:20,  1.36s/it]
 95%|█████████▍| 3450/3640 [1:19:13<04:19,  1.36s/it]
                                                     

 95%|█████████▍| 3450/3640 [1:19:13<04:19,  1.36s/it]
 95%|█████████▍| 3451/3640 [1:19:14<04:18,  1.37s/it]
 95%|█████████▍| 3452/3640 [1:19:16<04:17,  1.37s/it]
 95%|█████████▍| 3453/3640 [1:19:17<04:15,  1.37s/it]
 95%|█████████▍| 3454/3640 [1:19:19<04:14,  1.37s/it]
 95%|█████████▍| 3455/3640 [1:19:20<04:12,  1.37s/it]
 95%|█████████▍| 3456/3640 [1:19:21<04:13,  1.38s/it]
 95%|█████████▍| 3457/3640 [1:19:23<04:10,  1.37s/it]
 95%|█████████▌| 3458/3640 [1:19:24<04:09,  1.37s/it]
 95%|█████████▌| 3459/3640 [1:19:25<04:07,  1.37s/it]
 95%|█████████▌| 3460/3640 [1:19:27<04:05,  1.36s/it]
 95%|█████████▌| 3461/3640 [1:19:28<04:04,  1.36s/it]
 95%|█████████▌| 3462/3640 [1:19:29<04:03,  1.37s/it]
 95%|█████████▌| 3463/3640 [1:19:31<04:02,  1.37s/it]
 95%|█████████▌| 3464/3640 [1:19:32<04:00,  1.37s/it]
 95%|█████████▌| 3465/3640 [1:19:34<03:59,  1.37s/it]
 95%|█████████▌| 3466/3640 [1:19:35<03:57,  1.37s/it]
 95%|█████████▌| 3467/3640 [1:19:36<03:56,  1.37s/it]
 95%|█████████▌| 3468/3640 [1:19:38<03:55,  1.37s/it]
 95%|█████████▌| 3469/3640 [1:19:39<03:53,  1.37s/it]
 95%|█████████▌| 3470/3640 [1:19:40<03:52,  1.37s/it]
 95%|█████████▌| 3471/3640 [1:19:42<03:51,  1.37s/it]
 95%|█████████▌| 3472/3640 [1:19:43<03:50,  1.37s/it]
 95%|█████████▌| 3473/3640 [1:19:45<03:48,  1.37s/it]
 95%|█████████▌| 3474/3640 [1:19:46<03:47,  1.37s/it]
 95%|█████████▌| 3475/3640 [1:19:47<03:46,  1.37s/it]
                                                     

 95%|█████████▌| 3475/3640 [1:19:47<03:46,  1.37s/it]
 95%|█████████▌| 3476/3640 [1:19:49<03:45,  1.37s/it]
 96%|█████████▌| 3477/3640 [1:19:50<03:53,  1.43s/it]
 96%|█████████▌| 3478/3640 [1:19:52<03:48,  1.41s/it]
 96%|█████████▌| 3479/3640 [1:19:53<03:44,  1.40s/it]
 96%|█████████▌| 3480/3640 [1:19:54<03:43,  1.40s/it]
 96%|█████████▌| 3481/3640 [1:19:56<03:41,  1.39s/it]
 96%|█████████▌| 3482/3640 [1:19:57<03:38,  1.38s/it]
 96%|█████████▌| 3483/3640 [1:19:58<03:35,  1.37s/it]
 96%|█████████▌| 3484/3640 [1:20:00<03:33,  1.37s/it]
 96%|█████████▌| 3485/3640 [1:20:01<03:32,  1.37s/it]
 96%|█████████▌| 3486/3640 [1:20:03<03:31,  1.37s/it]
 96%|█████████▌| 3487/3640 [1:20:04<03:29,  1.37s/it]
 96%|█████████▌| 3488/3640 [1:20:05<03:27,  1.37s/it]
 96%|█████████▌| 3489/3640 [1:20:07<03:26,  1.37s/it]
 96%|█████████▌| 3490/3640 [1:20:08<03:24,  1.36s/it]
 96%|█████████▌| 3491/3640 [1:20:09<03:23,  1.36s/it]
 96%|█████████▌| 3492/3640 [1:20:11<03:21,  1.36s/it]
 96%|█████████▌| 3493/3640 [1:20:12<03:20,  1.36s/it]
 96%|█████████▌| 3494/3640 [1:20:13<03:19,  1.36s/it]
 96%|█████████▌| 3495/3640 [1:20:15<03:17,  1.36s/it]
 96%|█████████▌| 3496/3640 [1:20:16<03:16,  1.36s/it]
 96%|█████████▌| 3497/3640 [1:20:18<03:15,  1.37s/it]
 96%|█████████▌| 3498/3640 [1:20:19<03:13,  1.37s/it]
 96%|█████████▌| 3499/3640 [1:20:20<03:12,  1.37s/it]
 96%|█████████▌| 3500/3640 [1:20:22<03:11,  1.37s/it]
                                                     

 96%|█████████▌| 3500/3640 [1:20:22<03:11,  1.37s/it]
 96%|█████████▌| 3501/3640 [1:20:23<03:11,  1.38s/it]
 96%|█████████▌| 3502/3640 [1:20:24<03:09,  1.38s/it]
 96%|█████████▌| 3503/3640 [1:20:26<03:08,  1.37s/it]
 96%|█████████▋| 3504/3640 [1:20:27<03:07,  1.38s/it]
 96%|█████████▋| 3505/3640 [1:20:29<03:05,  1.38s/it]
 96%|█████████▋| 3506/3640 [1:20:30<03:04,  1.37s/it]
 96%|█████████▋| 3507/3640 [1:20:31<03:02,  1.37s/it]
 96%|█████████▋| 3508/3640 [1:20:33<03:00,  1.37s/it]
 96%|█████████▋| 3509/3640 [1:20:34<02:59,  1.37s/it]
 96%|█████████▋| 3510/3640 [1:20:35<02:57,  1.37s/it]
 96%|█████████▋| 3511/3640 [1:20:37<02:56,  1.37s/it]
 96%|█████████▋| 3512/3640 [1:20:38<02:55,  1.37s/it]
 97%|█████████▋| 3513/3640 [1:20:39<02:54,  1.37s/it]
 97%|█████████▋| 3514/3640 [1:20:41<02:52,  1.37s/it]
 97%|█████████▋| 3515/3640 [1:20:42<02:51,  1.37s/it]
 97%|█████████▋| 3516/3640 [1:20:44<02:49,  1.37s/it]
 97%|█████████▋| 3517/3640 [1:20:45<02:48,  1.37s/it]
 97%|█████████▋| 3518/3640 [1:20:46<02:47,  1.37s/it]
 97%|█████████▋| 3519/3640 [1:20:48<02:45,  1.37s/it]
 97%|█████████▋| 3520/3640 [1:20:49<02:44,  1.37s/it]
 97%|█████████▋| 3521/3640 [1:20:50<02:43,  1.37s/it]
 97%|█████████▋| 3522/3640 [1:20:52<02:41,  1.37s/it]
 97%|█████████▋| 3523/3640 [1:20:53<02:40,  1.37s/it]
 97%|█████████▋| 3524/3640 [1:20:55<02:38,  1.37s/it]
 97%|█████████▋| 3525/3640 [1:20:56<02:37,  1.37s/it]
                                                     

 97%|█████████▋| 3525/3640 [1:20:56<02:37,  1.37s/it]
 97%|█████████▋| 3526/3640 [1:20:57<02:37,  1.38s/it]
 97%|█████████▋| 3527/3640 [1:20:59<02:35,  1.38s/it]
 97%|█████████▋| 3528/3640 [1:21:00<02:34,  1.38s/it]
 97%|█████████▋| 3529/3640 [1:21:01<02:32,  1.38s/it]
 97%|█████████▋| 3530/3640 [1:21:03<02:31,  1.37s/it]
 97%|█████████▋| 3531/3640 [1:21:04<02:29,  1.37s/it]
 97%|█████████▋| 3532/3640 [1:21:06<02:28,  1.37s/it]
 97%|█████████▋| 3533/3640 [1:21:07<02:26,  1.37s/it]
 97%|█████████▋| 3534/3640 [1:21:08<02:25,  1.37s/it]
 97%|█████████▋| 3535/3640 [1:21:10<02:23,  1.37s/it]
 97%|█████████▋| 3536/3640 [1:21:11<02:22,  1.37s/it]
 97%|█████████▋| 3537/3640 [1:21:12<02:20,  1.37s/it]
 97%|█████████▋| 3538/3640 [1:21:14<02:19,  1.37s/it]
 97%|█████████▋| 3539/3640 [1:21:15<02:18,  1.37s/it]
 97%|█████████▋| 3540/3640 [1:21:16<02:16,  1.37s/it]
 97%|█████████▋| 3541/3640 [1:21:18<02:15,  1.37s/it]
 97%|█████████▋| 3542/3640 [1:21:19<02:14,  1.37s/it]
 97%|█████████▋| 3543/3640 [1:21:21<02:12,  1.37s/it]
 97%|█████████▋| 3544/3640 [1:21:22<02:11,  1.37s/it]
 97%|█████████▋| 3545/3640 [1:21:23<02:10,  1.37s/it]
 97%|█████████▋| 3546/3640 [1:21:25<02:08,  1.37s/it]
 97%|█████████▋| 3547/3640 [1:21:26<02:07,  1.37s/it]
 97%|█████████▋| 3548/3640 [1:21:27<02:05,  1.37s/it]
 98%|█████████▊| 3549/3640 [1:21:29<02:04,  1.37s/it]
 98%|█████████▊| 3550/3640 [1:21:30<02:02,  1.36s/it]
                                                     

 98%|█████████▊| 3550/3640 [1:21:30<02:02,  1.36s/it]
 98%|█████████▊| 3551/3640 [1:21:32<02:02,  1.37s/it]
 98%|█████████▊| 3552/3640 [1:21:33<02:01,  1.38s/it]
 98%|█████████▊| 3553/3640 [1:21:34<01:59,  1.38s/it]
 98%|█████████▊| 3554/3640 [1:21:36<01:58,  1.37s/it]
 98%|█████████▊| 3555/3640 [1:21:37<01:56,  1.37s/it]
 98%|█████████▊| 3556/3640 [1:21:38<01:55,  1.37s/it]
 98%|█████████▊| 3557/3640 [1:21:40<01:53,  1.37s/it]
 98%|█████████▊| 3558/3640 [1:21:41<01:52,  1.37s/it]
 98%|█████████▊| 3559/3640 [1:21:43<01:50,  1.37s/it]
 98%|█████████▊| 3560/3640 [1:21:44<01:49,  1.37s/it]
 98%|█████████▊| 3561/3640 [1:21:45<01:48,  1.37s/it]
 98%|█████████▊| 3562/3640 [1:21:47<01:46,  1.37s/it]
 98%|█████████▊| 3563/3640 [1:21:48<01:45,  1.37s/it]
 98%|█████████▊| 3564/3640 [1:21:49<01:44,  1.37s/it]
 98%|█████████▊| 3565/3640 [1:21:51<01:42,  1.37s/it]
 98%|█████████▊| 3566/3640 [1:21:52<01:41,  1.37s/it]
 98%|█████████▊| 3567/3640 [1:21:53<01:39,  1.37s/it]
 98%|█████████▊| 3568/3640 [1:21:55<01:38,  1.37s/it]
 98%|█████████▊| 3569/3640 [1:21:56<01:37,  1.37s/it]
 98%|█████████▊| 3570/3640 [1:21:58<01:35,  1.37s/it]
 98%|█████████▊| 3571/3640 [1:21:59<01:34,  1.37s/it]
 98%|█████████▊| 3572/3640 [1:22:00<01:33,  1.37s/it]
 98%|█████████▊| 3573/3640 [1:22:02<01:31,  1.37s/it]
 98%|█████████▊| 3574/3640 [1:22:03<01:30,  1.36s/it]
 98%|█████████▊| 3575/3640 [1:22:04<01:28,  1.36s/it]
                                                     

 98%|█████████▊| 3575/3640 [1:22:04<01:28,  1.36s/it]
 98%|█████████▊| 3576/3640 [1:22:06<01:27,  1.37s/it]
 98%|█████████▊| 3577/3640 [1:22:07<01:26,  1.37s/it]
 98%|█████████▊| 3578/3640 [1:22:09<01:25,  1.37s/it]
 98%|█████████▊| 3579/3640 [1:22:10<01:23,  1.37s/it]
 98%|█████████▊| 3580/3640 [1:22:11<01:22,  1.37s/it]
 98%|█████████▊| 3581/3640 [1:22:13<01:24,  1.43s/it]
 98%|█████████▊| 3582/3640 [1:22:14<01:21,  1.41s/it]
 98%|█████████▊| 3583/3640 [1:22:16<01:19,  1.40s/it]
 98%|█████████▊| 3584/3640 [1:22:17<01:17,  1.39s/it]
 98%|█████████▊| 3585/3640 [1:22:18<01:16,  1.38s/it]
 99%|█████████▊| 3586/3640 [1:22:20<01:14,  1.38s/it]
 99%|█████████▊| 3587/3640 [1:22:21<01:12,  1.37s/it]
 99%|█████████▊| 3588/3640 [1:22:22<01:11,  1.37s/it]
 99%|█████████▊| 3589/3640 [1:22:24<01:09,  1.37s/it]
 99%|█████████▊| 3590/3640 [1:22:25<01:08,  1.37s/it]
 99%|█████████▊| 3591/3640 [1:22:26<01:06,  1.36s/it]
 99%|█████████▊| 3592/3640 [1:22:28<01:05,  1.36s/it]
 99%|█████████▊| 3593/3640 [1:22:29<01:03,  1.36s/it]
 99%|█████████▊| 3594/3640 [1:22:31<01:02,  1.36s/it]
 99%|█████████▉| 3595/3640 [1:22:32<01:01,  1.36s/it]
 99%|█████████▉| 3596/3640 [1:22:33<00:59,  1.36s/it]
 99%|█████████▉| 3597/3640 [1:22:35<00:58,  1.36s/it]
 99%|█████████▉| 3598/3640 [1:22:36<00:56,  1.36s/it]
 99%|█████████▉| 3599/3640 [1:22:37<00:55,  1.36s/it]
 99%|█████████▉| 3600/3640 [1:22:39<00:54,  1.37s/it]
                                                     

 99%|█████████▉| 3600/3640 [1:22:39<00:54,  1.37s/it]
 99%|█████████▉| 3601/3640 [1:22:40<00:54,  1.39s/it]
 99%|█████████▉| 3602/3640 [1:22:42<00:52,  1.38s/it]
 99%|█████████▉| 3603/3640 [1:22:43<00:50,  1.37s/it]
 99%|█████████▉| 3604/3640 [1:22:44<00:49,  1.37s/it]
 99%|█████████▉| 3605/3640 [1:22:46<00:47,  1.37s/it]
 99%|█████████▉| 3606/3640 [1:22:47<00:46,  1.37s/it]
 99%|█████████▉| 3607/3640 [1:22:48<00:45,  1.37s/it]
 99%|█████████▉| 3608/3640 [1:22:50<00:43,  1.37s/it]
 99%|█████████▉| 3609/3640 [1:22:51<00:42,  1.37s/it]
 99%|█████████▉| 3610/3640 [1:22:53<00:41,  1.37s/it]
 99%|█████████▉| 3611/3640 [1:22:54<00:39,  1.37s/it]
 99%|█████████▉| 3612/3640 [1:22:55<00:38,  1.37s/it]
 99%|█████████▉| 3613/3640 [1:22:57<00:36,  1.36s/it]
 99%|█████████▉| 3614/3640 [1:22:58<00:35,  1.37s/it]
 99%|█████████▉| 3615/3640 [1:22:59<00:34,  1.36s/it]
 99%|█████████▉| 3616/3640 [1:23:01<00:32,  1.36s/it]
 99%|█████████▉| 3617/3640 [1:23:02<00:31,  1.37s/it]
 99%|█████████▉| 3618/3640 [1:23:03<00:30,  1.37s/it]
 99%|█████████▉| 3619/3640 [1:23:05<00:28,  1.37s/it]
 99%|█████████▉| 3620/3640 [1:23:06<00:27,  1.37s/it]
 99%|█████████▉| 3621/3640 [1:23:08<00:26,  1.37s/it]
100%|█████████▉| 3622/3640 [1:23:09<00:24,  1.37s/it]
100%|█████████▉| 3623/3640 [1:23:10<00:23,  1.37s/it]
100%|█████████▉| 3624/3640 [1:23:12<00:21,  1.37s/it]
100%|█████████▉| 3625/3640 [1:23:13<00:20,  1.37s/it]
                                                     

100%|█████████▉| 3625/3640 [1:23:13<00:20,  1.37s/it]
100%|█████████▉| 3626/3640 [1:23:14<00:19,  1.38s/it]
100%|█████████▉| 3627/3640 [1:23:16<00:17,  1.38s/it]
100%|█████████▉| 3628/3640 [1:23:17<00:16,  1.37s/it]
100%|█████████▉| 3629/3640 [1:23:19<00:15,  1.37s/it]
100%|█████████▉| 3630/3640 [1:23:20<00:13,  1.37s/it]
100%|█████████▉| 3631/3640 [1:23:21<00:12,  1.37s/it]
100%|█████████▉| 3632/3640 [1:23:23<00:10,  1.37s/it]
100%|█████████▉| 3633/3640 [1:23:24<00:09,  1.37s/it]
100%|█████████▉| 3634/3640 [1:23:25<00:08,  1.37s/it]
100%|█████████▉| 3635/3640 [1:23:27<00:06,  1.37s/it]
100%|█████████▉| 3636/3640 [1:23:28<00:05,  1.36s/it]
100%|█████████▉| 3637/3640 [1:23:29<00:04,  1.37s/it]
100%|█████████▉| 3638/3640 [1:23:31<00:02,  1.37s/it]
100%|█████████▉| 3639/3640 [1:23:32<00:01,  1.37s/it]
100%|██████████| 3640/3640 [1:23:34<00:00,  1.36s/it]Saving model checkpoint to data/output/mistral_lora_moe_cybersecurity_top1/checkpoint-3640
Configuration saved in data/output/mistral_lora_moe_cybersecurity_top1/checkpoint-3640/config.json
Configuration saved in data/output/mistral_lora_moe_cybersecurity_top1/checkpoint-3640/generation_config.json
The model is bigger than the maximum size per checkpoint (5GB) and is going to be split in 3 checkpoint shards. You can find where each parameters has been saved in the index located at data/output/mistral_lora_moe_cybersecurity_top1/checkpoint-3640/model.safetensors.index.json.
tokenizer config file saved in data/output/mistral_lora_moe_cybersecurity_top1/checkpoint-3640/tokenizer_config.json
Special tokens file saved in data/output/mistral_lora_moe_cybersecurity_top1/checkpoint-3640/special_tokens_map.json
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once


Training completed. Do not forget to share your model on huggingface.co/models =)



                                                     

100%|██████████| 3640/3640 [1:25:33<00:00,  1.36s/it]
100%|██████████| 3640/3640 [1:25:33<00:00,  1.41s/it]

***** Running Evaluation *****
  Num examples = 194
  Batch size = 1

  0%|          | 0/49 [00:00<?, ?it/s]
  4%|▍         | 2/49 [00:00<00:02, 17.26it/s]
  8%|▊         | 4/49 [00:00<00:04, 10.62it/s]
 12%|█▏        | 6/49 [00:00<00:04,  9.48it/s]
 16%|█▋        | 8/49 [00:00<00:04,  9.00it/s]
 18%|█▊        | 9/49 [00:00<00:04,  8.87it/s]
 20%|██        | 10/49 [00:01<00:04,  8.76it/s]
 22%|██▏       | 11/49 [00:01<00:04,  8.65it/s]
 24%|██▍       | 12/49 [00:01<00:04,  8.60it/s]
 27%|██▋       | 13/49 [00:01<00:04,  8.49it/s]
 29%|██▊       | 14/49 [00:01<00:04,  8.38it/s]
 31%|███       | 15/49 [00:01<00:04,  8.37it/s]
 33%|███▎      | 16/49 [00:01<00:03,  8.33it/s]
 35%|███▍      | 17/49 [00:01<00:03,  8.37it/s]
 37%|███▋      | 18/49 [00:02<00:03,  8.38it/s]
 39%|███▉      | 19/49 [00:02<00:03,  8.36it/s]
 41%|████      | 20/49 [00:02<00:03,  8.29it/s]
 43%|████▎     | 21/49 [00:02<00:03,  8.29it/s]
 45%|████▍     | 22/49 [00:02<00:03,  8.31it/s]
 47%|████▋     | 23/49 [00:02<00:03,  8.38it/s]
 49%|████▉     | 24/49 [00:02<00:02,  8.36it/s]
 51%|█████     | 25/49 [00:02<00:02,  8.41it/s]
 53%|█████▎    | 26/49 [00:02<00:02,  8.42it/s]
 55%|█████▌    | 27/49 [00:03<00:02,  8.40it/s]
 57%|█████▋    | 28/49 [00:03<00:02,  8.36it/s]
 59%|█████▉    | 29/49 [00:03<00:02,  8.36it/s]
 61%|██████    | 30/49 [00:03<00:02,  8.40it/s]
 63%|██████▎   | 31/49 [00:03<00:02,  8.43it/s]
 65%|██████▌   | 32/49 [00:03<00:02,  8.35it/s]
 67%|██████▋   | 33/49 [00:03<00:01,  8.38it/s]
 69%|██████▉   | 34/49 [00:03<00:01,  8.29it/s]
 71%|███████▏  | 35/49 [00:04<00:01,  8.27it/s]
 73%|███████▎  | 36/49 [00:04<00:01,  8.34it/s]
 76%|███████▌  | 37/49 [00:04<00:01,  8.36it/s]
 78%|███████▊  | 38/49 [00:04<00:01,  8.36it/s]
 80%|███████▉  | 39/49 [00:04<00:01,  8.35it/s]
 82%|████████▏ | 40/49 [00:04<00:01,  8.34it/s]
 84%|████████▎ | 41/49 [00:04<00:00,  8.29it/s]
 86%|████████▌ | 42/49 [00:04<00:00,  8.29it/s]
 88%|████████▊ | 43/49 [00:05<00:00,  8.33it/s]
 90%|████████▉ | 44/49 [00:05<00:00,  8.34it/s]
 92%|█████████▏| 45/49 [00:05<00:00,  8.22it/s]
 94%|█████████▍| 46/49 [00:05<00:00,  8.17it/s]
 96%|█████████▌| 47/49 [00:05<00:00,  8.23it/s]
 98%|█████████▊| 48/49 [00:05<00:00,  8.22it/s]
100%|██████████| 49/49 [00:05<00:00,  8.24it/s]Error executing job with overrides: []
Error executing job with overrides: []
Error executing job with overrides: []
Traceback (most recent call last):
Traceback (most recent call last):

100%|██████████| 49/49 [00:05<00:00,  8.44it/s]Traceback (most recent call last):
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 215, in main
    eval_results = trainer.evaluate()

  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 215, in main
    eval_results = trainer.evaluate()
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer.py", line 3999, in evaluate
    self.log(output.metrics)
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 215, in main
    eval_results = trainer.evaluate()
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer.py", line 3999, in evaluate
    self.log(output.metrics)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer.py", line 3495, in log
    self.control = self.callback_handler.on_log(self.args, self.state, self.control, logs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer.py", line 3999, in evaluate
    self.log(output.metrics)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer.py", line 3495, in log
    self.control = self.callback_handler.on_log(self.args, self.state, self.control, logs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer_callback.py", line 511, in on_log
    return self.call_event("on_log", args, state, control, logs=logs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer.py", line 3495, in log
    self.control = self.callback_handler.on_log(self.args, self.state, self.control, logs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer_callback.py", line 511, in on_log
    return self.call_event("on_log", args, state, control, logs=logs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer_callback.py", line 511, in on_log
    return self.call_event("on_log", args, state, control, logs=logs)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer_callback.py", line 518, in call_event
    result = getattr(callback, event)(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer_callback.py", line 518, in call_event
    result = getattr(callback, event)(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/trainer_callback.py", line 518, in call_event
    result = getattr(callback, event)(
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 136, in on_log
    wandb.log(eval_logs, step=state.global_step)
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 136, in on_log
    wandb.log(eval_logs, step=state.global_step)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/wandb/sdk/lib/preinit.py", line 36, in preinit_wrapper
    raise wandb.Error(f"You must call wandb.init() before {name}()")
  File "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/exmp_1.py", line 136, in on_log
    wandb.log(eval_logs, step=state.global_step)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/wandb/sdk/lib/preinit.py", line 36, in preinit_wrapper
    raise wandb.Error(f"You must call wandb.init() before {name}()")
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/wandb/sdk/lib/preinit.py", line 36, in preinit_wrapper
    raise wandb.Error(f"You must call wandb.init() before {name}()")
wandb.errors.errors.Error: You must call wandb.init() before wandb.log()
wandb.errors.errors.Error: You must call wandb.init() before wandb.log()
wandb.errors.errors.Error: You must call wandb.init() before wandb.log()

Set the environment variable HYDRA_FULL_ERROR=1 for a complete stack trace.

Set the environment variable HYDRA_FULL_ERROR=1 for a complete stack trace.

Set the environment variable HYDRA_FULL_ERROR=1 for a complete stack trace.
wandb:                                                                                
wandb: 
wandb: Run history:
wandb:             dataset_test_size ▁
wandb:            dataset_train_size ▁
wandb:              dataset_val_size ▁
wandb:                     eval/loss ▁
wandb:                  eval/runtime ▁
wandb:       eval/samples_per_second ▁
wandb:         eval/steps_per_second ▁
wandb:                     eval_loss ▁
wandb:                  eval_runtime ▁
wandb:       eval_samples_per_second ▁
wandb:         eval_steps_per_second ▁
wandb:                   final_epoch ▁
wandb:               final_eval_loss ▁
wandb:            final_eval_runtime ▁
wandb: final_eval_samples_per_second ▁
wandb:   final_eval_steps_per_second ▁
wandb:              model_parameters ▁
wandb:              n_router_weights ▁
wandb:                     n_weights ▁
wandb:                   train/epoch ▁▁▁▁▁▁▂▂▂▃▃▄▄▄▄▄▅▅▅▅▅▅▅▅▆▆▆▆▆▇▇▇▇▇▇█████
wandb:             train/global_step ▁▁▁▁▁▂▂▂▂▂▂▃▃▃▃▃▃▃▄▄▄▄▅▅▅▅▆▆▆▆▆▆▇▇▇▇████
wandb:               train/grad_norm ▇▇▇█▆▅▅▅▅▅▅▄▇▅▄▅▄▅▄▃▄▅▂▃▃▃▃▃▃▃▂▃▂▂▁▃▁▁▁▁
wandb:           train/learning_rate ██▇▇▇▇▇▇▇▇▆▆▆▆▆▆▅▅▅▅▅▅▅▅▅▃▃▃▃▃▃▂▂▂▂▂▁▁▁▁
wandb:                    train/loss █▆▅▅▄▄▄▄▄▃▃▃▂▂▂▂▂▂▂▂▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:          trainable_parameters ▁
wandb:          trainable_percentage ▁
wandb: 
wandb: Run summary:
wandb:             dataset_test_size 455
wandb:            dataset_train_size 5834
wandb:              dataset_val_size 194
wandb:                     eval/loss 0.04417
wandb:                  eval/runtime 5.914
wandb:       eval/samples_per_second 32.804
wandb:         eval/steps_per_second 8.285
wandb:                     eval_loss 0.04417
wandb:                  eval_runtime 5.914
wandb:       eval_samples_per_second 32.804
wandb:         eval_steps_per_second 8.285
wandb:                   final_epoch 9.98561
wandb:               final_eval_loss 0.04417
wandb:            final_eval_runtime 5.914
wandb: final_eval_samples_per_second 32.804
wandb:   final_eval_steps_per_second 8.285
wandb:              model_parameters 7356420096
wandb:              n_router_weights 256
wandb:                     n_weights 771
wandb:                    total_flos 1.0862477480233533e+18
wandb:                   train/epoch 9.98561
wandb:             train/global_step 3640
wandb:               train/grad_norm 0.12938
wandb:           train/learning_rate 0.0
wandb:                    train/loss 0.0299
wandb:                    train_loss 0.28572
wandb:                 train_runtime 5133.2839
wandb:      train_samples_per_second 11.365
wandb:        train_steps_per_second 0.709
wandb:          trainable_parameters 1918238720
wandb:          trainable_percentage 26.07571
wandb: 
wandb: 🚀 View run mistral-cybersecurity-moe at: https://wandb.ai/sarath-chandar/mistral-moe-training/runs/2l3a0nfa
wandb: ⭐️ View project at: https://wandb.ai/sarath-chandar/mistral-moe-training
wandb: Synced 5 W&B file(s), 0 media file(s), 0 artifact file(s) and 0 other file(s)
wandb: Find logs at: ./wandb/run-20250707_160842-2l3a0nfa/logs
[rank3]:[E707 17:44:42.722660593 ProcessGroupNCCL.cpp:1746] [PG ID 0 PG GUID 0(default_pg) Rank 3] ProcessGroupNCCL's watchdog got stuck for 480 seconds without making progress in monitoring enqueued collectives. This typically indicates a NCCL/CUDA API (e.g., CudaEventDestroy) hang blocking the watchdog, and could be triggered by another thread holding the GIL inside a CUDA api (for example, CudaEventDestroy), or other deadlock-prone behaviors.If you suspect the watchdog is not actually stuck and a longer timeout would help, you can either increase the timeout (TORCH_NCCL_HEARTBEAT_TIMEOUT_SEC) to a larger value or disable the heartbeat monitor (TORCH_NCCL_ENABLE_MONITORING=0).If either of aforementioned helps, feel free to file an issue to PyTorch about the short timeout or false positive abort; otherwise, please attempt to debug the hang. 
[rank2]:[E707 17:44:42.722951222 ProcessGroupNCCL.cpp:1746] [PG ID 0 PG GUID 0(default_pg) Rank 2] ProcessGroupNCCL's watchdog got stuck for 480 seconds without making progress in monitoring enqueued collectives. This typically indicates a NCCL/CUDA API (e.g., CudaEventDestroy) hang blocking the watchdog, and could be triggered by another thread holding the GIL inside a CUDA api (for example, CudaEventDestroy), or other deadlock-prone behaviors.If you suspect the watchdog is not actually stuck and a longer timeout would help, you can either increase the timeout (TORCH_NCCL_HEARTBEAT_TIMEOUT_SEC) to a larger value or disable the heartbeat monitor (TORCH_NCCL_ENABLE_MONITORING=0).If either of aforementioned helps, feel free to file an issue to PyTorch about the short timeout or false positive abort; otherwise, please attempt to debug the hang. 
[rank1]:[E707 17:44:42.723057562 ProcessGroupNCCL.cpp:1746] [PG ID 0 PG GUID 0(default_pg) Rank 1] ProcessGroupNCCL's watchdog got stuck for 480 seconds without making progress in monitoring enqueued collectives. This typically indicates a NCCL/CUDA API (e.g., CudaEventDestroy) hang blocking the watchdog, and could be triggered by another thread holding the GIL inside a CUDA api (for example, CudaEventDestroy), or other deadlock-prone behaviors.If you suspect the watchdog is not actually stuck and a longer timeout would help, you can either increase the timeout (TORCH_NCCL_HEARTBEAT_TIMEOUT_SEC) to a larger value or disable the heartbeat monitor (TORCH_NCCL_ENABLE_MONITORING=0).If either of aforementioned helps, feel free to file an issue to PyTorch about the short timeout or false positive abort; otherwise, please attempt to debug the hang. 
[rank3]:[E707 17:44:42.728987301 ProcessGroupNCCL.cpp:1536] [PG ID 0 PG GUID 0(default_pg) Rank 3] ProcessGroupNCCL preparing to dump debug info. Include stack trace: 1
[rank2]:[E707 17:44:42.735340048 ProcessGroupNCCL.cpp:1536] [PG ID 0 PG GUID 0(default_pg) Rank 2] ProcessGroupNCCL preparing to dump debug info. Include stack trace: 1
[rank1]:[E707 17:44:42.739896856 ProcessGroupNCCL.cpp:1536] [PG ID 0 PG GUID 0(default_pg) Rank 1] ProcessGroupNCCL preparing to dump debug info. Include stack trace: 1
[rank2]:[F707 17:52:42.754285000 ProcessGroupNCCL.cpp:1557] [PG ID 0 PG GUID 0(default_pg) Rank 2] [PG ID 0 PG GUID 0(default_pg) Rank 2] Terminating the process after attempting to dump debug info, due to ProcessGroupNCCL watchdog hang.
[rank3]:[F707 17:52:42.754301150 ProcessGroupNCCL.cpp:1557] [PG ID 0 PG GUID 0(default_pg) Rank 3] [PG ID 0 PG GUID 0(default_pg) Rank 3] Terminating the process after attempting to dump debug info, due to ProcessGroupNCCL watchdog hang.
[rank1]:[F707 17:52:42.757528046 ProcessGroupNCCL.cpp:1557] [PG ID 0 PG GUID 0(default_pg) Rank 1] [PG ID 0 PG GUID 0(default_pg) Rank 1] Terminating the process after attempting to dump debug info, due to ProcessGroupNCCL watchdog hang.
W0707 17:52:43.858000 2855285 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 2855407 closing signal SIGTERM
W0707 17:52:43.867000 2855285 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 2855408 closing signal SIGTERM
W0707 17:52:43.872000 2855285 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 2855410 closing signal SIGTERM
E0707 17:52:44.693000 2855285 torch/distributed/elastic/multiprocessing/api.py:874] failed (exitcode: -6) local_rank: 2 (pid: 2855409) of binary: /home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/bin/python
Traceback (most recent call last):
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/bin/accelerate", line 8, in <module>
    sys.exit(main())
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/accelerate/commands/accelerate_cli.py", line 50, in main
    args.func(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/accelerate/commands/launch.py", line 1183, in launch_command
    deepspeed_launcher(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/accelerate/commands/launch.py", line 868, in deepspeed_launcher
    distrib_run.run(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/run.py", line 883, in run
    elastic_launch(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 139, in __call__
    return launch_agent(self._config, self._entrypoint, list(args))
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 270, in launch_agent
    raise ChildFailedError(
torch.distributed.elastic.multiprocessing.errors.ChildFailedError: 
========================================================
exmp_1.py FAILED
--------------------------------------------------------
Failures:
  <NO_OTHER_FAILURES>
--------------------------------------------------------
Root Cause (first observed failure):
[0]:
  time      : 2025-07-07_17:52:43
  host      : cn-g020.server.mila.quebec
  rank      : 2 (local_rank: 2)
  exitcode  : -6 (pid: 2855409)
  error_file: <N/A>
  traceback : Signal 6 (SIGABRT) received by PID 2855409
========================================================

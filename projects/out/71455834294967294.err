[=== Module python/3.10 loaded ===]
[=== Module cudatoolkit/12.1.1 loaded ===]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
W0707 18:20:50.163000 3813190 torch/distributed/run.py:766] 
W0707 18:20:50.163000 3813190 torch/distributed/run.py:766] *****************************************
W0707 18:20:50.163000 3813190 torch/distributed/run.py:766] Setting OMP_NUM_THREADS environment variable for each process to be 1 in default, to avoid your system being overloaded, please further tune the variable for optimal performance in your application as needed. 
W0707 18:20:50.163000 3813190 torch/distributed/run.py:766] *****************************************
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
wandb: Currently logged in as: maryamha (sarath-chandar) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.20.1
wandb: Run data is saved locally in /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/run-20250707_182101-2wjetsf6
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run mistral-economic-moe
wandb: ⭐️ View project at https://wandb.ai/sarath-chandar/mistral-moe-training
wandb: 🚀 View run at https://wandb.ai/sarath-chandar/mistral-moe-training/runs/2wjetsf6

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:13<00:13, 13.24s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:13<00:13, 13.65s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:13<00:13, 13.97s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:13<00:13, 13.97s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  8.97s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.73s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.27s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.01s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.14s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.87s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.71s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.88s/it]



  0%|          | 0/675 [00:00<?, ?it/s]
100%|██████████| 675/675 [00:00<00:00, 99541.35it/s]
  0%|          | 0/675 [00:00<?, ?it/s]
  0%|          | 0/675 [00:00<?, ?it/s]
  0%|          | 0/675 [00:00<?, ?it/s]

100%|██████████| 675/675 [00:00<00:00, 44349.75it/s]
100%|██████████| 675/675 [00:00<00:00, 47166.27it/s]
100%|██████████| 675/675 [00:00<00:00, 50077.92it/s]


Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:08<00:08,  8.43s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:09<00:09,  9.25s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:10<00:10, 10.10s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:08<00:08,  8.65s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:14<00:00,  7.11s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:14<00:00,  7.32s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_economic_top1 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.mlp.up_proj.gate.weight', 'model.layers.8.mlp.down_proj.gate.weight', 'model.layers.8.mlp.gate_proj.gate.weight', 'model.layers.8.mlp.up_proj.gate.weight', 'model.layers.9.mlp.down_proj.gate.weight', 'model.layers.9.mlp.gate_proj.gate.weight', 'model.layers.9.mlp.up_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.

Generating train split: 0 examples [00:00, ? examples/s]
Loading checkpoint shards: 100%|██████████| 2/2 [00:15<00:00,  7.31s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  7.66s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:15<00:00,  7.60s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:14<00:00,  7.06s/it]Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_economic_top1 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.mlp.up_proj.gate.weight', 'model.layers.8.mlp.down_proj.gate.weight', 'model.layers.8.mlp.gate_proj.gate.weight', 'model.layers.8.mlp.up_proj.gate.weight', 'model.layers.9.mlp.down_proj.gate.weight', 'model.layers.9.mlp.gate_proj.gate.weight', 'model.layers.9.mlp.up_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.

Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  8.03s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:14<00:00,  7.31s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_economic_top1 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.mlp.up_proj.gate.weight', 'model.layers.8.mlp.down_proj.gate.weight', 'model.layers.8.mlp.gate_proj.gate.weight', 'model.layers.8.mlp.up_proj.gate.weight', 'model.layers.9.mlp.down_proj.gate.weight', 'model.layers.9.mlp.gate_proj.gate.weight', 'model.layers.9.mlp.up_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_economic_top1 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.mlp.up_proj.gate.weight', 'model.layers.8.mlp.down_proj.gate.weight', 'model.layers.8.mlp.gate_proj.gate.weight', 'model.layers.8.mlp.up_proj.gate.weight', 'model.layers.9.mlp.down_proj.gate.weight', 'model.layers.9.mlp.gate_proj.gate.weight', 'model.layers.9.mlp.up_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.

Generating train split: 4104 examples [00:00, 26233.70 examples/s]
Generating train split: 4104 examples [00:00, 24608.96 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank2]:[W707 18:22:47.601518094 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 2]  using GPU 2 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
[rank3]:[W707 18:22:47.603571826 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 3]  using GPU 3 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(

Map:   0%|          | 0/2000 [00:00<?, ? examples/s]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank1]:[W707 18:22:47.658836892 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 1]  using GPU 1 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.

Map:  50%|█████     | 1000/2000 [00:00<00:00, 4666.45 examples/s]
Map: 100%|██████████| 2000/2000 [00:00<00:00, 5066.72 examples/s]
Map: 100%|██████████| 2000/2000 [00:00<00:00, 4788.60 examples/s]

Map:   0%|          | 0/123 [00:00<?, ? examples/s]
Map: 100%|██████████| 123/123 [00:00<00:00, 3418.14 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank0]:[W707 18:22:47.142133361 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 0]  using GPU 0 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(

Map:   0%|          | 0/2000 [00:00<?, ? examples/s]
Map:   0%|          | 0/2000 [00:00<?, ? examples/s]
Map:   0%|          | 0/2000 [00:00<?, ? examples/s]Using auto half precision backend

Map:  50%|█████     | 1000/2000 [00:00<00:00, 1828.44 examples/s]
Map:  50%|█████     | 1000/2000 [00:00<00:00, 1702.25 examples/s]
Map:  50%|█████     | 1000/2000 [00:00<00:00, 1697.93 examples/s]
Map: 100%|██████████| 2000/2000 [00:01<00:00, 1952.30 examples/s]
Map: 100%|██████████| 2000/2000 [00:01<00:00, 1895.75 examples/s]

Map: 100%|██████████| 2000/2000 [00:01<00:00, 1891.87 examples/s]
Map:   0%|          | 0/123 [00:00<?, ? examples/s]
Map: 100%|██████████| 2000/2000 [00:01<00:00, 1851.28 examples/s]
Map: 100%|██████████| 2000/2000 [00:01<00:00, 1829.78 examples/s]

Map: 100%|██████████| 2000/2000 [00:01<00:00, 1790.06 examples/s]

Map: 100%|██████████| 123/123 [00:00<00:00, 3001.44 examples/s]

Map:   0%|          | 0/123 [00:00<?, ? examples/s]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(

Map:   0%|          | 0/123 [00:00<?, ? examples/s]
Map: 100%|██████████| 123/123 [00:00<00:00, 2135.57 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(

Map: 100%|██████████| 123/123 [00:00<00:00, 2237.40 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(
***** Running training *****
  Num examples = 2,000
  Num Epochs = 10
  Instantaneous batch size per device = 1
  Total train batch size (w. parallel, distributed & accumulation) = 16
  Gradient Accumulation steps = 4
  Total optimization steps = 1,250
  Number of trainable parameters = 1,918,238,720
Automatic Weights & Biases logging enabled, to disable set os.environ["WANDB_DISABLED"] = "true"

  0%|          | 0/1250 [00:00<?, ?it/s]
  0%|          | 1/1250 [00:02<43:02,  2.07s/it]
                                                

  0%|          | 1/1250 [00:02<43:02,  2.07s/it]
  0%|          | 2/1250 [00:03<35:19,  1.70s/it]
  0%|          | 3/1250 [00:04<32:30,  1.56s/it]
  0%|          | 4/1250 [00:06<31:11,  1.50s/it]
  0%|          | 5/1250 [00:07<30:31,  1.47s/it]
  0%|          | 6/1250 [00:09<30:02,  1.45s/it]
  1%|          | 7/1250 [00:10<29:42,  1.43s/it]
  1%|          | 8/1250 [00:11<29:30,  1.43s/it]
  1%|          | 9/1250 [00:13<29:23,  1.42s/it]
  1%|          | 10/1250 [00:14<29:17,  1.42s/it]
  1%|          | 11/1250 [00:16<29:14,  1.42s/it]
  1%|          | 12/1250 [00:17<29:10,  1.41s/it]
  1%|          | 13/1250 [00:19<29:08,  1.41s/it]
  1%|          | 14/1250 [00:20<29:02,  1.41s/it]
  1%|          | 15/1250 [00:21<29:00,  1.41s/it]
  1%|▏         | 16/1250 [00:23<28:55,  1.41s/it]
  1%|▏         | 17/1250 [00:24<28:48,  1.40s/it]
  1%|▏         | 18/1250 [00:26<28:50,  1.40s/it]
  2%|▏         | 19/1250 [00:27<28:44,  1.40s/it]
  2%|▏         | 20/1250 [00:28<28:47,  1.40s/it]
  2%|▏         | 21/1250 [00:30<28:45,  1.40s/it]
  2%|▏         | 22/1250 [00:31<28:42,  1.40s/it]
  2%|▏         | 23/1250 [00:33<28:45,  1.41s/it]
  2%|▏         | 24/1250 [00:34<28:46,  1.41s/it]
  2%|▏         | 25/1250 [00:35<28:44,  1.41s/it]
                                                 

  2%|▏         | 25/1250 [00:35<28:44,  1.41s/it]
  2%|▏         | 26/1250 [00:37<28:47,  1.41s/it]
  2%|▏         | 27/1250 [00:38<28:44,  1.41s/it]
  2%|▏         | 28/1250 [00:40<28:41,  1.41s/it]
  2%|▏         | 29/1250 [00:41<28:36,  1.41s/it]
  2%|▏         | 30/1250 [00:42<28:38,  1.41s/it]
  2%|▏         | 31/1250 [00:44<28:36,  1.41s/it]
  3%|▎         | 32/1250 [00:45<28:33,  1.41s/it]
  3%|▎         | 33/1250 [00:47<28:34,  1.41s/it]
  3%|▎         | 34/1250 [00:48<28:34,  1.41s/it]
  3%|▎         | 35/1250 [00:49<28:31,  1.41s/it]
  3%|▎         | 36/1250 [00:51<28:26,  1.41s/it]
  3%|▎         | 37/1250 [00:52<28:26,  1.41s/it]
  3%|▎         | 38/1250 [00:54<28:19,  1.40s/it]
  3%|▎         | 39/1250 [00:55<28:17,  1.40s/it]
  3%|▎         | 40/1250 [00:56<28:16,  1.40s/it]
  3%|▎         | 41/1250 [00:58<28:14,  1.40s/it]
  3%|▎         | 42/1250 [00:59<28:13,  1.40s/it]
  3%|▎         | 43/1250 [01:01<28:12,  1.40s/it]
  4%|▎         | 44/1250 [01:02<28:14,  1.40s/it]
  4%|▎         | 45/1250 [01:03<28:16,  1.41s/it]
  4%|▎         | 46/1250 [01:05<28:10,  1.40s/it]
  4%|▍         | 47/1250 [01:06<28:10,  1.41s/it]
  4%|▍         | 48/1250 [01:08<28:09,  1.41s/it]
  4%|▍         | 49/1250 [01:09<28:09,  1.41s/it]
  4%|▍         | 50/1250 [01:11<28:07,  1.41s/it]
                                                 

  4%|▍         | 50/1250 [01:11<28:07,  1.41s/it]
  4%|▍         | 51/1250 [01:12<28:08,  1.41s/it]
  4%|▍         | 52/1250 [01:13<28:04,  1.41s/it]
  4%|▍         | 53/1250 [01:15<28:02,  1.41s/it]
  4%|▍         | 54/1250 [01:16<28:02,  1.41s/it]
  4%|▍         | 55/1250 [01:18<27:58,  1.40s/it]
  4%|▍         | 56/1250 [01:19<27:57,  1.40s/it]
  5%|▍         | 57/1250 [01:20<27:47,  1.40s/it]
  5%|▍         | 58/1250 [01:22<27:42,  1.39s/it]
  5%|▍         | 59/1250 [01:23<27:46,  1.40s/it]
  5%|▍         | 60/1250 [01:25<27:47,  1.40s/it]
  5%|▍         | 61/1250 [01:26<27:48,  1.40s/it]
  5%|▍         | 62/1250 [01:27<27:46,  1.40s/it]
  5%|▌         | 63/1250 [01:29<27:45,  1.40s/it]
  5%|▌         | 64/1250 [01:30<27:45,  1.40s/it]
  5%|▌         | 65/1250 [01:32<27:46,  1.41s/it]
  5%|▌         | 66/1250 [01:33<27:42,  1.40s/it]
  5%|▌         | 67/1250 [01:34<27:40,  1.40s/it]
  5%|▌         | 68/1250 [01:36<27:42,  1.41s/it]
  6%|▌         | 69/1250 [01:37<27:43,  1.41s/it]
  6%|▌         | 70/1250 [01:39<27:41,  1.41s/it]
  6%|▌         | 71/1250 [01:40<27:39,  1.41s/it]
  6%|▌         | 72/1250 [01:41<27:35,  1.41s/it]
  6%|▌         | 73/1250 [01:43<27:36,  1.41s/it]
  6%|▌         | 74/1250 [01:44<28:47,  1.47s/it]
  6%|▌         | 75/1250 [01:46<28:24,  1.45s/it]
                                                 

  6%|▌         | 75/1250 [01:46<28:24,  1.45s/it]
  6%|▌         | 76/1250 [01:47<28:10,  1.44s/it]
  6%|▌         | 77/1250 [01:49<27:57,  1.43s/it]
  6%|▌         | 78/1250 [01:50<27:44,  1.42s/it]
  6%|▋         | 79/1250 [01:51<27:37,  1.42s/it]
  6%|▋         | 80/1250 [01:53<27:36,  1.42s/it]
  6%|▋         | 81/1250 [01:54<27:32,  1.41s/it]
  7%|▋         | 82/1250 [01:56<27:30,  1.41s/it]
  7%|▋         | 83/1250 [01:57<27:25,  1.41s/it]
  7%|▋         | 84/1250 [01:59<27:27,  1.41s/it]
  7%|▋         | 85/1250 [02:00<27:21,  1.41s/it]
  7%|▋         | 86/1250 [02:01<27:19,  1.41s/it]
  7%|▋         | 87/1250 [02:03<27:19,  1.41s/it]
  7%|▋         | 88/1250 [02:04<27:15,  1.41s/it]
  7%|▋         | 89/1250 [02:06<27:12,  1.41s/it]
  7%|▋         | 90/1250 [02:07<27:08,  1.40s/it]
  7%|▋         | 91/1250 [02:08<27:10,  1.41s/it]
  7%|▋         | 92/1250 [02:10<27:07,  1.41s/it]
  7%|▋         | 93/1250 [02:11<27:05,  1.41s/it]
  8%|▊         | 94/1250 [02:13<27:08,  1.41s/it]
  8%|▊         | 95/1250 [02:14<27:06,  1.41s/it]
  8%|▊         | 96/1250 [02:15<27:04,  1.41s/it]
  8%|▊         | 97/1250 [02:17<27:01,  1.41s/it]
  8%|▊         | 98/1250 [02:18<27:02,  1.41s/it]
  8%|▊         | 99/1250 [02:20<26:59,  1.41s/it]
  8%|▊         | 100/1250 [02:21<26:57,  1.41s/it]
                                                  

  8%|▊         | 100/1250 [02:21<26:57,  1.41s/it]
  8%|▊         | 101/1250 [02:22<26:58,  1.41s/it]
  8%|▊         | 102/1250 [02:24<26:54,  1.41s/it]
  8%|▊         | 103/1250 [02:25<26:47,  1.40s/it]
  8%|▊         | 104/1250 [02:27<26:48,  1.40s/it]
  8%|▊         | 105/1250 [02:28<26:47,  1.40s/it]
  8%|▊         | 106/1250 [02:29<26:46,  1.40s/it]
  9%|▊         | 107/1250 [02:31<26:46,  1.41s/it]
  9%|▊         | 108/1250 [02:32<26:38,  1.40s/it]
  9%|▊         | 109/1250 [02:34<26:41,  1.40s/it]
  9%|▉         | 110/1250 [02:35<26:43,  1.41s/it]
  9%|▉         | 111/1250 [02:36<26:42,  1.41s/it]
  9%|▉         | 112/1250 [02:38<26:33,  1.40s/it]
  9%|▉         | 113/1250 [02:39<26:33,  1.40s/it]
  9%|▉         | 114/1250 [02:41<26:32,  1.40s/it]
  9%|▉         | 115/1250 [02:42<26:33,  1.40s/it]
  9%|▉         | 116/1250 [02:43<26:33,  1.41s/it]
  9%|▉         | 117/1250 [02:45<26:30,  1.40s/it]
  9%|▉         | 118/1250 [02:46<26:30,  1.41s/it]
 10%|▉         | 119/1250 [02:48<26:29,  1.41s/it]
 10%|▉         | 120/1250 [02:49<26:29,  1.41s/it]
 10%|▉         | 121/1250 [02:51<26:26,  1.41s/it]
 10%|▉         | 122/1250 [02:52<26:24,  1.41s/it]
 10%|▉         | 123/1250 [02:53<26:26,  1.41s/it]
 10%|▉         | 124/1250 [02:55<26:22,  1.41s/it]
 10%|█         | 125/1250 [02:56<26:17,  1.40s/it]
                                                  

 10%|█         | 125/1250 [02:56<26:17,  1.40s/it]
 10%|█         | 126/1250 [02:58<26:24,  1.41s/it]
 10%|█         | 127/1250 [02:59<26:22,  1.41s/it]
 10%|█         | 128/1250 [03:00<26:19,  1.41s/it]
 10%|█         | 129/1250 [03:02<26:15,  1.41s/it]
 10%|█         | 130/1250 [03:03<26:19,  1.41s/it]
 10%|█         | 131/1250 [03:05<26:16,  1.41s/it]
 11%|█         | 132/1250 [03:06<26:11,  1.41s/it]
 11%|█         | 133/1250 [03:07<26:10,  1.41s/it]
 11%|█         | 134/1250 [03:09<26:09,  1.41s/it]
 11%|█         | 135/1250 [03:10<26:07,  1.41s/it]
 11%|█         | 136/1250 [03:12<26:04,  1.40s/it]
 11%|█         | 137/1250 [03:13<26:03,  1.41s/it]
 11%|█         | 138/1250 [03:14<26:02,  1.41s/it]
 11%|█         | 139/1250 [03:16<25:55,  1.40s/it]
 11%|█         | 140/1250 [03:17<25:53,  1.40s/it]
 11%|█▏        | 141/1250 [03:19<25:55,  1.40s/it]
 11%|█▏        | 142/1250 [03:20<25:54,  1.40s/it]
 11%|█▏        | 143/1250 [03:21<25:50,  1.40s/it]
 12%|█▏        | 144/1250 [03:23<25:45,  1.40s/it]
 12%|█▏        | 145/1250 [03:24<25:45,  1.40s/it]
 12%|█▏        | 146/1250 [03:26<25:48,  1.40s/it]
 12%|█▏        | 147/1250 [03:27<25:46,  1.40s/it]
 12%|█▏        | 148/1250 [03:28<25:47,  1.40s/it]
 12%|█▏        | 149/1250 [03:30<25:44,  1.40s/it]
 12%|█▏        | 150/1250 [03:31<25:43,  1.40s/it]
                                                  

 12%|█▏        | 150/1250 [03:31<25:43,  1.40s/it]
 12%|█▏        | 151/1250 [03:33<25:53,  1.41s/it]
 12%|█▏        | 152/1250 [03:34<25:49,  1.41s/it]
 12%|█▏        | 153/1250 [03:35<25:46,  1.41s/it]
 12%|█▏        | 154/1250 [03:37<25:43,  1.41s/it]
 12%|█▏        | 155/1250 [03:38<25:38,  1.41s/it]
 12%|█▏        | 156/1250 [03:40<25:37,  1.41s/it]
 13%|█▎        | 157/1250 [03:41<25:36,  1.41s/it]
 13%|█▎        | 158/1250 [03:43<25:39,  1.41s/it]
 13%|█▎        | 159/1250 [03:44<25:35,  1.41s/it]
 13%|█▎        | 160/1250 [03:45<25:31,  1.40s/it]
 13%|█▎        | 161/1250 [03:47<25:28,  1.40s/it]
 13%|█▎        | 162/1250 [03:48<25:27,  1.40s/it]
 13%|█▎        | 163/1250 [03:50<25:26,  1.40s/it]
 13%|█▎        | 164/1250 [03:51<25:19,  1.40s/it]
 13%|█▎        | 165/1250 [03:52<25:20,  1.40s/it]
 13%|█▎        | 166/1250 [03:54<25:19,  1.40s/it]
 13%|█▎        | 167/1250 [03:55<25:16,  1.40s/it]
 13%|█▎        | 168/1250 [03:57<25:16,  1.40s/it]
 14%|█▎        | 169/1250 [03:58<25:17,  1.40s/it]
 14%|█▎        | 170/1250 [03:59<25:18,  1.41s/it]
 14%|█▎        | 171/1250 [04:01<25:15,  1.40s/it]
 14%|█▍        | 172/1250 [04:02<25:15,  1.41s/it]
 14%|█▍        | 173/1250 [04:04<25:15,  1.41s/it]
 14%|█▍        | 174/1250 [04:05<25:13,  1.41s/it]
 14%|█▍        | 175/1250 [04:06<25:13,  1.41s/it]
                                                  

 14%|█▍        | 175/1250 [04:06<25:13,  1.41s/it]
 14%|█▍        | 176/1250 [04:08<25:17,  1.41s/it]
 14%|█▍        | 177/1250 [04:09<25:13,  1.41s/it]
 14%|█▍        | 178/1250 [04:11<25:09,  1.41s/it]
 14%|█▍        | 179/1250 [04:12<25:06,  1.41s/it]
 14%|█▍        | 180/1250 [04:13<25:05,  1.41s/it]
 14%|█▍        | 181/1250 [04:15<25:04,  1.41s/it]
 15%|█▍        | 182/1250 [04:16<25:02,  1.41s/it]
 15%|█▍        | 183/1250 [04:18<25:03,  1.41s/it]
 15%|█▍        | 184/1250 [04:19<24:53,  1.40s/it]
 15%|█▍        | 185/1250 [04:20<24:52,  1.40s/it]
 15%|█▍        | 186/1250 [04:22<24:49,  1.40s/it]
 15%|█▍        | 187/1250 [04:23<25:54,  1.46s/it]
 15%|█▌        | 188/1250 [04:25<25:34,  1.44s/it]
 15%|█▌        | 189/1250 [04:26<25:16,  1.43s/it]
 15%|█▌        | 190/1250 [04:28<25:09,  1.42s/it]
 15%|█▌        | 191/1250 [04:29<25:03,  1.42s/it]
 15%|█▌        | 192/1250 [04:30<24:59,  1.42s/it]
 15%|█▌        | 193/1250 [04:32<24:51,  1.41s/it]
 16%|█▌        | 194/1250 [04:33<24:49,  1.41s/it]
 16%|█▌        | 195/1250 [04:35<24:47,  1.41s/it]
 16%|█▌        | 196/1250 [04:36<24:42,  1.41s/it]
 16%|█▌        | 197/1250 [04:37<24:41,  1.41s/it]
 16%|█▌        | 198/1250 [04:39<24:33,  1.40s/it]
 16%|█▌        | 199/1250 [04:40<24:25,  1.39s/it]
 16%|█▌        | 200/1250 [04:42<24:17,  1.39s/it]
                                                  

 16%|█▌        | 200/1250 [04:42<24:17,  1.39s/it]
 16%|█▌        | 201/1250 [04:43<24:22,  1.39s/it]
 16%|█▌        | 202/1250 [04:44<24:16,  1.39s/it]
 16%|█▌        | 203/1250 [04:46<24:12,  1.39s/it]
 16%|█▋        | 204/1250 [04:47<24:09,  1.39s/it]
 16%|█▋        | 205/1250 [04:49<24:09,  1.39s/it]
 16%|█▋        | 206/1250 [04:50<24:06,  1.39s/it]
 17%|█▋        | 207/1250 [04:51<24:04,  1.38s/it]
 17%|█▋        | 208/1250 [04:53<24:02,  1.38s/it]
 17%|█▋        | 209/1250 [04:54<24:00,  1.38s/it]
 17%|█▋        | 210/1250 [04:55<23:59,  1.38s/it]
 17%|█▋        | 211/1250 [04:57<23:57,  1.38s/it]
 17%|█▋        | 212/1250 [04:58<23:55,  1.38s/it]
 17%|█▋        | 213/1250 [05:00<23:54,  1.38s/it]
 17%|█▋        | 214/1250 [05:01<23:51,  1.38s/it]
 17%|█▋        | 215/1250 [05:02<23:52,  1.38s/it]
 17%|█▋        | 216/1250 [05:04<23:49,  1.38s/it]
 17%|█▋        | 217/1250 [05:05<23:46,  1.38s/it]
 17%|█▋        | 218/1250 [05:07<23:47,  1.38s/it]
 18%|█▊        | 219/1250 [05:08<23:46,  1.38s/it]
 18%|█▊        | 220/1250 [05:09<23:41,  1.38s/it]
 18%|█▊        | 221/1250 [05:11<23:39,  1.38s/it]
 18%|█▊        | 222/1250 [05:12<23:38,  1.38s/it]
 18%|█▊        | 223/1250 [05:13<23:38,  1.38s/it]
 18%|█▊        | 224/1250 [05:15<23:37,  1.38s/it]
 18%|█▊        | 225/1250 [05:16<23:36,  1.38s/it]
                                                  

 18%|█▊        | 225/1250 [05:16<23:36,  1.38s/it]
 18%|█▊        | 226/1250 [05:18<23:44,  1.39s/it]
 18%|█▊        | 227/1250 [05:19<23:39,  1.39s/it]
 18%|█▊        | 228/1250 [05:20<23:36,  1.39s/it]
 18%|█▊        | 229/1250 [05:22<23:33,  1.38s/it]
 18%|█▊        | 230/1250 [05:23<23:32,  1.39s/it]
 18%|█▊        | 231/1250 [05:25<23:25,  1.38s/it]
 19%|█▊        | 232/1250 [05:26<23:23,  1.38s/it]
 19%|█▊        | 233/1250 [05:27<23:24,  1.38s/it]
 19%|█▊        | 234/1250 [05:29<23:23,  1.38s/it]
 19%|█▉        | 235/1250 [05:30<23:21,  1.38s/it]
 19%|█▉        | 236/1250 [05:31<23:18,  1.38s/it]
 19%|█▉        | 237/1250 [05:33<23:21,  1.38s/it]
 19%|█▉        | 238/1250 [05:34<23:18,  1.38s/it]
 19%|█▉        | 239/1250 [05:36<23:17,  1.38s/it]
 19%|█▉        | 240/1250 [05:37<23:15,  1.38s/it]
 19%|█▉        | 241/1250 [05:38<23:16,  1.38s/it]
 19%|█▉        | 242/1250 [05:40<23:14,  1.38s/it]
 19%|█▉        | 243/1250 [05:41<23:09,  1.38s/it]
 20%|█▉        | 244/1250 [05:42<23:13,  1.39s/it]
 20%|█▉        | 245/1250 [05:44<23:07,  1.38s/it]
 20%|█▉        | 246/1250 [05:45<23:05,  1.38s/it]
 20%|█▉        | 247/1250 [05:47<23:04,  1.38s/it]
 20%|█▉        | 248/1250 [05:48<23:03,  1.38s/it]
 20%|█▉        | 249/1250 [05:49<23:00,  1.38s/it]
 20%|██        | 250/1250 [05:51<23:00,  1.38s/it]
                                                  

 20%|██        | 250/1250 [05:51<23:00,  1.38s/it]
 20%|██        | 251/1250 [05:52<23:09,  1.39s/it]
 20%|██        | 252/1250 [05:54<23:03,  1.39s/it]
 20%|██        | 253/1250 [05:55<23:00,  1.38s/it]
 20%|██        | 254/1250 [05:56<22:55,  1.38s/it]
 20%|██        | 255/1250 [05:58<22:54,  1.38s/it]
 20%|██        | 256/1250 [05:59<22:52,  1.38s/it]
 21%|██        | 257/1250 [06:00<22:51,  1.38s/it]
 21%|██        | 258/1250 [06:02<22:49,  1.38s/it]
 21%|██        | 259/1250 [06:03<22:49,  1.38s/it]
 21%|██        | 260/1250 [06:05<22:45,  1.38s/it]
 21%|██        | 261/1250 [06:06<22:45,  1.38s/it]
 21%|██        | 262/1250 [06:07<22:48,  1.39s/it]
 21%|██        | 263/1250 [06:09<22:47,  1.39s/it]
 21%|██        | 264/1250 [06:10<22:44,  1.38s/it]
 21%|██        | 265/1250 [06:12<22:41,  1.38s/it]
 21%|██▏       | 266/1250 [06:13<23:44,  1.45s/it]
 21%|██▏       | 267/1250 [06:14<23:22,  1.43s/it]
 21%|██▏       | 268/1250 [06:16<23:09,  1.41s/it]
 22%|██▏       | 269/1250 [06:17<22:57,  1.40s/it]
 22%|██▏       | 270/1250 [06:19<22:48,  1.40s/it]
 22%|██▏       | 271/1250 [06:20<22:39,  1.39s/it]
 22%|██▏       | 272/1250 [06:21<22:36,  1.39s/it]
 22%|██▏       | 273/1250 [06:23<22:33,  1.39s/it]
 22%|██▏       | 274/1250 [06:24<22:31,  1.38s/it]
 22%|██▏       | 275/1250 [06:26<22:27,  1.38s/it]
                                                  

 22%|██▏       | 275/1250 [06:26<22:27,  1.38s/it]
 22%|██▏       | 276/1250 [06:27<22:32,  1.39s/it]
 22%|██▏       | 277/1250 [06:28<22:30,  1.39s/it]
 22%|██▏       | 278/1250 [06:30<22:25,  1.38s/it]
 22%|██▏       | 279/1250 [06:31<22:22,  1.38s/it]
 22%|██▏       | 280/1250 [06:32<22:19,  1.38s/it]
 22%|██▏       | 281/1250 [06:34<22:19,  1.38s/it]
 23%|██▎       | 282/1250 [06:35<22:17,  1.38s/it]
 23%|██▎       | 283/1250 [06:37<22:16,  1.38s/it]
 23%|██▎       | 284/1250 [06:38<22:15,  1.38s/it]
 23%|██▎       | 285/1250 [06:39<22:14,  1.38s/it]
 23%|██▎       | 286/1250 [06:41<22:11,  1.38s/it]
 23%|██▎       | 287/1250 [06:42<22:11,  1.38s/it]
 23%|██▎       | 288/1250 [06:44<22:11,  1.38s/it]
 23%|██▎       | 289/1250 [06:45<22:09,  1.38s/it]
 23%|██▎       | 290/1250 [06:46<22:05,  1.38s/it]
 23%|██▎       | 291/1250 [06:48<22:06,  1.38s/it]
 23%|██▎       | 292/1250 [06:49<22:04,  1.38s/it]
 23%|██▎       | 293/1250 [06:51<22:59,  1.44s/it]
 24%|██▎       | 294/1250 [06:52<22:42,  1.42s/it]
 24%|██▎       | 295/1250 [06:53<22:28,  1.41s/it]
 24%|██▎       | 296/1250 [06:55<22:19,  1.40s/it]
 24%|██▍       | 297/1250 [06:56<22:12,  1.40s/it]
 24%|██▍       | 298/1250 [06:58<22:09,  1.40s/it]
 24%|██▍       | 299/1250 [06:59<22:05,  1.39s/it]
 24%|██▍       | 300/1250 [07:00<22:00,  1.39s/it]
                                                  

 24%|██▍       | 300/1250 [07:00<22:00,  1.39s/it]
 24%|██▍       | 301/1250 [07:02<21:59,  1.39s/it]
 24%|██▍       | 302/1250 [07:03<21:59,  1.39s/it]
 24%|██▍       | 303/1250 [07:04<21:53,  1.39s/it]
 24%|██▍       | 304/1250 [07:06<21:47,  1.38s/it]
 24%|██▍       | 305/1250 [07:07<21:47,  1.38s/it]
 24%|██▍       | 306/1250 [07:09<21:45,  1.38s/it]
 25%|██▍       | 307/1250 [07:10<21:43,  1.38s/it]
 25%|██▍       | 308/1250 [07:11<21:42,  1.38s/it]
 25%|██▍       | 309/1250 [07:13<21:44,  1.39s/it]
 25%|██▍       | 310/1250 [07:14<21:41,  1.38s/it]
 25%|██▍       | 311/1250 [07:16<21:40,  1.38s/it]
 25%|██▍       | 312/1250 [07:17<21:40,  1.39s/it]
 25%|██▌       | 313/1250 [07:18<21:39,  1.39s/it]
 25%|██▌       | 314/1250 [07:20<21:37,  1.39s/it]
 25%|██▌       | 315/1250 [07:21<21:33,  1.38s/it]
 25%|██▌       | 316/1250 [07:22<21:35,  1.39s/it]
 25%|██▌       | 317/1250 [07:24<21:29,  1.38s/it]
 25%|██▌       | 318/1250 [07:25<21:29,  1.38s/it]
 26%|██▌       | 319/1250 [07:27<21:28,  1.38s/it]
 26%|██▌       | 320/1250 [07:28<21:28,  1.39s/it]
 26%|██▌       | 321/1250 [07:29<21:23,  1.38s/it]
 26%|██▌       | 322/1250 [07:31<21:23,  1.38s/it]
 26%|██▌       | 323/1250 [07:32<21:23,  1.38s/it]
 26%|██▌       | 324/1250 [07:34<21:22,  1.38s/it]
 26%|██▌       | 325/1250 [07:35<21:18,  1.38s/it]
                                                  

 26%|██▌       | 325/1250 [07:35<21:18,  1.38s/it]
 26%|██▌       | 326/1250 [07:36<21:22,  1.39s/it]
 26%|██▌       | 327/1250 [07:38<21:18,  1.38s/it]
 26%|██▌       | 328/1250 [07:39<21:16,  1.38s/it]
 26%|██▋       | 329/1250 [07:40<21:13,  1.38s/it]
 26%|██▋       | 330/1250 [07:42<21:11,  1.38s/it]
 26%|██▋       | 331/1250 [07:43<21:13,  1.39s/it]
 27%|██▋       | 332/1250 [07:45<21:10,  1.38s/it]
 27%|██▋       | 333/1250 [07:46<21:08,  1.38s/it]
 27%|██▋       | 334/1250 [07:47<21:07,  1.38s/it]
 27%|██▋       | 335/1250 [07:49<21:02,  1.38s/it]
 27%|██▋       | 336/1250 [07:50<20:58,  1.38s/it]
 27%|██▋       | 337/1250 [07:52<20:57,  1.38s/it]
 27%|██▋       | 338/1250 [07:53<20:58,  1.38s/it]
 27%|██▋       | 339/1250 [07:54<20:57,  1.38s/it]
 27%|██▋       | 340/1250 [07:56<20:57,  1.38s/it]
 27%|██▋       | 341/1250 [07:57<20:56,  1.38s/it]
 27%|██▋       | 342/1250 [07:58<20:56,  1.38s/it]
 27%|██▋       | 343/1250 [08:00<20:54,  1.38s/it]
 28%|██▊       | 344/1250 [08:01<20:51,  1.38s/it]
 28%|██▊       | 345/1250 [08:03<20:51,  1.38s/it]
 28%|██▊       | 346/1250 [08:04<20:49,  1.38s/it]
 28%|██▊       | 347/1250 [08:05<20:48,  1.38s/it]
 28%|██▊       | 348/1250 [08:07<20:45,  1.38s/it]
 28%|██▊       | 349/1250 [08:08<20:44,  1.38s/it]
 28%|██▊       | 350/1250 [08:09<20:43,  1.38s/it]
                                                  

 28%|██▊       | 350/1250 [08:10<20:43,  1.38s/it]
 28%|██▊       | 351/1250 [08:11<20:51,  1.39s/it]
 28%|██▊       | 352/1250 [08:12<20:48,  1.39s/it]
 28%|██▊       | 353/1250 [08:14<20:46,  1.39s/it]
 28%|██▊       | 354/1250 [08:15<20:42,  1.39s/it]
 28%|██▊       | 355/1250 [08:16<20:39,  1.38s/it]
 28%|██▊       | 356/1250 [08:18<20:37,  1.38s/it]
 29%|██▊       | 357/1250 [08:19<20:34,  1.38s/it]
 29%|██▊       | 358/1250 [08:21<20:32,  1.38s/it]
 29%|██▊       | 359/1250 [08:22<20:31,  1.38s/it]
 29%|██▉       | 360/1250 [08:23<20:31,  1.38s/it]
 29%|██▉       | 361/1250 [08:25<20:28,  1.38s/it]
 29%|██▉       | 362/1250 [08:26<20:27,  1.38s/it]
 29%|██▉       | 363/1250 [08:27<20:27,  1.38s/it]
 29%|██▉       | 364/1250 [08:29<20:25,  1.38s/it]
 29%|██▉       | 365/1250 [08:30<20:23,  1.38s/it]
 29%|██▉       | 366/1250 [08:32<21:15,  1.44s/it]
 29%|██▉       | 367/1250 [08:33<21:01,  1.43s/it]
 29%|██▉       | 368/1250 [08:35<20:47,  1.41s/it]
 30%|██▉       | 369/1250 [08:36<20:38,  1.41s/it]
 30%|██▉       | 370/1250 [08:37<20:32,  1.40s/it]
 30%|██▉       | 371/1250 [08:39<20:24,  1.39s/it]
 30%|██▉       | 372/1250 [08:40<20:19,  1.39s/it]
 30%|██▉       | 373/1250 [08:42<21:11,  1.45s/it]
 30%|██▉       | 374/1250 [08:43<20:53,  1.43s/it]
 30%|███       | 375/1250 [08:45<20:39,  1.42s/it]
                                                  

 30%|███       | 375/1250 [08:45<20:39,  1.42s/it]
 30%|███       | 376/1250 [08:46<20:35,  1.41s/it]
 30%|███       | 377/1250 [08:47<20:24,  1.40s/it]
 30%|███       | 378/1250 [08:49<20:19,  1.40s/it]
 30%|███       | 379/1250 [08:50<20:12,  1.39s/it]
 30%|███       | 380/1250 [08:51<20:08,  1.39s/it]
 30%|███       | 381/1250 [08:53<20:06,  1.39s/it]
 31%|███       | 382/1250 [08:54<20:01,  1.38s/it]
 31%|███       | 383/1250 [08:56<20:00,  1.38s/it]
 31%|███       | 384/1250 [08:57<19:57,  1.38s/it]
 31%|███       | 385/1250 [08:58<19:57,  1.38s/it]
 31%|███       | 386/1250 [09:00<19:54,  1.38s/it]
 31%|███       | 387/1250 [09:01<19:53,  1.38s/it]
 31%|███       | 388/1250 [09:02<19:52,  1.38s/it]
 31%|███       | 389/1250 [09:04<19:51,  1.38s/it]
 31%|███       | 390/1250 [09:05<19:50,  1.38s/it]
 31%|███▏      | 391/1250 [09:07<19:49,  1.38s/it]
 31%|███▏      | 392/1250 [09:08<19:46,  1.38s/it]
 31%|███▏      | 393/1250 [09:09<19:45,  1.38s/it]
 32%|███▏      | 394/1250 [09:11<19:42,  1.38s/it]
 32%|███▏      | 395/1250 [09:12<19:40,  1.38s/it]
 32%|███▏      | 396/1250 [09:14<19:40,  1.38s/it]
 32%|███▏      | 397/1250 [09:15<19:38,  1.38s/it]
 32%|███▏      | 398/1250 [09:16<19:37,  1.38s/it]
 32%|███▏      | 399/1250 [09:18<19:37,  1.38s/it]
 32%|███▏      | 400/1250 [09:19<19:33,  1.38s/it]
                                                  

 32%|███▏      | 400/1250 [09:19<19:33,  1.38s/it]
 32%|███▏      | 401/1250 [09:20<19:40,  1.39s/it]
 32%|███▏      | 402/1250 [09:22<19:34,  1.39s/it]
 32%|███▏      | 403/1250 [09:23<19:34,  1.39s/it]
 32%|███▏      | 404/1250 [09:25<19:33,  1.39s/it]
 32%|███▏      | 405/1250 [09:26<19:29,  1.38s/it]
 32%|███▏      | 406/1250 [09:27<19:30,  1.39s/it]
 33%|███▎      | 407/1250 [09:29<19:27,  1.39s/it]
 33%|███▎      | 408/1250 [09:30<19:26,  1.39s/it]
 33%|███▎      | 409/1250 [09:32<19:24,  1.38s/it]
 33%|███▎      | 410/1250 [09:33<19:25,  1.39s/it]
 33%|███▎      | 411/1250 [09:34<19:22,  1.39s/it]
 33%|███▎      | 412/1250 [09:36<19:18,  1.38s/it]
 33%|███▎      | 413/1250 [09:37<19:16,  1.38s/it]
 33%|███▎      | 414/1250 [09:38<19:17,  1.38s/it]
 33%|███▎      | 415/1250 [09:40<19:14,  1.38s/it]
 33%|███▎      | 416/1250 [09:41<19:11,  1.38s/it]
 33%|███▎      | 417/1250 [09:43<19:12,  1.38s/it]
 33%|███▎      | 418/1250 [09:44<19:10,  1.38s/it]
 34%|███▎      | 419/1250 [09:45<19:08,  1.38s/it]
 34%|███▎      | 420/1250 [09:47<19:07,  1.38s/it]
 34%|███▎      | 421/1250 [09:48<19:07,  1.38s/it]
 34%|███▍      | 422/1250 [09:50<19:02,  1.38s/it]
 34%|███▍      | 423/1250 [09:51<19:01,  1.38s/it]
 34%|███▍      | 424/1250 [09:52<19:02,  1.38s/it]
 34%|███▍      | 425/1250 [09:54<18:59,  1.38s/it]
                                                  

 34%|███▍      | 425/1250 [09:54<18:59,  1.38s/it]
 34%|███▍      | 426/1250 [09:55<19:05,  1.39s/it]
 34%|███▍      | 427/1250 [09:56<19:01,  1.39s/it]
 34%|███▍      | 428/1250 [09:58<19:00,  1.39s/it]
 34%|███▍      | 429/1250 [09:59<18:57,  1.39s/it]
 34%|███▍      | 430/1250 [10:01<18:53,  1.38s/it]
 34%|███▍      | 431/1250 [10:02<18:50,  1.38s/it]
 35%|███▍      | 432/1250 [10:03<18:50,  1.38s/it]
 35%|███▍      | 433/1250 [10:05<18:45,  1.38s/it]
 35%|███▍      | 434/1250 [10:06<18:44,  1.38s/it]
 35%|███▍      | 435/1250 [10:08<18:44,  1.38s/it]
 35%|███▍      | 436/1250 [10:09<18:43,  1.38s/it]
 35%|███▍      | 437/1250 [10:10<18:41,  1.38s/it]
 35%|███▌      | 438/1250 [10:12<18:40,  1.38s/it]
 35%|███▌      | 439/1250 [10:13<18:40,  1.38s/it]
 35%|███▌      | 440/1250 [10:14<18:37,  1.38s/it]
 35%|███▌      | 441/1250 [10:16<18:35,  1.38s/it]
 35%|███▌      | 442/1250 [10:17<18:34,  1.38s/it]
 35%|███▌      | 443/1250 [10:19<18:35,  1.38s/it]
 36%|███▌      | 444/1250 [10:20<18:33,  1.38s/it]
 36%|███▌      | 445/1250 [10:21<18:29,  1.38s/it]
 36%|███▌      | 446/1250 [10:23<18:27,  1.38s/it]
 36%|███▌      | 447/1250 [10:24<18:27,  1.38s/it]
 36%|███▌      | 448/1250 [10:26<19:17,  1.44s/it]
 36%|███▌      | 449/1250 [10:27<19:02,  1.43s/it]
 36%|███▌      | 450/1250 [10:28<18:51,  1.41s/it]
                                                  

 36%|███▌      | 450/1250 [10:28<18:51,  1.41s/it]
 36%|███▌      | 451/1250 [10:30<18:46,  1.41s/it]
 36%|███▌      | 452/1250 [10:31<18:38,  1.40s/it]
 36%|███▌      | 453/1250 [10:33<18:35,  1.40s/it]
 36%|███▋      | 454/1250 [10:34<18:28,  1.39s/it]
 36%|███▋      | 455/1250 [10:35<18:23,  1.39s/it]
 36%|███▋      | 456/1250 [10:37<18:17,  1.38s/it]
 37%|███▋      | 457/1250 [10:38<18:18,  1.38s/it]
 37%|███▋      | 458/1250 [10:39<18:13,  1.38s/it]
 37%|███▋      | 459/1250 [10:41<18:11,  1.38s/it]
 37%|███▋      | 460/1250 [10:42<18:09,  1.38s/it]
 37%|███▋      | 461/1250 [10:44<18:09,  1.38s/it]
 37%|███▋      | 462/1250 [10:45<18:08,  1.38s/it]
 37%|███▋      | 463/1250 [10:46<18:07,  1.38s/it]
 37%|███▋      | 464/1250 [10:48<18:07,  1.38s/it]
 37%|███▋      | 465/1250 [10:49<18:05,  1.38s/it]
 37%|███▋      | 466/1250 [10:51<18:02,  1.38s/it]
 37%|███▋      | 467/1250 [10:52<18:00,  1.38s/it]
 37%|███▋      | 468/1250 [10:53<17:59,  1.38s/it]
 38%|███▊      | 469/1250 [10:55<17:59,  1.38s/it]
 38%|███▊      | 470/1250 [10:56<17:58,  1.38s/it]
 38%|███▊      | 471/1250 [10:57<17:57,  1.38s/it]
 38%|███▊      | 472/1250 [10:59<17:56,  1.38s/it]
 38%|███▊      | 473/1250 [11:00<17:53,  1.38s/it]
 38%|███▊      | 474/1250 [11:02<17:51,  1.38s/it]
 38%|███▊      | 475/1250 [11:03<17:52,  1.38s/it]
                                                  

 38%|███▊      | 475/1250 [11:03<17:52,  1.38s/it]
 38%|███▊      | 476/1250 [11:04<17:53,  1.39s/it]
 38%|███▊      | 477/1250 [11:06<17:51,  1.39s/it]
 38%|███▊      | 478/1250 [11:07<17:46,  1.38s/it]
 38%|███▊      | 479/1250 [11:09<17:45,  1.38s/it]
 38%|███▊      | 480/1250 [11:10<17:41,  1.38s/it]
 38%|███▊      | 481/1250 [11:11<17:38,  1.38s/it]
 39%|███▊      | 482/1250 [11:13<17:38,  1.38s/it]
 39%|███▊      | 483/1250 [11:14<17:38,  1.38s/it]
 39%|███▊      | 484/1250 [11:15<17:34,  1.38s/it]
 39%|███▉      | 485/1250 [11:17<17:33,  1.38s/it]
 39%|███▉      | 486/1250 [11:18<17:35,  1.38s/it]
 39%|███▉      | 487/1250 [11:20<17:33,  1.38s/it]
 39%|███▉      | 488/1250 [11:21<17:31,  1.38s/it]
 39%|███▉      | 489/1250 [11:22<17:32,  1.38s/it]
 39%|███▉      | 490/1250 [11:24<17:31,  1.38s/it]
 39%|███▉      | 491/1250 [11:25<17:30,  1.38s/it]
 39%|███▉      | 492/1250 [11:26<17:28,  1.38s/it]
 39%|███▉      | 493/1250 [11:28<17:28,  1.38s/it]
 40%|███▉      | 494/1250 [11:29<17:25,  1.38s/it]
 40%|███▉      | 495/1250 [11:31<17:22,  1.38s/it]
 40%|███▉      | 496/1250 [11:32<17:22,  1.38s/it]
 40%|███▉      | 497/1250 [11:33<17:20,  1.38s/it]
 40%|███▉      | 498/1250 [11:35<17:19,  1.38s/it]
 40%|███▉      | 499/1250 [11:36<17:17,  1.38s/it]
 40%|████      | 500/1250 [11:38<17:16,  1.38s/it]
                                                  

 40%|████      | 500/1250 [11:38<17:16,  1.38s/it]
 40%|████      | 501/1250 [11:39<17:23,  1.39s/it]
 40%|████      | 502/1250 [11:40<17:20,  1.39s/it]
 40%|████      | 503/1250 [11:42<17:17,  1.39s/it]
 40%|████      | 504/1250 [11:43<17:13,  1.39s/it]
 40%|████      | 505/1250 [11:44<17:09,  1.38s/it]
 40%|████      | 506/1250 [11:46<17:08,  1.38s/it]
 41%|████      | 507/1250 [11:47<17:04,  1.38s/it]
 41%|████      | 508/1250 [11:49<17:03,  1.38s/it]
 41%|████      | 509/1250 [11:50<17:01,  1.38s/it]
 41%|████      | 510/1250 [11:51<17:01,  1.38s/it]
 41%|████      | 511/1250 [11:53<17:00,  1.38s/it]
 41%|████      | 512/1250 [11:54<16:58,  1.38s/it]
 41%|████      | 513/1250 [11:55<16:58,  1.38s/it]
 41%|████      | 514/1250 [11:57<16:57,  1.38s/it]
 41%|████      | 515/1250 [11:58<16:55,  1.38s/it]
 41%|████▏     | 516/1250 [12:00<16:53,  1.38s/it]
 41%|████▏     | 517/1250 [12:01<16:51,  1.38s/it]
 41%|████▏     | 518/1250 [12:02<16:50,  1.38s/it]
 42%|████▏     | 519/1250 [12:04<16:49,  1.38s/it]
 42%|████▏     | 520/1250 [12:05<16:48,  1.38s/it]
 42%|████▏     | 521/1250 [12:07<16:47,  1.38s/it]
 42%|████▏     | 522/1250 [12:08<16:46,  1.38s/it]
 42%|████▏     | 523/1250 [12:09<16:44,  1.38s/it]
 42%|████▏     | 524/1250 [12:11<16:42,  1.38s/it]
 42%|████▏     | 525/1250 [12:12<16:41,  1.38s/it]
                                                  

 42%|████▏     | 525/1250 [12:12<16:41,  1.38s/it]
 42%|████▏     | 526/1250 [12:13<16:45,  1.39s/it]
 42%|████▏     | 527/1250 [12:15<16:42,  1.39s/it]
 42%|████▏     | 528/1250 [12:16<16:40,  1.39s/it]
 42%|████▏     | 529/1250 [12:18<16:37,  1.38s/it]
 42%|████▏     | 530/1250 [12:19<16:35,  1.38s/it]
 42%|████▏     | 531/1250 [12:20<16:35,  1.39s/it]
 43%|████▎     | 532/1250 [12:22<16:33,  1.38s/it]
 43%|████▎     | 533/1250 [12:23<16:31,  1.38s/it]
 43%|████▎     | 534/1250 [12:25<16:30,  1.38s/it]
 43%|████▎     | 535/1250 [12:26<16:28,  1.38s/it]
 43%|████▎     | 536/1250 [12:27<16:25,  1.38s/it]
 43%|████▎     | 537/1250 [12:29<16:23,  1.38s/it]
 43%|████▎     | 538/1250 [12:30<16:22,  1.38s/it]
 43%|████▎     | 539/1250 [12:31<16:20,  1.38s/it]
 43%|████▎     | 540/1250 [12:33<16:17,  1.38s/it]
 43%|████▎     | 541/1250 [12:34<16:17,  1.38s/it]
 43%|████▎     | 542/1250 [12:36<16:16,  1.38s/it]
 43%|████▎     | 543/1250 [12:37<16:15,  1.38s/it]
 44%|████▎     | 544/1250 [12:38<16:13,  1.38s/it]
 44%|████▎     | 545/1250 [12:40<16:12,  1.38s/it]
 44%|████▎     | 546/1250 [12:41<16:11,  1.38s/it]
 44%|████▍     | 547/1250 [12:42<16:09,  1.38s/it]
 44%|████▍     | 548/1250 [12:44<16:08,  1.38s/it]
 44%|████▍     | 549/1250 [12:45<16:06,  1.38s/it]
 44%|████▍     | 550/1250 [12:47<16:06,  1.38s/it]
                                                  

 44%|████▍     | 550/1250 [12:47<16:06,  1.38s/it]
 44%|████▍     | 551/1250 [12:48<16:06,  1.38s/it]
 44%|████▍     | 552/1250 [12:49<16:05,  1.38s/it]
 44%|████▍     | 553/1250 [12:51<16:04,  1.38s/it]
 44%|████▍     | 554/1250 [12:52<16:15,  1.40s/it]
 44%|████▍     | 555/1250 [12:54<16:09,  1.39s/it]
 44%|████▍     | 556/1250 [12:55<16:05,  1.39s/it]
 45%|████▍     | 557/1250 [12:56<16:02,  1.39s/it]
 45%|████▍     | 558/1250 [12:58<16:40,  1.45s/it]
 45%|████▍     | 559/1250 [12:59<16:24,  1.42s/it]
 45%|████▍     | 560/1250 [13:01<16:13,  1.41s/it]
 45%|████▍     | 561/1250 [13:02<16:07,  1.40s/it]
 45%|████▍     | 562/1250 [13:03<16:02,  1.40s/it]
 45%|████▌     | 563/1250 [13:05<15:57,  1.39s/it]
 45%|████▌     | 564/1250 [13:06<15:53,  1.39s/it]
 45%|████▌     | 565/1250 [13:08<15:50,  1.39s/it]
 45%|████▌     | 566/1250 [13:09<15:47,  1.38s/it]
 45%|████▌     | 567/1250 [13:10<15:44,  1.38s/it]
 45%|████▌     | 568/1250 [13:12<15:43,  1.38s/it]
 46%|████▌     | 569/1250 [13:13<15:41,  1.38s/it]
 46%|████▌     | 570/1250 [13:15<15:40,  1.38s/it]
 46%|████▌     | 571/1250 [13:16<15:38,  1.38s/it]
 46%|████▌     | 572/1250 [13:17<15:35,  1.38s/it]
 46%|████▌     | 573/1250 [13:19<15:35,  1.38s/it]
 46%|████▌     | 574/1250 [13:20<15:34,  1.38s/it]
 46%|████▌     | 575/1250 [13:21<15:32,  1.38s/it]
                                                  

 46%|████▌     | 575/1250 [13:21<15:32,  1.38s/it]
 46%|████▌     | 576/1250 [13:23<15:32,  1.38s/it]
 46%|████▌     | 577/1250 [13:24<15:30,  1.38s/it]
 46%|████▌     | 578/1250 [13:26<15:29,  1.38s/it]
 46%|████▋     | 579/1250 [13:27<15:27,  1.38s/it]
 46%|████▋     | 580/1250 [13:28<15:27,  1.38s/it]
 46%|████▋     | 581/1250 [13:30<15:24,  1.38s/it]
 47%|████▋     | 582/1250 [13:31<15:23,  1.38s/it]
 47%|████▋     | 583/1250 [13:32<15:22,  1.38s/it]
 47%|████▋     | 584/1250 [13:34<15:20,  1.38s/it]
 47%|████▋     | 585/1250 [13:35<15:19,  1.38s/it]
 47%|████▋     | 586/1250 [13:37<15:17,  1.38s/it]
 47%|████▋     | 587/1250 [13:38<15:14,  1.38s/it]
 47%|████▋     | 588/1250 [13:39<15:14,  1.38s/it]
 47%|████▋     | 589/1250 [13:41<15:13,  1.38s/it]
 47%|████▋     | 590/1250 [13:42<15:12,  1.38s/it]
 47%|████▋     | 591/1250 [13:44<15:10,  1.38s/it]
 47%|████▋     | 592/1250 [13:45<15:09,  1.38s/it]
 47%|████▋     | 593/1250 [13:46<15:07,  1.38s/it]
 48%|████▊     | 594/1250 [13:48<15:07,  1.38s/it]
 48%|████▊     | 595/1250 [13:49<15:06,  1.38s/it]
 48%|████▊     | 596/1250 [13:50<15:05,  1.38s/it]
 48%|████▊     | 597/1250 [13:52<15:03,  1.38s/it]
 48%|████▊     | 598/1250 [13:53<15:02,  1.38s/it]
 48%|████▊     | 599/1250 [13:55<15:01,  1.38s/it]
 48%|████▊     | 600/1250 [13:56<14:59,  1.38s/it]
                                                  

 48%|████▊     | 600/1250 [13:56<14:59,  1.38s/it]
 48%|████▊     | 601/1250 [13:57<15:03,  1.39s/it]
 48%|████▊     | 602/1250 [13:59<14:59,  1.39s/it]
 48%|████▊     | 603/1250 [14:00<14:57,  1.39s/it]
 48%|████▊     | 604/1250 [14:02<14:55,  1.39s/it]
 48%|████▊     | 605/1250 [14:03<14:54,  1.39s/it]
 48%|████▊     | 606/1250 [14:04<14:52,  1.39s/it]
 49%|████▊     | 607/1250 [14:06<14:49,  1.38s/it]
 49%|████▊     | 608/1250 [14:07<14:47,  1.38s/it]
 49%|████▊     | 609/1250 [14:08<14:46,  1.38s/it]
 49%|████▉     | 610/1250 [14:10<14:45,  1.38s/it]
 49%|████▉     | 611/1250 [14:11<14:44,  1.38s/it]
 49%|████▉     | 612/1250 [14:13<14:43,  1.38s/it]
 49%|████▉     | 613/1250 [14:14<14:41,  1.38s/it]
 49%|████▉     | 614/1250 [14:15<14:39,  1.38s/it]
 49%|████▉     | 615/1250 [14:17<14:38,  1.38s/it]
 49%|████▉     | 616/1250 [14:18<14:38,  1.39s/it]
 49%|████▉     | 617/1250 [14:20<14:38,  1.39s/it]
 49%|████▉     | 618/1250 [14:21<14:37,  1.39s/it]
 50%|████▉     | 619/1250 [14:22<14:36,  1.39s/it]
 50%|████▉     | 620/1250 [14:24<14:34,  1.39s/it]
 50%|████▉     | 621/1250 [14:25<14:32,  1.39s/it]
 50%|████▉     | 622/1250 [14:26<14:30,  1.39s/it]
 50%|████▉     | 623/1250 [14:28<14:28,  1.39s/it]
 50%|████▉     | 624/1250 [14:29<14:26,  1.38s/it]
 50%|█████     | 625/1250 [14:31<14:24,  1.38s/it]
                                                  

 50%|█████     | 625/1250 [14:31<14:24,  1.38s/it]
 50%|█████     | 626/1250 [14:32<14:29,  1.39s/it]
 50%|█████     | 627/1250 [14:33<14:25,  1.39s/it]
 50%|█████     | 628/1250 [14:35<14:22,  1.39s/it]
 50%|█████     | 629/1250 [14:36<14:20,  1.39s/it]
 50%|█████     | 630/1250 [14:38<14:18,  1.38s/it]
 50%|█████     | 631/1250 [14:39<14:16,  1.38s/it]
 51%|█████     | 632/1250 [14:40<14:14,  1.38s/it]
 51%|█████     | 633/1250 [14:42<14:13,  1.38s/it]
 51%|█████     | 634/1250 [14:43<14:13,  1.39s/it]
 51%|█████     | 635/1250 [14:44<14:10,  1.38s/it]
 51%|█████     | 636/1250 [14:46<14:09,  1.38s/it]
 51%|█████     | 637/1250 [14:47<14:07,  1.38s/it]
 51%|█████     | 638/1250 [14:49<14:05,  1.38s/it]
 51%|█████     | 639/1250 [14:50<14:05,  1.38s/it]
 51%|█████     | 640/1250 [14:52<14:42,  1.45s/it]
 51%|█████▏    | 641/1250 [14:53<14:29,  1.43s/it]
 51%|█████▏    | 642/1250 [14:54<14:19,  1.41s/it]
 51%|█████▏    | 643/1250 [14:56<14:12,  1.40s/it]
 52%|█████▏    | 644/1250 [14:57<14:07,  1.40s/it]
 52%|█████▏    | 645/1250 [14:59<14:03,  1.39s/it]
 52%|█████▏    | 646/1250 [15:00<14:00,  1.39s/it]
 52%|█████▏    | 647/1250 [15:01<13:58,  1.39s/it]
 52%|█████▏    | 648/1250 [15:03<13:56,  1.39s/it]
 52%|█████▏    | 649/1250 [15:04<13:54,  1.39s/it]
 52%|█████▏    | 650/1250 [15:05<13:51,  1.39s/it]
                                                  

 52%|█████▏    | 650/1250 [15:05<13:51,  1.39s/it]
 52%|█████▏    | 651/1250 [15:07<13:55,  1.40s/it]
 52%|█████▏    | 652/1250 [15:08<13:52,  1.39s/it]
 52%|█████▏    | 653/1250 [15:10<13:49,  1.39s/it]
 52%|█████▏    | 654/1250 [15:11<13:48,  1.39s/it]
 52%|█████▏    | 655/1250 [15:12<13:46,  1.39s/it]
 52%|█████▏    | 656/1250 [15:14<13:43,  1.39s/it]
 53%|█████▎    | 657/1250 [15:15<13:42,  1.39s/it]
 53%|█████▎    | 658/1250 [15:17<13:39,  1.38s/it]
 53%|█████▎    | 659/1250 [15:18<13:37,  1.38s/it]
 53%|█████▎    | 660/1250 [15:19<13:36,  1.38s/it]
 53%|█████▎    | 661/1250 [15:21<13:34,  1.38s/it]
 53%|█████▎    | 662/1250 [15:22<13:33,  1.38s/it]
 53%|█████▎    | 663/1250 [15:23<13:33,  1.39s/it]
 53%|█████▎    | 664/1250 [15:25<14:08,  1.45s/it]
 53%|█████▎    | 665/1250 [15:26<13:54,  1.43s/it]
 53%|█████▎    | 666/1250 [15:28<13:47,  1.42s/it]
 53%|█████▎    | 667/1250 [15:29<13:41,  1.41s/it]
 53%|█████▎    | 668/1250 [15:31<13:35,  1.40s/it]
 54%|█████▎    | 669/1250 [15:32<13:30,  1.40s/it]
 54%|█████▎    | 670/1250 [15:33<13:26,  1.39s/it]
 54%|█████▎    | 671/1250 [15:35<13:24,  1.39s/it]
 54%|█████▍    | 672/1250 [15:36<13:21,  1.39s/it]
 54%|█████▍    | 673/1250 [15:38<13:20,  1.39s/it]
 54%|█████▍    | 674/1250 [15:39<13:18,  1.39s/it]
 54%|█████▍    | 675/1250 [15:40<13:16,  1.39s/it]
                                                  

 54%|█████▍    | 675/1250 [15:40<13:16,  1.39s/it]
 54%|█████▍    | 676/1250 [15:42<13:20,  1.40s/it]
 54%|█████▍    | 677/1250 [15:43<13:17,  1.39s/it]
 54%|█████▍    | 678/1250 [15:44<13:14,  1.39s/it]
 54%|█████▍    | 679/1250 [15:46<13:11,  1.39s/it]
 54%|█████▍    | 680/1250 [15:47<13:10,  1.39s/it]
 54%|█████▍    | 681/1250 [15:49<13:08,  1.38s/it]
 55%|█████▍    | 682/1250 [15:50<13:04,  1.38s/it]
 55%|█████▍    | 683/1250 [15:51<13:03,  1.38s/it]
 55%|█████▍    | 684/1250 [15:53<13:01,  1.38s/it]
 55%|█████▍    | 685/1250 [15:54<13:00,  1.38s/it]
 55%|█████▍    | 686/1250 [15:56<12:57,  1.38s/it]
 55%|█████▍    | 687/1250 [15:57<12:57,  1.38s/it]
 55%|█████▌    | 688/1250 [15:58<12:56,  1.38s/it]
 55%|█████▌    | 689/1250 [16:00<12:55,  1.38s/it]
 55%|█████▌    | 690/1250 [16:01<12:54,  1.38s/it]
 55%|█████▌    | 691/1250 [16:02<12:53,  1.38s/it]
 55%|█████▌    | 692/1250 [16:04<12:51,  1.38s/it]
 55%|█████▌    | 693/1250 [16:05<12:51,  1.38s/it]
 56%|█████▌    | 694/1250 [16:07<12:49,  1.38s/it]
 56%|█████▌    | 695/1250 [16:08<12:46,  1.38s/it]
 56%|█████▌    | 696/1250 [16:09<12:59,  1.41s/it]
 56%|█████▌    | 697/1250 [16:11<12:52,  1.40s/it]
 56%|█████▌    | 698/1250 [16:12<12:48,  1.39s/it]
 56%|█████▌    | 699/1250 [16:14<12:45,  1.39s/it]
 56%|█████▌    | 700/1250 [16:15<12:43,  1.39s/it]
                                                  

 56%|█████▌    | 700/1250 [16:15<12:43,  1.39s/it]
 56%|█████▌    | 701/1250 [16:16<12:44,  1.39s/it]
 56%|█████▌    | 702/1250 [16:18<12:41,  1.39s/it]
 56%|█████▌    | 703/1250 [16:19<12:38,  1.39s/it]
 56%|█████▋    | 704/1250 [16:21<12:36,  1.39s/it]
 56%|█████▋    | 705/1250 [16:22<12:34,  1.38s/it]
 56%|█████▋    | 706/1250 [16:23<12:32,  1.38s/it]
 57%|█████▋    | 707/1250 [16:25<12:29,  1.38s/it]
 57%|█████▋    | 708/1250 [16:26<12:28,  1.38s/it]
 57%|█████▋    | 709/1250 [16:27<12:24,  1.38s/it]
 57%|█████▋    | 710/1250 [16:29<12:24,  1.38s/it]
 57%|█████▋    | 711/1250 [16:30<12:23,  1.38s/it]
 57%|█████▋    | 712/1250 [16:32<12:21,  1.38s/it]
 57%|█████▋    | 713/1250 [16:33<12:20,  1.38s/it]
 57%|█████▋    | 714/1250 [16:34<12:19,  1.38s/it]
 57%|█████▋    | 715/1250 [16:36<12:17,  1.38s/it]
 57%|█████▋    | 716/1250 [16:37<12:16,  1.38s/it]
 57%|█████▋    | 717/1250 [16:38<12:16,  1.38s/it]
 57%|█████▋    | 718/1250 [16:40<12:14,  1.38s/it]
 58%|█████▊    | 719/1250 [16:41<12:14,  1.38s/it]
 58%|█████▊    | 720/1250 [16:43<12:11,  1.38s/it]
 58%|█████▊    | 721/1250 [16:44<12:10,  1.38s/it]
 58%|█████▊    | 722/1250 [16:45<12:09,  1.38s/it]
 58%|█████▊    | 723/1250 [16:47<12:07,  1.38s/it]
 58%|█████▊    | 724/1250 [16:48<12:04,  1.38s/it]
 58%|█████▊    | 725/1250 [16:49<12:03,  1.38s/it]
                                                  

 58%|█████▊    | 725/1250 [16:50<12:03,  1.38s/it]
 58%|█████▊    | 726/1250 [16:51<12:05,  1.38s/it]
 58%|█████▊    | 727/1250 [16:52<12:03,  1.38s/it]
 58%|█████▊    | 728/1250 [16:54<12:01,  1.38s/it]
 58%|█████▊    | 729/1250 [16:55<12:00,  1.38s/it]
 58%|█████▊    | 730/1250 [16:56<11:58,  1.38s/it]
 58%|█████▊    | 731/1250 [16:58<11:56,  1.38s/it]
 59%|█████▊    | 732/1250 [16:59<11:55,  1.38s/it]
 59%|█████▊    | 733/1250 [17:01<11:53,  1.38s/it]
 59%|█████▊    | 734/1250 [17:02<11:52,  1.38s/it]
 59%|█████▉    | 735/1250 [17:03<11:52,  1.38s/it]
 59%|█████▉    | 736/1250 [17:05<11:51,  1.38s/it]
 59%|█████▉    | 737/1250 [17:06<11:49,  1.38s/it]
 59%|█████▉    | 738/1250 [17:07<11:47,  1.38s/it]
 59%|█████▉    | 739/1250 [17:09<12:17,  1.44s/it]
 59%|█████▉    | 740/1250 [17:10<12:05,  1.42s/it]
 59%|█████▉    | 741/1250 [17:12<11:57,  1.41s/it]
 59%|█████▉    | 742/1250 [17:13<11:50,  1.40s/it]
 59%|█████▉    | 743/1250 [17:15<11:46,  1.39s/it]
 60%|█████▉    | 744/1250 [17:16<11:43,  1.39s/it]
 60%|█████▉    | 745/1250 [17:17<11:39,  1.39s/it]
 60%|█████▉    | 746/1250 [17:19<12:09,  1.45s/it]
 60%|█████▉    | 747/1250 [17:20<11:55,  1.42s/it]
 60%|█████▉    | 748/1250 [17:22<11:48,  1.41s/it]
 60%|█████▉    | 749/1250 [17:23<11:41,  1.40s/it]
 60%|██████    | 750/1250 [17:24<11:36,  1.39s/it]
                                                  

 60%|██████    | 750/1250 [17:24<11:36,  1.39s/it]
 60%|██████    | 751/1250 [17:26<11:37,  1.40s/it]
 60%|██████    | 752/1250 [17:27<11:33,  1.39s/it]
 60%|██████    | 753/1250 [17:29<11:30,  1.39s/it]
 60%|██████    | 754/1250 [17:30<11:27,  1.39s/it]
 60%|██████    | 755/1250 [17:31<11:24,  1.38s/it]
 60%|██████    | 756/1250 [17:33<11:20,  1.38s/it]
 61%|██████    | 757/1250 [17:34<11:19,  1.38s/it]
 61%|██████    | 758/1250 [17:35<11:19,  1.38s/it]
 61%|██████    | 759/1250 [17:37<11:17,  1.38s/it]
 61%|██████    | 760/1250 [17:38<11:17,  1.38s/it]
 61%|██████    | 761/1250 [17:40<11:14,  1.38s/it]
 61%|██████    | 762/1250 [17:41<11:11,  1.38s/it]
 61%|██████    | 763/1250 [17:42<11:10,  1.38s/it]
 61%|██████    | 764/1250 [17:44<11:09,  1.38s/it]
 61%|██████    | 765/1250 [17:45<11:08,  1.38s/it]
 61%|██████▏   | 766/1250 [17:46<11:07,  1.38s/it]
 61%|██████▏   | 767/1250 [17:48<11:05,  1.38s/it]
 61%|██████▏   | 768/1250 [17:49<11:04,  1.38s/it]
 62%|██████▏   | 769/1250 [17:51<11:04,  1.38s/it]
 62%|██████▏   | 770/1250 [17:52<11:02,  1.38s/it]
 62%|██████▏   | 771/1250 [17:53<11:00,  1.38s/it]
 62%|██████▏   | 772/1250 [17:55<10:58,  1.38s/it]
 62%|██████▏   | 773/1250 [17:56<10:57,  1.38s/it]
 62%|██████▏   | 774/1250 [17:58<10:54,  1.38s/it]
 62%|██████▏   | 775/1250 [17:59<10:53,  1.38s/it]
                                                  

 62%|██████▏   | 775/1250 [17:59<10:53,  1.38s/it]
 62%|██████▏   | 776/1250 [18:00<10:54,  1.38s/it]
 62%|██████▏   | 777/1250 [18:02<10:53,  1.38s/it]
 62%|██████▏   | 778/1250 [18:03<10:49,  1.38s/it]
 62%|██████▏   | 779/1250 [18:04<10:48,  1.38s/it]
 62%|██████▏   | 780/1250 [18:06<10:47,  1.38s/it]
 62%|██████▏   | 781/1250 [18:07<10:45,  1.38s/it]
 63%|██████▎   | 782/1250 [18:09<10:44,  1.38s/it]
 63%|██████▎   | 783/1250 [18:10<10:44,  1.38s/it]
 63%|██████▎   | 784/1250 [18:11<10:43,  1.38s/it]
 63%|██████▎   | 785/1250 [18:13<10:42,  1.38s/it]
 63%|██████▎   | 786/1250 [18:14<10:40,  1.38s/it]
 63%|██████▎   | 787/1250 [18:15<10:39,  1.38s/it]
 63%|██████▎   | 788/1250 [18:17<10:38,  1.38s/it]
 63%|██████▎   | 789/1250 [18:18<10:36,  1.38s/it]
 63%|██████▎   | 790/1250 [18:20<10:35,  1.38s/it]
 63%|██████▎   | 791/1250 [18:21<10:33,  1.38s/it]
 63%|██████▎   | 792/1250 [18:22<10:31,  1.38s/it]
 63%|██████▎   | 793/1250 [18:24<10:30,  1.38s/it]
 64%|██████▎   | 794/1250 [18:25<10:29,  1.38s/it]
 64%|██████▎   | 795/1250 [18:26<10:28,  1.38s/it]
 64%|██████▎   | 796/1250 [18:28<10:26,  1.38s/it]
 64%|██████▍   | 797/1250 [18:29<10:25,  1.38s/it]
 64%|██████▍   | 798/1250 [18:31<10:23,  1.38s/it]
 64%|██████▍   | 799/1250 [18:32<10:22,  1.38s/it]
 64%|██████▍   | 800/1250 [18:33<10:21,  1.38s/it]
                                                  

 64%|██████▍   | 800/1250 [18:33<10:21,  1.38s/it]
 64%|██████▍   | 801/1250 [18:35<10:20,  1.38s/it]
 64%|██████▍   | 802/1250 [18:36<10:18,  1.38s/it]
 64%|██████▍   | 803/1250 [18:38<10:17,  1.38s/it]
 64%|██████▍   | 804/1250 [18:39<10:16,  1.38s/it]
 64%|██████▍   | 805/1250 [18:40<10:14,  1.38s/it]
 64%|██████▍   | 806/1250 [18:42<10:12,  1.38s/it]
 65%|██████▍   | 807/1250 [18:43<10:12,  1.38s/it]
 65%|██████▍   | 808/1250 [18:44<10:10,  1.38s/it]
 65%|██████▍   | 809/1250 [18:46<10:10,  1.38s/it]
 65%|██████▍   | 810/1250 [18:47<10:07,  1.38s/it]
 65%|██████▍   | 811/1250 [18:49<10:06,  1.38s/it]
 65%|██████▍   | 812/1250 [18:50<10:05,  1.38s/it]
 65%|██████▌   | 813/1250 [18:51<10:03,  1.38s/it]
 65%|██████▌   | 814/1250 [18:53<10:01,  1.38s/it]
 65%|██████▌   | 815/1250 [18:54<10:00,  1.38s/it]
 65%|██████▌   | 816/1250 [18:55<09:58,  1.38s/it]
 65%|██████▌   | 817/1250 [18:57<09:57,  1.38s/it]
 65%|██████▌   | 818/1250 [18:58<09:56,  1.38s/it]
 66%|██████▌   | 819/1250 [19:00<10:22,  1.44s/it]
 66%|██████▌   | 820/1250 [19:01<10:12,  1.42s/it]
 66%|██████▌   | 821/1250 [19:03<10:06,  1.41s/it]
 66%|██████▌   | 822/1250 [19:04<10:00,  1.40s/it]
 66%|██████▌   | 823/1250 [19:05<09:56,  1.40s/it]
 66%|██████▌   | 824/1250 [19:07<09:53,  1.39s/it]
 66%|██████▌   | 825/1250 [19:08<09:50,  1.39s/it]
                                                  

 66%|██████▌   | 825/1250 [19:08<09:50,  1.39s/it]
 66%|██████▌   | 826/1250 [19:10<09:50,  1.39s/it]
 66%|██████▌   | 827/1250 [19:11<09:47,  1.39s/it]
 66%|██████▌   | 828/1250 [19:12<09:45,  1.39s/it]
 66%|██████▋   | 829/1250 [19:14<09:43,  1.39s/it]
 66%|██████▋   | 830/1250 [19:15<09:41,  1.38s/it]
 66%|██████▋   | 831/1250 [19:16<09:39,  1.38s/it]
 67%|██████▋   | 832/1250 [19:18<09:37,  1.38s/it]
 67%|██████▋   | 833/1250 [19:19<09:36,  1.38s/it]
 67%|██████▋   | 834/1250 [19:21<09:34,  1.38s/it]
 67%|██████▋   | 835/1250 [19:22<09:33,  1.38s/it]
 67%|██████▋   | 836/1250 [19:23<09:31,  1.38s/it]
 67%|██████▋   | 837/1250 [19:25<09:29,  1.38s/it]
 67%|██████▋   | 838/1250 [19:26<09:28,  1.38s/it]
 67%|██████▋   | 839/1250 [19:27<09:27,  1.38s/it]
 67%|██████▋   | 840/1250 [19:29<09:27,  1.38s/it]
 67%|██████▋   | 841/1250 [19:30<09:24,  1.38s/it]
 67%|██████▋   | 842/1250 [19:32<09:23,  1.38s/it]
 67%|██████▋   | 843/1250 [19:33<09:22,  1.38s/it]
 68%|██████▊   | 844/1250 [19:34<09:21,  1.38s/it]
 68%|██████▊   | 845/1250 [19:36<09:19,  1.38s/it]
 68%|██████▊   | 846/1250 [19:37<09:18,  1.38s/it]
 68%|██████▊   | 847/1250 [19:39<09:16,  1.38s/it]
 68%|██████▊   | 848/1250 [19:40<09:15,  1.38s/it]
 68%|██████▊   | 849/1250 [19:41<09:13,  1.38s/it]
 68%|██████▊   | 850/1250 [19:43<09:11,  1.38s/it]
                                                  

 68%|██████▊   | 850/1250 [19:43<09:11,  1.38s/it]
 68%|██████▊   | 851/1250 [19:44<09:13,  1.39s/it]
 68%|██████▊   | 852/1250 [19:45<09:11,  1.39s/it]
 68%|██████▊   | 853/1250 [19:47<09:08,  1.38s/it]
 68%|██████▊   | 854/1250 [19:48<09:07,  1.38s/it]
 68%|██████▊   | 855/1250 [19:50<09:05,  1.38s/it]
 68%|██████▊   | 856/1250 [19:51<09:04,  1.38s/it]
 69%|██████▊   | 857/1250 [19:52<09:02,  1.38s/it]
 69%|██████▊   | 858/1250 [19:54<09:00,  1.38s/it]
 69%|██████▊   | 859/1250 [19:55<08:58,  1.38s/it]
 69%|██████▉   | 860/1250 [19:57<08:58,  1.38s/it]
 69%|██████▉   | 861/1250 [19:58<08:56,  1.38s/it]
 69%|██████▉   | 862/1250 [19:59<08:55,  1.38s/it]
 69%|██████▉   | 863/1250 [20:01<08:53,  1.38s/it]
 69%|██████▉   | 864/1250 [20:02<08:52,  1.38s/it]
 69%|██████▉   | 865/1250 [20:03<08:51,  1.38s/it]
 69%|██████▉   | 866/1250 [20:05<08:50,  1.38s/it]
 69%|██████▉   | 867/1250 [20:06<08:49,  1.38s/it]
 69%|██████▉   | 868/1250 [20:08<08:47,  1.38s/it]
 70%|██████▉   | 869/1250 [20:09<08:45,  1.38s/it]
 70%|██████▉   | 870/1250 [20:10<08:44,  1.38s/it]
 70%|██████▉   | 871/1250 [20:12<08:43,  1.38s/it]
 70%|██████▉   | 872/1250 [20:13<08:40,  1.38s/it]
 70%|██████▉   | 873/1250 [20:14<08:38,  1.38s/it]
 70%|██████▉   | 874/1250 [20:16<08:37,  1.38s/it]
 70%|███████   | 875/1250 [20:17<08:36,  1.38s/it]
                                                  

 70%|███████   | 875/1250 [20:17<08:36,  1.38s/it]
 70%|███████   | 876/1250 [20:19<08:38,  1.39s/it]
 70%|███████   | 877/1250 [20:20<08:35,  1.38s/it]
 70%|███████   | 878/1250 [20:21<08:34,  1.38s/it]
 70%|███████   | 879/1250 [20:23<08:32,  1.38s/it]
 70%|███████   | 880/1250 [20:24<08:31,  1.38s/it]
 70%|███████   | 881/1250 [20:26<08:30,  1.38s/it]
 71%|███████   | 882/1250 [20:27<08:28,  1.38s/it]
 71%|███████   | 883/1250 [20:28<08:26,  1.38s/it]
 71%|███████   | 884/1250 [20:30<08:25,  1.38s/it]
 71%|███████   | 885/1250 [20:31<08:24,  1.38s/it]
 71%|███████   | 886/1250 [20:32<08:22,  1.38s/it]
 71%|███████   | 887/1250 [20:34<08:20,  1.38s/it]
 71%|███████   | 888/1250 [20:35<08:17,  1.38s/it]
 71%|███████   | 889/1250 [20:37<08:16,  1.38s/it]
 71%|███████   | 890/1250 [20:38<08:16,  1.38s/it]
 71%|███████▏  | 891/1250 [20:39<08:14,  1.38s/it]
 71%|███████▏  | 892/1250 [20:41<08:13,  1.38s/it]
 71%|███████▏  | 893/1250 [20:42<08:12,  1.38s/it]
 72%|███████▏  | 894/1250 [20:43<08:10,  1.38s/it]
 72%|███████▏  | 895/1250 [20:45<08:09,  1.38s/it]
 72%|███████▏  | 896/1250 [20:46<08:08,  1.38s/it]
 72%|███████▏  | 897/1250 [20:48<08:07,  1.38s/it]
 72%|███████▏  | 898/1250 [20:49<08:06,  1.38s/it]
 72%|███████▏  | 899/1250 [20:50<08:04,  1.38s/it]
 72%|███████▏  | 900/1250 [20:52<08:03,  1.38s/it]
                                                  

 72%|███████▏  | 900/1250 [20:52<08:03,  1.38s/it]
 72%|███████▏  | 901/1250 [20:53<08:04,  1.39s/it]
 72%|███████▏  | 902/1250 [20:54<08:02,  1.39s/it]
 72%|███████▏  | 903/1250 [20:56<08:00,  1.39s/it]
 72%|███████▏  | 904/1250 [20:57<07:58,  1.38s/it]
 72%|███████▏  | 905/1250 [20:59<07:56,  1.38s/it]
 72%|███████▏  | 906/1250 [21:00<07:55,  1.38s/it]
 73%|███████▎  | 907/1250 [21:01<07:53,  1.38s/it]
 73%|███████▎  | 908/1250 [21:03<07:51,  1.38s/it]
 73%|███████▎  | 909/1250 [21:04<07:50,  1.38s/it]
 73%|███████▎  | 910/1250 [21:06<07:48,  1.38s/it]
 73%|███████▎  | 911/1250 [21:07<07:47,  1.38s/it]
 73%|███████▎  | 912/1250 [21:08<07:46,  1.38s/it]
 73%|███████▎  | 913/1250 [21:10<07:44,  1.38s/it]
 73%|███████▎  | 914/1250 [21:11<07:42,  1.38s/it]
 73%|███████▎  | 915/1250 [21:12<07:41,  1.38s/it]
 73%|███████▎  | 916/1250 [21:14<07:40,  1.38s/it]
 73%|███████▎  | 917/1250 [21:15<07:38,  1.38s/it]
 73%|███████▎  | 918/1250 [21:17<07:37,  1.38s/it]
 74%|███████▎  | 919/1250 [21:18<07:36,  1.38s/it]
 74%|███████▎  | 920/1250 [21:19<07:34,  1.38s/it]
 74%|███████▎  | 921/1250 [21:21<07:33,  1.38s/it]
 74%|███████▍  | 922/1250 [21:22<07:31,  1.38s/it]
 74%|███████▍  | 923/1250 [21:23<07:30,  1.38s/it]
 74%|███████▍  | 924/1250 [21:25<07:29,  1.38s/it]
 74%|███████▍  | 925/1250 [21:26<07:27,  1.38s/it]
                                                  

 74%|███████▍  | 925/1250 [21:26<07:27,  1.38s/it]
 74%|███████▍  | 926/1250 [21:28<07:29,  1.39s/it]
 74%|███████▍  | 927/1250 [21:29<07:27,  1.38s/it]
 74%|███████▍  | 928/1250 [21:30<07:25,  1.38s/it]
 74%|███████▍  | 929/1250 [21:32<07:23,  1.38s/it]
 74%|███████▍  | 930/1250 [21:33<07:22,  1.38s/it]
 74%|███████▍  | 931/1250 [21:35<07:20,  1.38s/it]
 75%|███████▍  | 932/1250 [21:36<07:19,  1.38s/it]
 75%|███████▍  | 933/1250 [21:37<07:17,  1.38s/it]
 75%|███████▍  | 934/1250 [21:39<07:16,  1.38s/it]
 75%|███████▍  | 935/1250 [21:40<07:14,  1.38s/it]
 75%|███████▍  | 936/1250 [21:41<07:13,  1.38s/it]
 75%|███████▍  | 937/1250 [21:43<07:11,  1.38s/it]
 75%|███████▌  | 938/1250 [21:44<07:11,  1.38s/it]
 75%|███████▌  | 939/1250 [21:46<07:09,  1.38s/it]
 75%|███████▌  | 940/1250 [21:47<07:08,  1.38s/it]
 75%|███████▌  | 941/1250 [21:48<07:07,  1.38s/it]
 75%|███████▌  | 942/1250 [21:50<07:05,  1.38s/it]
 75%|███████▌  | 943/1250 [21:51<07:03,  1.38s/it]
 76%|███████▌  | 944/1250 [21:52<07:01,  1.38s/it]
 76%|███████▌  | 945/1250 [21:54<07:00,  1.38s/it]
 76%|███████▌  | 946/1250 [21:55<07:00,  1.38s/it]
 76%|███████▌  | 947/1250 [21:57<07:16,  1.44s/it]
 76%|███████▌  | 948/1250 [21:58<07:09,  1.42s/it]
 76%|███████▌  | 949/1250 [22:00<07:04,  1.41s/it]
 76%|███████▌  | 950/1250 [22:01<06:59,  1.40s/it]
                                                  

 76%|███████▌  | 950/1250 [22:01<06:59,  1.40s/it]
 76%|███████▌  | 951/1250 [22:02<06:58,  1.40s/it]
 76%|███████▌  | 952/1250 [22:04<06:54,  1.39s/it]
 76%|███████▌  | 953/1250 [22:05<06:51,  1.39s/it]
 76%|███████▋  | 954/1250 [22:06<06:49,  1.38s/it]
 76%|███████▋  | 955/1250 [22:08<06:48,  1.38s/it]
 76%|███████▋  | 956/1250 [22:09<06:46,  1.38s/it]
 77%|███████▋  | 957/1250 [22:11<06:45,  1.38s/it]
 77%|███████▋  | 958/1250 [22:12<06:43,  1.38s/it]
 77%|███████▋  | 959/1250 [22:13<06:42,  1.38s/it]
 77%|███████▋  | 960/1250 [22:15<06:40,  1.38s/it]
 77%|███████▋  | 961/1250 [22:16<06:39,  1.38s/it]
 77%|███████▋  | 962/1250 [22:18<06:37,  1.38s/it]
 77%|███████▋  | 963/1250 [22:19<06:36,  1.38s/it]
 77%|███████▋  | 964/1250 [22:20<06:35,  1.38s/it]
 77%|███████▋  | 965/1250 [22:22<06:33,  1.38s/it]
 77%|███████▋  | 966/1250 [22:23<06:32,  1.38s/it]
 77%|███████▋  | 967/1250 [22:24<06:30,  1.38s/it]
 77%|███████▋  | 968/1250 [22:26<06:29,  1.38s/it]
 78%|███████▊  | 969/1250 [22:27<06:27,  1.38s/it]
 78%|███████▊  | 970/1250 [22:29<06:26,  1.38s/it]
 78%|███████▊  | 971/1250 [22:30<06:24,  1.38s/it]
 78%|███████▊  | 972/1250 [22:31<06:23,  1.38s/it]
 78%|███████▊  | 973/1250 [22:33<06:22,  1.38s/it]
 78%|███████▊  | 974/1250 [22:34<06:21,  1.38s/it]
 78%|███████▊  | 975/1250 [22:35<06:19,  1.38s/it]
                                                  

 78%|███████▊  | 975/1250 [22:35<06:19,  1.38s/it]
 78%|███████▊  | 976/1250 [22:37<06:19,  1.39s/it]
 78%|███████▊  | 977/1250 [22:38<06:17,  1.38s/it]
 78%|███████▊  | 978/1250 [22:40<06:16,  1.38s/it]
 78%|███████▊  | 979/1250 [22:41<06:13,  1.38s/it]
 78%|███████▊  | 980/1250 [22:42<06:12,  1.38s/it]
 78%|███████▊  | 981/1250 [22:44<06:10,  1.38s/it]
 79%|███████▊  | 982/1250 [22:45<06:08,  1.38s/it]
 79%|███████▊  | 983/1250 [22:46<06:07,  1.38s/it]
 79%|███████▊  | 984/1250 [22:48<06:06,  1.38s/it]
 79%|███████▉  | 985/1250 [22:49<06:05,  1.38s/it]
 79%|███████▉  | 986/1250 [22:51<06:04,  1.38s/it]
 79%|███████▉  | 987/1250 [22:52<06:02,  1.38s/it]
 79%|███████▉  | 988/1250 [22:53<06:01,  1.38s/it]
 79%|███████▉  | 989/1250 [22:55<05:59,  1.38s/it]
 79%|███████▉  | 990/1250 [22:56<05:58,  1.38s/it]
 79%|███████▉  | 991/1250 [22:58<05:57,  1.38s/it]
 79%|███████▉  | 992/1250 [22:59<05:55,  1.38s/it]
 79%|███████▉  | 993/1250 [23:00<05:53,  1.38s/it]
 80%|███████▉  | 994/1250 [23:02<05:53,  1.38s/it]
 80%|███████▉  | 995/1250 [23:03<05:51,  1.38s/it]
 80%|███████▉  | 996/1250 [23:04<05:50,  1.38s/it]
 80%|███████▉  | 997/1250 [23:06<05:48,  1.38s/it]
 80%|███████▉  | 998/1250 [23:07<05:47,  1.38s/it]
 80%|███████▉  | 999/1250 [23:09<05:44,  1.37s/it]
 80%|████████  | 1000/1250 [23:10<05:43,  1.38s/it]
                                                   

 80%|████████  | 1000/1250 [23:10<05:43,  1.38s/it]
 80%|████████  | 1001/1250 [23:11<05:44,  1.38s/it]
 80%|████████  | 1002/1250 [23:13<05:43,  1.38s/it]
 80%|████████  | 1003/1250 [23:14<05:40,  1.38s/it]
 80%|████████  | 1004/1250 [23:15<05:38,  1.38s/it]
 80%|████████  | 1005/1250 [23:17<05:37,  1.38s/it]
 80%|████████  | 1006/1250 [23:18<05:36,  1.38s/it]
 81%|████████  | 1007/1250 [23:20<05:35,  1.38s/it]
 81%|████████  | 1008/1250 [23:21<05:33,  1.38s/it]
 81%|████████  | 1009/1250 [23:22<05:32,  1.38s/it]
 81%|████████  | 1010/1250 [23:24<05:31,  1.38s/it]
 81%|████████  | 1011/1250 [23:25<05:30,  1.38s/it]
 81%|████████  | 1012/1250 [23:27<05:43,  1.45s/it]
 81%|████████  | 1013/1250 [23:28<05:37,  1.43s/it]
 81%|████████  | 1014/1250 [23:29<05:33,  1.41s/it]
 81%|████████  | 1015/1250 [23:31<05:29,  1.40s/it]
 81%|████████▏ | 1016/1250 [23:32<05:26,  1.39s/it]
 81%|████████▏ | 1017/1250 [23:34<05:24,  1.39s/it]
 81%|████████▏ | 1018/1250 [23:35<05:21,  1.39s/it]
 82%|████████▏ | 1019/1250 [23:36<05:19,  1.38s/it]
 82%|████████▏ | 1020/1250 [23:38<05:18,  1.38s/it]
 82%|████████▏ | 1021/1250 [23:39<05:16,  1.38s/it]
 82%|████████▏ | 1022/1250 [23:41<05:15,  1.38s/it]
 82%|████████▏ | 1023/1250 [23:42<05:13,  1.38s/it]
 82%|████████▏ | 1024/1250 [23:43<05:12,  1.38s/it]
 82%|████████▏ | 1025/1250 [23:45<05:10,  1.38s/it]
                                                   

 82%|████████▏ | 1025/1250 [23:45<05:10,  1.38s/it]
 82%|████████▏ | 1026/1250 [23:46<05:09,  1.38s/it]
 82%|████████▏ | 1027/1250 [23:47<05:08,  1.38s/it]
 82%|████████▏ | 1028/1250 [23:49<05:06,  1.38s/it]
 82%|████████▏ | 1029/1250 [23:50<05:05,  1.38s/it]
 82%|████████▏ | 1030/1250 [23:52<05:03,  1.38s/it]
 82%|████████▏ | 1031/1250 [23:53<05:02,  1.38s/it]
 83%|████████▎ | 1032/1250 [23:54<05:01,  1.38s/it]
 83%|████████▎ | 1033/1250 [23:56<04:59,  1.38s/it]
 83%|████████▎ | 1034/1250 [23:57<04:57,  1.38s/it]
 83%|████████▎ | 1035/1250 [23:58<04:56,  1.38s/it]
 83%|████████▎ | 1036/1250 [24:00<04:55,  1.38s/it]
 83%|████████▎ | 1037/1250 [24:01<04:54,  1.38s/it]
 83%|████████▎ | 1038/1250 [24:03<04:52,  1.38s/it]
 83%|████████▎ | 1039/1250 [24:04<04:50,  1.38s/it]
 83%|████████▎ | 1040/1250 [24:05<04:49,  1.38s/it]
 83%|████████▎ | 1041/1250 [24:07<04:47,  1.38s/it]
 83%|████████▎ | 1042/1250 [24:08<04:46,  1.38s/it]
 83%|████████▎ | 1043/1250 [24:09<04:44,  1.38s/it]
 84%|████████▎ | 1044/1250 [24:11<04:43,  1.38s/it]
 84%|████████▎ | 1045/1250 [24:12<04:42,  1.38s/it]
 84%|████████▎ | 1046/1250 [24:14<04:40,  1.38s/it]
 84%|████████▍ | 1047/1250 [24:15<04:39,  1.37s/it]
 84%|████████▍ | 1048/1250 [24:16<04:38,  1.38s/it]
 84%|████████▍ | 1049/1250 [24:18<04:37,  1.38s/it]
 84%|████████▍ | 1050/1250 [24:19<04:36,  1.38s/it]
                                                   

 84%|████████▍ | 1050/1250 [24:19<04:36,  1.38s/it]
 84%|████████▍ | 1051/1250 [24:21<04:36,  1.39s/it]
 84%|████████▍ | 1052/1250 [24:22<04:34,  1.39s/it]
 84%|████████▍ | 1053/1250 [24:23<04:33,  1.39s/it]
 84%|████████▍ | 1054/1250 [24:25<04:31,  1.38s/it]
 84%|████████▍ | 1055/1250 [24:26<04:29,  1.38s/it]
 84%|████████▍ | 1056/1250 [24:27<04:28,  1.38s/it]
 85%|████████▍ | 1057/1250 [24:29<04:26,  1.38s/it]
 85%|████████▍ | 1058/1250 [24:30<04:24,  1.38s/it]
 85%|████████▍ | 1059/1250 [24:32<04:35,  1.44s/it]
 85%|████████▍ | 1060/1250 [24:33<04:30,  1.42s/it]
 85%|████████▍ | 1061/1250 [24:35<04:26,  1.41s/it]
 85%|████████▍ | 1062/1250 [24:36<04:23,  1.40s/it]
 85%|████████▌ | 1063/1250 [24:37<04:20,  1.39s/it]
 85%|████████▌ | 1064/1250 [24:39<04:18,  1.39s/it]
 85%|████████▌ | 1065/1250 [24:40<04:16,  1.39s/it]
 85%|████████▌ | 1066/1250 [24:41<04:14,  1.39s/it]
 85%|████████▌ | 1067/1250 [24:43<04:13,  1.38s/it]
 85%|████████▌ | 1068/1250 [24:44<04:11,  1.38s/it]
 86%|████████▌ | 1069/1250 [24:46<04:09,  1.38s/it]
 86%|████████▌ | 1070/1250 [24:47<04:08,  1.38s/it]
 86%|████████▌ | 1071/1250 [24:48<04:07,  1.38s/it]
 86%|████████▌ | 1072/1250 [24:50<04:05,  1.38s/it]
 86%|████████▌ | 1073/1250 [24:51<04:04,  1.38s/it]
 86%|████████▌ | 1074/1250 [24:52<04:02,  1.38s/it]
 86%|████████▌ | 1075/1250 [24:54<04:01,  1.38s/it]
                                                   

 86%|████████▌ | 1075/1250 [24:54<04:01,  1.38s/it]
 86%|████████▌ | 1076/1250 [24:55<04:01,  1.39s/it]
 86%|████████▌ | 1077/1250 [24:57<03:59,  1.38s/it]
 86%|████████▌ | 1078/1250 [24:58<03:58,  1.38s/it]
 86%|████████▋ | 1079/1250 [24:59<03:56,  1.38s/it]
 86%|████████▋ | 1080/1250 [25:01<03:54,  1.38s/it]
 86%|████████▋ | 1081/1250 [25:02<03:53,  1.38s/it]
 87%|████████▋ | 1082/1250 [25:04<03:51,  1.38s/it]
 87%|████████▋ | 1083/1250 [25:05<03:50,  1.38s/it]
 87%|████████▋ | 1084/1250 [25:06<03:49,  1.38s/it]
 87%|████████▋ | 1085/1250 [25:08<03:46,  1.38s/it]
 87%|████████▋ | 1086/1250 [25:09<03:45,  1.38s/it]
 87%|████████▋ | 1087/1250 [25:10<03:44,  1.38s/it]
 87%|████████▋ | 1088/1250 [25:12<03:43,  1.38s/it]
 87%|████████▋ | 1089/1250 [25:13<03:41,  1.38s/it]
 87%|████████▋ | 1090/1250 [25:15<03:40,  1.38s/it]
 87%|████████▋ | 1091/1250 [25:16<03:39,  1.38s/it]
 87%|████████▋ | 1092/1250 [25:17<03:37,  1.38s/it]
 87%|████████▋ | 1093/1250 [25:19<03:36,  1.38s/it]
 88%|████████▊ | 1094/1250 [25:20<03:35,  1.38s/it]
 88%|████████▊ | 1095/1250 [25:21<03:33,  1.38s/it]
 88%|████████▊ | 1096/1250 [25:23<03:32,  1.38s/it]
 88%|████████▊ | 1097/1250 [25:24<03:30,  1.38s/it]
 88%|████████▊ | 1098/1250 [25:26<03:29,  1.38s/it]
 88%|████████▊ | 1099/1250 [25:27<03:28,  1.38s/it]
 88%|████████▊ | 1100/1250 [25:28<03:26,  1.38s/it]
                                                   

 88%|████████▊ | 1100/1250 [25:28<03:26,  1.38s/it]
 88%|████████▊ | 1101/1250 [25:30<03:26,  1.39s/it]
 88%|████████▊ | 1102/1250 [25:31<03:24,  1.38s/it]
 88%|████████▊ | 1103/1250 [25:33<03:23,  1.38s/it]
 88%|████████▊ | 1104/1250 [25:34<03:21,  1.38s/it]
 88%|████████▊ | 1105/1250 [25:35<03:20,  1.38s/it]
 88%|████████▊ | 1106/1250 [25:37<03:18,  1.38s/it]
 89%|████████▊ | 1107/1250 [25:38<03:17,  1.38s/it]
 89%|████████▊ | 1108/1250 [25:39<03:15,  1.38s/it]
 89%|████████▊ | 1109/1250 [25:41<03:14,  1.38s/it]
 89%|████████▉ | 1110/1250 [25:42<03:13,  1.38s/it]
 89%|████████▉ | 1111/1250 [25:44<03:11,  1.38s/it]
 89%|████████▉ | 1112/1250 [25:45<03:10,  1.38s/it]
 89%|████████▉ | 1113/1250 [25:46<03:08,  1.38s/it]
 89%|████████▉ | 1114/1250 [25:48<03:07,  1.38s/it]
 89%|████████▉ | 1115/1250 [25:49<03:05,  1.38s/it]
 89%|████████▉ | 1116/1250 [25:50<03:04,  1.38s/it]
 89%|████████▉ | 1117/1250 [25:52<03:03,  1.38s/it]
 89%|████████▉ | 1118/1250 [25:53<03:10,  1.44s/it]
 90%|████████▉ | 1119/1250 [25:55<03:06,  1.42s/it]
 90%|████████▉ | 1120/1250 [25:56<03:03,  1.41s/it]
 90%|████████▉ | 1121/1250 [25:58<03:00,  1.40s/it]
 90%|████████▉ | 1122/1250 [25:59<02:58,  1.40s/it]
 90%|████████▉ | 1123/1250 [26:00<02:56,  1.39s/it]
 90%|████████▉ | 1124/1250 [26:02<02:54,  1.39s/it]
 90%|█████████ | 1125/1250 [26:03<02:53,  1.39s/it]
                                                   

 90%|█████████ | 1125/1250 [26:03<02:53,  1.39s/it]
 90%|█████████ | 1126/1250 [26:04<02:51,  1.39s/it]
 90%|█████████ | 1127/1250 [26:06<02:50,  1.38s/it]
 90%|█████████ | 1128/1250 [26:07<02:48,  1.38s/it]
 90%|█████████ | 1129/1250 [26:09<02:47,  1.38s/it]
 90%|█████████ | 1130/1250 [26:10<02:45,  1.38s/it]
 90%|█████████ | 1131/1250 [26:11<02:43,  1.38s/it]
 91%|█████████ | 1132/1250 [26:13<02:42,  1.38s/it]
 91%|█████████ | 1133/1250 [26:14<02:41,  1.38s/it]
 91%|█████████ | 1134/1250 [26:15<02:40,  1.38s/it]
 91%|█████████ | 1135/1250 [26:17<02:38,  1.38s/it]
 91%|█████████ | 1136/1250 [26:18<02:42,  1.43s/it]
 91%|█████████ | 1137/1250 [26:20<02:39,  1.41s/it]
 91%|█████████ | 1138/1250 [26:21<02:36,  1.40s/it]
 91%|█████████ | 1139/1250 [26:23<02:34,  1.40s/it]
 91%|█████████ | 1140/1250 [26:24<02:33,  1.39s/it]
 91%|█████████▏| 1141/1250 [26:25<02:31,  1.39s/it]
 91%|█████████▏| 1142/1250 [26:27<02:29,  1.39s/it]
 91%|█████████▏| 1143/1250 [26:28<02:28,  1.38s/it]
 92%|█████████▏| 1144/1250 [26:29<02:26,  1.38s/it]
 92%|█████████▏| 1145/1250 [26:31<02:25,  1.38s/it]
 92%|█████████▏| 1146/1250 [26:32<02:23,  1.38s/it]
 92%|█████████▏| 1147/1250 [26:34<02:22,  1.38s/it]
 92%|█████████▏| 1148/1250 [26:35<02:20,  1.38s/it]
 92%|█████████▏| 1149/1250 [26:36<02:19,  1.38s/it]
 92%|█████████▏| 1150/1250 [26:38<02:18,  1.38s/it]
                                                   

 92%|█████████▏| 1150/1250 [26:38<02:18,  1.38s/it]
 92%|█████████▏| 1151/1250 [26:39<02:17,  1.39s/it]
 92%|█████████▏| 1152/1250 [26:41<02:16,  1.39s/it]
 92%|█████████▏| 1153/1250 [26:42<02:14,  1.38s/it]
 92%|█████████▏| 1154/1250 [26:43<02:12,  1.38s/it]
 92%|█████████▏| 1155/1250 [26:45<02:11,  1.38s/it]
 92%|█████████▏| 1156/1250 [26:46<02:09,  1.38s/it]
 93%|█████████▎| 1157/1250 [26:47<02:08,  1.38s/it]
 93%|█████████▎| 1158/1250 [26:49<02:07,  1.38s/it]
 93%|█████████▎| 1159/1250 [26:50<02:05,  1.38s/it]
 93%|█████████▎| 1160/1250 [26:52<02:04,  1.38s/it]
 93%|█████████▎| 1161/1250 [26:53<02:02,  1.38s/it]
 93%|█████████▎| 1162/1250 [26:54<02:01,  1.38s/it]
 93%|█████████▎| 1163/1250 [26:56<02:00,  1.38s/it]
 93%|█████████▎| 1164/1250 [26:57<01:58,  1.38s/it]
 93%|█████████▎| 1165/1250 [26:58<01:56,  1.38s/it]
 93%|█████████▎| 1166/1250 [27:00<01:55,  1.38s/it]
 93%|█████████▎| 1167/1250 [27:01<01:54,  1.38s/it]
 93%|█████████▎| 1168/1250 [27:03<01:53,  1.38s/it]
 94%|█████████▎| 1169/1250 [27:04<01:51,  1.38s/it]
 94%|█████████▎| 1170/1250 [27:05<01:50,  1.38s/it]
 94%|█████████▎| 1171/1250 [27:07<01:49,  1.38s/it]
 94%|█████████▍| 1172/1250 [27:08<01:47,  1.38s/it]
 94%|█████████▍| 1173/1250 [27:09<01:46,  1.38s/it]
 94%|█████████▍| 1174/1250 [27:11<01:45,  1.38s/it]
 94%|█████████▍| 1175/1250 [27:12<01:43,  1.38s/it]
                                                   

 94%|█████████▍| 1175/1250 [27:12<01:43,  1.38s/it]
 94%|█████████▍| 1176/1250 [27:14<01:42,  1.39s/it]
 94%|█████████▍| 1177/1250 [27:15<01:41,  1.39s/it]
 94%|█████████▍| 1178/1250 [27:16<01:39,  1.39s/it]
 94%|█████████▍| 1179/1250 [27:18<01:38,  1.38s/it]
 94%|█████████▍| 1180/1250 [27:19<01:36,  1.39s/it]
 94%|█████████▍| 1181/1250 [27:21<01:35,  1.38s/it]
 95%|█████████▍| 1182/1250 [27:22<01:34,  1.38s/it]
 95%|█████████▍| 1183/1250 [27:23<01:32,  1.38s/it]
 95%|█████████▍| 1184/1250 [27:25<01:31,  1.38s/it]
 95%|█████████▍| 1185/1250 [27:26<01:29,  1.38s/it]
 95%|█████████▍| 1186/1250 [27:27<01:28,  1.38s/it]
 95%|█████████▍| 1187/1250 [27:29<01:26,  1.38s/it]
 95%|█████████▌| 1188/1250 [27:30<01:25,  1.37s/it]
 95%|█████████▌| 1189/1250 [27:32<01:23,  1.37s/it]
 95%|█████████▌| 1190/1250 [27:33<01:22,  1.37s/it]
 95%|█████████▌| 1191/1250 [27:34<01:21,  1.38s/it]
 95%|█████████▌| 1192/1250 [27:36<01:19,  1.38s/it]
 95%|█████████▌| 1193/1250 [27:37<01:22,  1.44s/it]
 96%|█████████▌| 1194/1250 [27:39<01:19,  1.42s/it]
 96%|█████████▌| 1195/1250 [27:40<01:17,  1.41s/it]
 96%|█████████▌| 1196/1250 [27:41<01:15,  1.40s/it]
 96%|█████████▌| 1197/1250 [27:43<01:13,  1.39s/it]
 96%|█████████▌| 1198/1250 [27:44<01:12,  1.39s/it]
 96%|█████████▌| 1199/1250 [27:46<01:10,  1.39s/it]
 96%|█████████▌| 1200/1250 [27:47<01:09,  1.39s/it]
                                                   

 96%|█████████▌| 1200/1250 [27:47<01:09,  1.39s/it]
 96%|█████████▌| 1201/1250 [27:48<01:08,  1.39s/it]
 96%|█████████▌| 1202/1250 [27:50<01:06,  1.39s/it]
 96%|█████████▌| 1203/1250 [27:51<01:05,  1.39s/it]
 96%|█████████▋| 1204/1250 [27:53<01:03,  1.38s/it]
 96%|█████████▋| 1205/1250 [27:54<01:02,  1.38s/it]
 96%|█████████▋| 1206/1250 [27:55<01:00,  1.38s/it]
 97%|█████████▋| 1207/1250 [27:57<00:59,  1.38s/it]
 97%|█████████▋| 1208/1250 [27:58<00:58,  1.38s/it]
 97%|█████████▋| 1209/1250 [27:59<00:56,  1.38s/it]
 97%|█████████▋| 1210/1250 [28:01<00:55,  1.38s/it]
 97%|█████████▋| 1211/1250 [28:02<00:53,  1.38s/it]
 97%|█████████▋| 1212/1250 [28:04<00:52,  1.38s/it]
 97%|█████████▋| 1213/1250 [28:05<00:51,  1.38s/it]
 97%|█████████▋| 1214/1250 [28:06<00:49,  1.38s/it]
 97%|█████████▋| 1215/1250 [28:08<00:48,  1.38s/it]
 97%|█████████▋| 1216/1250 [28:09<00:46,  1.38s/it]
 97%|█████████▋| 1217/1250 [28:10<00:45,  1.38s/it]
 97%|█████████▋| 1218/1250 [28:12<00:44,  1.38s/it]
 98%|█████████▊| 1219/1250 [28:13<00:42,  1.38s/it]
 98%|█████████▊| 1220/1250 [28:15<00:41,  1.38s/it]
 98%|█████████▊| 1221/1250 [28:16<00:40,  1.38s/it]
 98%|█████████▊| 1222/1250 [28:17<00:38,  1.38s/it]
 98%|█████████▊| 1223/1250 [28:19<00:37,  1.38s/it]
 98%|█████████▊| 1224/1250 [28:20<00:35,  1.38s/it]
 98%|█████████▊| 1225/1250 [28:22<00:34,  1.38s/it]
                                                   

 98%|█████████▊| 1225/1250 [28:22<00:34,  1.38s/it]
 98%|█████████▊| 1226/1250 [28:23<00:33,  1.39s/it]
 98%|█████████▊| 1227/1250 [28:24<00:31,  1.38s/it]
 98%|█████████▊| 1228/1250 [28:26<00:30,  1.39s/it]
 98%|█████████▊| 1229/1250 [28:27<00:28,  1.38s/it]
 98%|█████████▊| 1230/1250 [28:28<00:27,  1.38s/it]
 98%|█████████▊| 1231/1250 [28:30<00:26,  1.38s/it]
 99%|█████████▊| 1232/1250 [28:31<00:24,  1.38s/it]
 99%|█████████▊| 1233/1250 [28:33<00:23,  1.38s/it]
 99%|█████████▊| 1234/1250 [28:34<00:22,  1.38s/it]
 99%|█████████▉| 1235/1250 [28:35<00:20,  1.38s/it]
 99%|█████████▉| 1236/1250 [28:37<00:19,  1.38s/it]
 99%|█████████▉| 1237/1250 [28:38<00:17,  1.37s/it]
 99%|█████████▉| 1238/1250 [28:39<00:16,  1.38s/it]
 99%|█████████▉| 1239/1250 [28:41<00:15,  1.38s/it]
 99%|█████████▉| 1240/1250 [28:42<00:13,  1.38s/it]
 99%|█████████▉| 1241/1250 [28:44<00:12,  1.38s/it]
 99%|█████████▉| 1242/1250 [28:45<00:11,  1.38s/it]
 99%|█████████▉| 1243/1250 [28:46<00:09,  1.38s/it]
100%|█████████▉| 1244/1250 [28:48<00:08,  1.38s/it]
100%|█████████▉| 1245/1250 [28:49<00:06,  1.38s/it]
100%|█████████▉| 1246/1250 [28:50<00:05,  1.38s/it]
100%|█████████▉| 1247/1250 [28:52<00:04,  1.38s/it]
100%|█████████▉| 1248/1250 [28:53<00:02,  1.38s/it]
100%|█████████▉| 1249/1250 [28:55<00:01,  1.38s/it]
100%|██████████| 1250/1250 [28:56<00:00,  1.38s/it]
                                                   

100%|██████████| 1250/1250 [28:56<00:00,  1.38s/it]Saving model checkpoint to data/output/mistral_lora_moe_economic_top1/checkpoint-1250
Configuration saved in data/output/mistral_lora_moe_economic_top1/checkpoint-1250/config.json
Configuration saved in data/output/mistral_lora_moe_economic_top1/checkpoint-1250/generation_config.json
The model is bigger than the maximum size per checkpoint (5GB) and is going to be split in 3 checkpoint shards. You can find where each parameters has been saved in the index located at data/output/mistral_lora_moe_economic_top1/checkpoint-1250/model.safetensors.index.json.
tokenizer config file saved in data/output/mistral_lora_moe_economic_top1/checkpoint-1250/tokenizer_config.json
Special tokens file saved in data/output/mistral_lora_moe_economic_top1/checkpoint-1250/special_tokens_map.json
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once


Training completed. Do not forget to share your model on huggingface.co/models =)



                                                   

100%|██████████| 1250/1250 [30:46<00:00,  1.38s/it]
100%|██████████| 1250/1250 [30:46<00:00,  1.48s/it]

***** Running Evaluation *****
  Num examples = 123
  Batch size = 1

  0%|          | 0/31 [00:00<?, ?it/s]
  6%|▋         | 2/31 [00:00<00:01, 16.22it/s]
 13%|█▎        | 4/31 [00:00<00:02, 10.54it/s]
 19%|█▉        | 6/31 [00:00<00:02,  9.53it/s]
 26%|██▌       | 8/31 [00:00<00:02,  9.08it/s]
 29%|██▉       | 9/31 [00:00<00:02,  8.94it/s]
 32%|███▏      | 10/31 [00:01<00:02,  8.80it/s]
 35%|███▌      | 11/31 [00:01<00:02,  8.73it/s]
 39%|███▊      | 12/31 [00:01<00:02,  8.65it/s]
 42%|████▏     | 13/31 [00:01<00:02,  8.58it/s]
 45%|████▌     | 14/31 [00:01<00:01,  8.55it/s]
 48%|████▊     | 15/31 [00:01<00:01,  8.54it/s]
 52%|█████▏    | 16/31 [00:01<00:01,  8.52it/s]
 55%|█████▍    | 17/31 [00:01<00:01,  8.49it/s]
 58%|█████▊    | 18/31 [00:02<00:01,  8.50it/s]
 61%|██████▏   | 19/31 [00:02<00:01,  8.52it/s]
 65%|██████▍   | 20/31 [00:02<00:01,  8.50it/s]
 68%|██████▊   | 21/31 [00:02<00:01,  8.52it/s]
 71%|███████   | 22/31 [00:02<00:01,  8.46it/s]
 74%|███████▍  | 23/31 [00:02<00:00,  8.39it/s]
 77%|███████▋  | 24/31 [00:02<00:00,  8.34it/s]
 81%|████████  | 25/31 [00:02<00:00,  8.34it/s]
 84%|████████▍ | 26/31 [00:02<00:00,  8.34it/s]
 87%|████████▋ | 27/31 [00:03<00:00,  8.33it/s]
 90%|█████████ | 28/31 [00:03<00:00,  8.32it/s]
 94%|█████████▎| 29/31 [00:03<00:00,  8.29it/s]
 97%|█████████▋| 30/31 [00:03<00:00,  8.35it/s]
100%|██████████| 31/31 [00:03<00:00,  8.41it/s]
100%|██████████| 31/31 [00:03<00:00,  8.60it/s]
wandb:                                                                                
wandb: 
wandb: Run history:
wandb:             dataset_test_size ▁
wandb:            dataset_train_size ▁
wandb:              dataset_val_size ▁
wandb:                     eval/loss ▁
wandb:                  eval/runtime ▁
wandb:       eval/samples_per_second ▁
wandb:         eval/steps_per_second ▁
wandb:                     eval_loss ▁
wandb:                  eval_runtime ▁
wandb:       eval_samples_per_second ▁
wandb:         eval_steps_per_second ▁
wandb:                   final_epoch ▁
wandb:               final_eval_loss ▁
wandb:            final_eval_runtime ▁
wandb: final_eval_samples_per_second ▁
wandb:   final_eval_steps_per_second ▁
wandb:              model_parameters ▁
wandb:              n_router_weights ▁
wandb:                     n_weights ▁
wandb:                   train/epoch ▁▁▁▁▂▂▂▂▂▃▃▃▃▃▃▄▄▄▄▅▅▅▅▅▆▆▆▆▆▆▇▇▇▇▇█████
wandb:             train/global_step ▁▁▁▁▂▂▂▂▂▂▃▃▃▃▃▄▄▄▄▄▅▅▅▅▅▆▆▆▆▆▆▇▇▇▇█████
wandb:               train/grad_norm █▃▃▃▂▂▂▂▂▂▃▂▂▂▂▂▂▂▂▂▁▂▂▂▁▂▂▂▂▁▁▁▁▁▁▂▁▁▁▁
wandb:           train/learning_rate ████▇▇▇▇▇▇▆▆▆▆▆▅▅▅▅▅▅▄▄▄▄▄▄▃▃▃▃▂▂▂▂▂▂▁▁▁
wandb:                    train/loss ██▇▇▆▅▅▅▅▄▄▄▄▄▃▃▃▃▂▂▂▂▂▂▂▂▂▂▂▁▁▁▁▁▁▁▁▁▁▁
wandb:          trainable_parameters ▁
wandb:          trainable_percentage ▁
wandb: 
wandb: Run summary:
wandb:             dataset_test_size 288
wandb:            dataset_train_size 2000
wandb:              dataset_val_size 123
wandb:                     eval/loss 0.27501
wandb:                  eval/runtime 3.7069
wandb:       eval/samples_per_second 33.182
wandb:         eval/steps_per_second 8.363
wandb:                     eval_loss 0.27501
wandb:                  eval_runtime 3.7069
wandb:       eval_samples_per_second 33.182
wandb:         eval_steps_per_second 8.363
wandb:                   final_epoch 10
wandb:               final_eval_loss 0.27501
wandb:            final_eval_runtime 3.7069
wandb: final_eval_samples_per_second 33.182
wandb:   final_eval_steps_per_second 8.363
wandb:              model_parameters 7356420096
wandb:              n_router_weights 256
wandb:                     n_weights 771
wandb:                    total_flos 3.985657945873449e+17
wandb:                   train/epoch 10
wandb:             train/global_step 1250
wandb:               train/grad_norm 0.4345
wandb:           train/learning_rate 0
wandb:                    train/loss 0.0712
wandb:                    train_loss 0.40426
wandb:                 train_runtime 1846.3522
wandb:      train_samples_per_second 10.832
wandb:        train_steps_per_second 0.677
wandb:          trainable_parameters 1918238720
wandb:          trainable_percentage 26.07571
wandb: 
wandb: 🚀 View run mistral-economic-moe at: https://wandb.ai/sarath-chandar/mistral-moe-training/runs/2wjetsf6
wandb: ⭐️ View project at: https://wandb.ai/sarath-chandar/mistral-moe-training
wandb: Synced 5 W&B file(s), 0 media file(s), 0 artifact file(s) and 0 other file(s)
wandb: Find logs at: ./wandb/run-20250707_182101-2wjetsf6/logs
[rank1]:[E707 19:09:01.930154231 ProcessGroupNCCL.cpp:1746] [PG ID 0 PG GUID 0(default_pg) Rank 1] ProcessGroupNCCL's watchdog got stuck for 480 seconds without making progress in monitoring enqueued collectives. This typically indicates a NCCL/CUDA API (e.g., CudaEventDestroy) hang blocking the watchdog, and could be triggered by another thread holding the GIL inside a CUDA api (for example, CudaEventDestroy), or other deadlock-prone behaviors.If you suspect the watchdog is not actually stuck and a longer timeout would help, you can either increase the timeout (TORCH_NCCL_HEARTBEAT_TIMEOUT_SEC) to a larger value or disable the heartbeat monitor (TORCH_NCCL_ENABLE_MONITORING=0).If either of aforementioned helps, feel free to file an issue to PyTorch about the short timeout or false positive abort; otherwise, please attempt to debug the hang. 
[rank1]:[E707 19:09:01.934894776 ProcessGroupNCCL.cpp:1536] [PG ID 0 PG GUID 0(default_pg) Rank 1] ProcessGroupNCCL preparing to dump debug info. Include stack trace: 1
[rank2]:[E707 19:09:02.576186430 ProcessGroupNCCL.cpp:1746] [PG ID 0 PG GUID 0(default_pg) Rank 2] ProcessGroupNCCL's watchdog got stuck for 480 seconds without making progress in monitoring enqueued collectives. This typically indicates a NCCL/CUDA API (e.g., CudaEventDestroy) hang blocking the watchdog, and could be triggered by another thread holding the GIL inside a CUDA api (for example, CudaEventDestroy), or other deadlock-prone behaviors.If you suspect the watchdog is not actually stuck and a longer timeout would help, you can either increase the timeout (TORCH_NCCL_HEARTBEAT_TIMEOUT_SEC) to a larger value or disable the heartbeat monitor (TORCH_NCCL_ENABLE_MONITORING=0).If either of aforementioned helps, feel free to file an issue to PyTorch about the short timeout or false positive abort; otherwise, please attempt to debug the hang. 
[rank3]:[E707 19:09:02.576185979 ProcessGroupNCCL.cpp:1746] [PG ID 0 PG GUID 0(default_pg) Rank 3] ProcessGroupNCCL's watchdog got stuck for 480 seconds without making progress in monitoring enqueued collectives. This typically indicates a NCCL/CUDA API (e.g., CudaEventDestroy) hang blocking the watchdog, and could be triggered by another thread holding the GIL inside a CUDA api (for example, CudaEventDestroy), or other deadlock-prone behaviors.If you suspect the watchdog is not actually stuck and a longer timeout would help, you can either increase the timeout (TORCH_NCCL_HEARTBEAT_TIMEOUT_SEC) to a larger value or disable the heartbeat monitor (TORCH_NCCL_ENABLE_MONITORING=0).If either of aforementioned helps, feel free to file an issue to PyTorch about the short timeout or false positive abort; otherwise, please attempt to debug the hang. 
[rank2]:[E707 19:09:02.581504544 ProcessGroupNCCL.cpp:1536] [PG ID 0 PG GUID 0(default_pg) Rank 2] ProcessGroupNCCL preparing to dump debug info. Include stack trace: 1
[rank3]:[E707 19:09:02.586042288 ProcessGroupNCCL.cpp:1536] [PG ID 0 PG GUID 0(default_pg) Rank 3] ProcessGroupNCCL preparing to dump debug info. Include stack trace: 1
[rank1]:[F707 19:17:01.944074714 ProcessGroupNCCL.cpp:1557] [PG ID 0 PG GUID 0(default_pg) Rank 1] [PG ID 0 PG GUID 0(default_pg) Rank 1] Terminating the process after attempting to dump debug info, due to ProcessGroupNCCL watchdog hang.
W0707 19:17:02.055000 3813190 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 3813298 closing signal SIGTERM
W0707 19:17:02.060000 3813190 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 3813300 closing signal SIGTERM
W0707 19:17:02.066000 3813190 torch/distributed/elastic/multiprocessing/api.py:900] Sending process 3813301 closing signal SIGTERM
E0707 19:17:03.287000 3813190 torch/distributed/elastic/multiprocessing/api.py:874] failed (exitcode: -6) local_rank: 1 (pid: 3813299) of binary: /home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/bin/python
Traceback (most recent call last):
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/bin/accelerate", line 8, in <module>
    sys.exit(main())
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/accelerate/commands/accelerate_cli.py", line 50, in main
    args.func(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/accelerate/commands/launch.py", line 1183, in launch_command
    deepspeed_launcher(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/accelerate/commands/launch.py", line 868, in deepspeed_launcher
    distrib_run.run(args)
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/run.py", line 883, in run
    elastic_launch(
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 139, in __call__
    return launch_agent(self._config, self._entrypoint, list(args))
  File "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 270, in launch_agent
    raise ChildFailedError(
torch.distributed.elastic.multiprocessing.errors.ChildFailedError: 
========================================================
exmp_1.py FAILED
--------------------------------------------------------
Failures:
  <NO_OTHER_FAILURES>
--------------------------------------------------------
Root Cause (first observed failure):
[0]:
  time      : 2025-07-07_19:17:02
  host      : cn-g022.server.mila.quebec
  rank      : 1 (local_rank: 1)
  exitcode  : -6 (pid: 3813299)
  error_file: <N/A>
  traceback : Signal 6 (SIGABRT) received by PID 3813299
========================================================
